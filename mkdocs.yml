site_name: hippocampus-cloud
repo_url: https://github.com/ankaisen/hippocampus-cloud
site_url: https://ankaisen.github.io/hippocampus-cloud
site_description: This is a template repository for Python projects that use uv for their dependency management.
site_author: <PERSON> <PERSON><PERSON>
edit_uri: edit/main/docs/
repo_name: ankaisen/hippocampus-cloud
copyright: Maintained by <a href="https://ankaisen.com">an<PERSON><PERSON></a>.

nav:
  - Home: index.md
  - Modules: modules.md
plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          paths: ["app"]
theme:
  name: material
  feature:
    tabs: true
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: white
      accent: deep orange
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: black
      accent: deep orange
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  icon:
    repo: fontawesome/brands/github

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/ankaisen/hippocampus-cloud
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/hippocampus-cloud

markdown_extensions:
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
