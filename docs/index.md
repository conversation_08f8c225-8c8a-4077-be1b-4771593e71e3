# hippocampus-cloud

[![Release](https://img.shields.io/github/v/release/ankaisen/hippocampus-cloud)](https://img.shields.io/github/v/release/ankaisen/hippocampus-cloud)
[![Build status](https://img.shields.io/github/actions/workflow/status/ankaisen/hippocampus-cloud/main.yml?branch=main)](https://github.com/ankaisen/hippocampus-cloud/actions/workflows/main.yml?query=branch%3Amain)
[![Commit activity](https://img.shields.io/github/commit-activity/m/ankaisen/hippocampus-cloud)](https://img.shields.io/github/commit-activity/m/ankaisen/hippocampus-cloud)
[![License](https://img.shields.io/github/license/ankaisen/hippocampus-cloud)](https://img.shields.io/github/license/ankaisen/hippocampus-cloud)

This is a template repository for Python projects that use uv for their dependency management.
