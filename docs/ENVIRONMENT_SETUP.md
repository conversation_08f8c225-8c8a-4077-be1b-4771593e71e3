# Environment Setup

This project uses environment-specific configuration files to manage different settings for development, testing, and production environments.

## Environment Files

The project includes three environment configuration files:

- `.env.dev` - Development environment settings
- `.env.test` - Testing environment settings
- `.env.prod` - Production environment settings

## Environment Selection

The environment is controlled by the `ENV` environment variable:

```bash
# Development (default)
ENV=development

# Testing
ENV=test  # or ENV=testing

# Production
ENV=production
```

## Database Configuration

Each environment uses a different database:

- **Development**: `cloud_dev`
- **Testing**: `cloud_test`
- **Production**: `cloud_prod`

## Usage Examples

### Running the Application

```bash
# Development mode (default)
python app/main.py

# Testing mode
ENV=test python app/main.py

# Production mode
ENV=production python app/main.py
```

### Database Migrations

```bash
# Migrate development database (default)
alembic upgrade head

# Migrate test database
ENV=test alembic upgrade head

# Migrate production database
ENV=production alembic upgrade head

# Migrate both dev and test databases
./scripts/migrate_all.sh
```

### Running Tests

```bash
# Ensure test database is up to date
ENV=test alembic upgrade head

# Run tests
ENV=test pytest
```

## Local Development Setup

1. Copy the appropriate environment file for local customization:
   ```bash
   cp .env.dev .env.dev.local
   ```

2. Modify `.env.dev.local` with your local settings (this file is gitignored)

3. The config loader will automatically pick up your local overrides

## Production Deployment

1. Update `.env.prod` with your production database credentials and settings
2. Set `ENV=production` in your deployment environment
3. Run migrations: `ENV=production alembic upgrade head`
4. Start the application: `ENV=production python app/main.py`

## Environment Variables Priority

The configuration system follows this priority order:
1. Environment variables
2. Environment-specific `.env` files (`.env.dev`, `.env.test`, `.env.prod`)
3. Default values in the configuration classes
