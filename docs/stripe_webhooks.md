# Stripe Webhooks Implementation

This document describes the Stripe webhook implementation for handling payment events in the Hippocampus Cloud application.

## Overview

The webhook system provides secure, reliable processing of Stripe events with the following features:

- **Signature Verification**: All webhooks are verified using <PERSON><PERSON>'s recommended HMAC-SHA256 signature verification
- **Idempotency**: Duplicate events are detected and handled gracefully
- **Audit Logging**: All webhook events are stored for audit purposes
- **Error Handling**: Comprehensive error handling with appropriate HTTP status codes
- **Event Processing**: Specific handlers for different event types

## Supported Events

Currently supported Stripe webhook events:

### invoice.paid
- **Purpose**: Process successful invoice payments
- **Actions**:
  - Updates subscription status in database
  - Logs payment for audit purposes
  - Links payment to customer and organization records
  - Updates latest invoice reference

## Configuration

### Environment Variables

Add the following environment variable to your configuration:

```bash
STRIPE_WEBHOOK_ENDPOINT_SECRET=whsec_your_webhook_endpoint_secret_here
```

This secret is provided by <PERSON><PERSON> when you create a webhook endpoint in your Stripe dashboard.

### Stripe Dashboard Setup

1. Go to your Stripe Dashboard → Developers → Webhooks
2. Click "Add endpoint"
3. Set the endpoint URL to: `https://your-domain.com/api/v1/webhooks/stripe`
4. Select the events you want to receive:
   - `invoice.paid`
5. Copy the webhook signing secret and set it as `STRIPE_WEBHOOK_ENDPOINT_SECRET`

## API Endpoints

### POST /api/v1/webhooks/stripe
Main webhook endpoint for receiving Stripe events.

**Headers Required:**
- `Stripe-Signature`: Stripe's signature header for verification
- `Content-Type`: application/json

**Response:**
```json
{
  "event_id": "evt_1234567890",
  "event_type": "invoice.paid",
  "processed": true,
  "message": "Event processed successfully",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200`: Event processed successfully
- `400`: Invalid signature or malformed request
- `500`: Internal processing error

### GET /api/v1/webhooks/stripe/health
Health check endpoint for monitoring webhook service availability.

### POST /api/v1/webhooks/stripe/test
Test endpoint for development (bypasses signature verification).

## Database Schema

### StripeWebhookEvent Table

Stores all webhook events for audit and idempotency:

```sql
CREATE TABLE stripe_webhook_events (
    id VARCHAR PRIMARY KEY,
    stripe_event_id VARCHAR UNIQUE NOT NULL,
    event_type VARCHAR NOT NULL,
    stripe_created TIMESTAMP NOT NULL,
    livemode BOOLEAN DEFAULT FALSE,
    status VARCHAR NOT NULL,
    processed_at TIMESTAMP,
    attempts INTEGER DEFAULT 0,
    last_attempt_at TIMESTAMP,
    event_data JSONB,
    processing_result JSONB,
    error_message TEXT,
    customer_id VARCHAR,
    subscription_id VARCHAR,
    invoice_id VARCHAR,
    identity_id VARCHAR REFERENCES identities(id),
    organization_id VARCHAR REFERENCES organizations(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Security Features

### Signature Verification
- Uses HMAC-SHA256 with your webhook endpoint secret
- Verifies timestamp to prevent replay attacks (5-minute tolerance)
- Constant-time comparison to prevent timing attacks

### Error Handling
- All errors are logged with appropriate detail levels
- Sensitive information is not exposed in error responses
- Failed events are marked in database for investigation

## Monitoring and Debugging

### Logging
All webhook processing is logged with structured information:
- Event ID and type
- Processing status and duration
- Error details (if any)
- Database operations performed

### Health Checks
Use the health endpoint to monitor webhook service availability:
```bash
curl https://your-domain.com/api/v1/webhooks/stripe/health
```

### Testing
For development, use the test endpoint to verify webhook reception:
```bash
curl -X POST https://your-domain.com/api/v1/webhooks/stripe/test \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

## Error Scenarios

### Common Issues and Solutions

1. **Signature Verification Failed**
   - Check that `STRIPE_WEBHOOK_ENDPOINT_SECRET` is correctly set
   - Verify the webhook endpoint URL in Stripe dashboard
   - Ensure the request is coming from Stripe

2. **Duplicate Events**
   - Events are automatically detected and marked as duplicates
   - No action needed - this is normal Stripe behavior

3. **Processing Failures**
   - Check application logs for detailed error information
   - Failed events are stored in database for investigation
   - Events can be manually reprocessed if needed

## Development

### Running Tests
```bash
pytest tests/test_stripe_webhooks.py -v
```

### Local Development
1. Use ngrok or similar tool to expose local server
2. Set up webhook endpoint in Stripe test mode
3. Use test endpoint for initial verification
4. Test with actual Stripe test events

## Architecture

### Components

1. **WebhookVerificationService**: Handles signature verification
2. **StripeWebhookProcessor**: Processes different event types
3. **StripeWebhookEventCrud**: Database operations for webhook events
4. **Webhook Endpoint**: FastAPI route handler

### Flow

1. Webhook received → Signature verification
2. Event stored in database → Mark as processing
3. Event processed based on type → Update database
4. Mark as completed/failed → Return response

This implementation ensures reliable, secure, and auditable processing of Stripe webhook events.
