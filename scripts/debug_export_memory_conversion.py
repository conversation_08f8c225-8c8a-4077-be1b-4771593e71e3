#!/usr/bin/env python3
"""
Database export script for debug data synchronization.
Exports specified tables to CSV files for sharing between development environments.
"""

import asyncio
import csv
import json
from datetime import datetime
from pathlib import Path
from typing import Any

from loguru import logger
from sqlalchemy import text

from app.services.db.session import SessionFactory
from config.loader import SettingsFactory

# Tables to export (matching your configuration)
TABLES_TO_EXPORT = [
    "memories",
    "conversations",
    "memory_categories",
]

# Directory to save exported files
EXPORT_DIR = Path(__file__).parent / "exported_data"


def serialize_value(value: Any) -> str:
    """Serialize complex data types for CSV export."""
    if value is None:
        return ""
    elif isinstance(value, (dict, list)):
        return json.dumps(value, ensure_ascii=False)
    elif isinstance(value, datetime):
        return value.isoformat()
    elif isinstance(value, (int, float, str, bool)):
        return str(value)
    else:
        # For other types (like UUID), convert to string
        return str(value)


async def export_table(table_name: str, output_dir: Path) -> None:
    """Export a single table to CSV file."""
    logger.info(f"Starting export of table: {table_name}")

    session = SessionFactory.make_local_session()
    try:
        # Get all data from the table
        result = await session.execute(text(f"SELECT * FROM {table_name}"))
        rows = result.fetchall()

        if not rows:
            logger.warning(f"No data found in table {table_name}")
            return

        # Get column names
        column_names = list(result.keys())

        # Create CSV file
        csv_file = output_dir / f"{table_name}.csv"

        with open(csv_file, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(column_names)

            # Write data rows
            for row in rows:
                serialized_row = [serialize_value(value) for value in row]
                writer.writerow(serialized_row)

        logger.info(f"Exported {len(rows)} rows from {table_name} to {csv_file}")

    except Exception as e:
        logger.error(f"Error exporting table {table_name}: {e}")
        raise
    finally:
        await session.close()


async def export_all_tables() -> None:
    """Export all specified tables."""
    # Create export directory
    EXPORT_DIR.mkdir(exist_ok=True)

    logger.info(f"Starting database export to {EXPORT_DIR}")
    logger.info(f"Tables to export: {TABLES_TO_EXPORT}")

    # Initialize database connection
    settings = SettingsFactory.settings()
    logger.info(f"Database: {settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}")

    # Export each table
    for table_name in TABLES_TO_EXPORT:
        try:
            await export_table(table_name, EXPORT_DIR)
        except Exception as e:
            logger.error(f"Failed to export table {table_name}: {e}")
            continue

    # Create metadata file
    metadata = {
        "export_timestamp": datetime.now().isoformat(),
        "database_name": settings.DATABASE_NAME,
        "tables_exported": TABLES_TO_EXPORT,
        "export_directory": str(EXPORT_DIR),
    }

    metadata_file = EXPORT_DIR / "export_metadata.json"
    with open(metadata_file, "w", encoding="utf-8") as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)

    logger.info("Export completed successfully!")
    logger.info("Exported files:")
    for table_name in TABLES_TO_EXPORT:
        csv_file = EXPORT_DIR / f"{table_name}.csv"
        if csv_file.exists():
            logger.info(f"  - {csv_file}")
    logger.info(f"  - {metadata_file}")


def main():
    """Main entry point."""
    try:
        asyncio.run(export_all_tables())
    except KeyboardInterrupt:
        logger.info("Export cancelled by user")
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise


if __name__ == "__main__":
    main()
