#!/bin/bash

# Script to clear all Celery tasks and queues
# This will remove all pending, active, and scheduled tasks

echo "🧹 Clearing Celery tasks and queues..."

# Method 1: Using Celery purge command (clears all queues)
echo "📋 Purging all Celery queues..."
celery -A app.services.task_queue.celery_app purge -f

# Method 2: Clear Redis databases directly (more thorough)
echo "🗑️  Clearing Redis databases..."

# Clear broker database (db 0) - where pending tasks are stored
echo "Clearing Redis database 0 (broker)..."
redis-cli -n 0 FLUSHDB

# Clear result backend database (db 1) - where task results are stored
echo "Clearing Redis database 1 (result backend)..."
redis-cli -n 1 FLUSHDB

echo "✅ All Celery tasks and queues have been cleared!"
echo ""
echo "💡 You can now restart your Celery worker:"
echo "   celery -A app.services.task_queue.celery_app worker --loglevel=info"
