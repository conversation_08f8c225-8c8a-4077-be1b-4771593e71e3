import asyncio
import sys
import traceback
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import all models to ensure SQLAlchemy can resolve relationships

from app.modules.memory.crud import MemoryCategoryRepo as memory_category_crud
from app.modules.memory.schema import IMemoryCategoryCreate
from app.services.db.session import SessionFactory

user_id = "test_user_123"
agent_id = "test_agent_456"


async def main():
    async with SessionFactory.make_local_session() as session:
        await insert_memory_categories(session=session)


async def insert_memory_categories(session):
    """Insert two memory categories into the database."""

    # First memory category - event
    category_1 = {
        "user_id": user_id,
        "agent_id": agent_id,
        "class": "basic",
        "name": "event",
    }

    # Second memory category - profile
    category_2 = {
        "user_id": user_id,
        "agent_id": agent_id,
        "class": "basic",
        "name": "profile",
    }

    category_3 = {
        "user_id": user_id,
        "agent_id": agent_id,
        "class": "cluster",
        "name": "promotion",
    }

    try:
        # Create first memory category
        memory_category_1 = await memory_category_crud.create(
            new=IMemoryCategoryCreate(**category_1), db_session=session
        )
        await session.commit()
        print(f"Successfully created memory category: {memory_category_1.name}")

        # Create second memory category
        memory_category_2 = await memory_category_crud.create(
            new=IMemoryCategoryCreate(**category_2), db_session=session
        )
        await session.commit()
        print(f"Successfully created memory category: {memory_category_2.name}")

        # Create second memory category
        memory_category_3 = await memory_category_crud.create(
            new=IMemoryCategoryCreate(**category_3), db_session=session
        )
        await session.commit()
        print(f"Successfully created memory category: {memory_category_3.name}")

        print("Both memory categories have been successfully inserted!")

    except Exception as e:
        print(f"Error inserting memory categories: {e!r}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
