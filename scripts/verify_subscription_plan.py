#!/usr/bin/env python3
"""
<PERSON>ript to verify the subscription plan was inserted correctly.
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from loguru import logger

from app.modules import cruds
from app.services.db.session import SessionFactory


async def verify_subscription_plan():
    """Verify the MEMU-PRO-v20250731 subscription plan exists."""

    async with SessionFactory.make_local_session() as session:
        try:
            # Get the pro plan
            pro_plan = await cruds.subscription_plan.get_by_code(code="MEMU-PRO-v20250731", session=session)

            if pro_plan:
                logger.success(f"✅ Found subscription plan: {pro_plan.name}")
                logger.info("Plan details:")
                logger.info(f"  - ID: {pro_plan.id}")
                logger.info(f"  - Name: {pro_plan.name}")
                logger.info(f"  - Code: {pro_plan.code}")
                logger.info(f"  - Token Quota: {pro_plan.token_quota:,}")
                logger.info(f"  - Description: {pro_plan.description}")
                logger.info(f"  - Extra Features: {pro_plan.extra_features}")
                logger.info(f"  - Is Active: {pro_plan.is_active}")
                logger.info(f"  - Created At: {pro_plan.created_at}")
                logger.info(f"  - Updated At: {pro_plan.updated_at}")
                return pro_plan
            else:
                logger.error("❌ Subscription plan with code 'MEMU-PRO-v20250731' not found")
                return None

        except Exception as e:
            logger.error(f"Error verifying subscription plan: {e}")
            raise


async def main():
    """Main function to run the verification."""
    logger.info("Verifying subscription plan insertion...")

    try:
        plan = await verify_subscription_plan()
        if plan:
            logger.success("✅ Verification completed successfully!")
        else:
            logger.error("❌ Verification failed!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Verification failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
