#!/usr/bin/env python3
"""
Database import script for debug data synchronization.
Imports CSV files to specified tables for sharing between development environments.
"""

import asyncio
import csv
import json
from datetime import datetime
from pathlib import Path
from typing import Any

from loguru import logger
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from app.services.db.session import SessionFactory
from config.loader import SettingsFactory

# Tables to import (matching your configuration)
TABLES_TO_IMPORT = [
    "memory_categories",  # Import this first (may be referenced by memories)
    "conversations",
    "memories",  # Import this last (has foreign keys)
]

# Key overwrites - can be customized via command line
KEY_OVERWRITES = {
    "api_key_id": "30h07a3I4qA5d9l4m8L7C38Y3Ea",
    "project_id": "30fR0HGOoHA0hEPtWYOrITvbAHQ",
}

# Directory containing exported files
IMPORT_DIR = Path(__file__).parent / "exported_data"


def apply_key_overwrites(row: dict[str, Any], key_overwrites: dict[str, Any]) -> dict[str, Any]:
    """Apply key overwrites to a row, only for keys that exist in the row."""
    overwritten_row = row.copy()

    for key, new_value in key_overwrites.items():
        if key in overwritten_row:
            old_value = overwritten_row[key]
            overwritten_row[key] = new_value
            logger.debug(f"Overwriting {key}: {old_value} -> {new_value}")

    return overwritten_row


def deserialize_value(value: str, column_name: str) -> Any:
    """Deserialize values from CSV back to appropriate types."""
    if value == "":
        return None

    # Handle JSONB columns - keep as JSON strings for PostgreSQL JSONB columns
    if column_name in ["content", "links", "links_before", "links_after"]:
        try:
            # Validate JSON format but return as string for JSONB columns
            json.loads(value)  # Just validate, don't use result
            return value  # Return original JSON string
        except (json.JSONDecodeError, TypeError):
            return value

    # Handle embedding column (Vector type) - deserialize to list for Vector columns
    if column_name == "embedding":
        try:
            return json.loads(value)  # Vector type expects list of floats
        except (json.JSONDecodeError, TypeError):
            return value

    # Handle datetime columns
    if (
        "at" in column_name
        or "date" in column_name
        or column_name in ["created_at", "updated_at", "session_date", "last_message_at", "happened_at", "timestamp"]
    ):
        try:
            if value and value != "None":
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
        except ValueError:
            pass

    # Handle boolean columns
    if value.lower() in ["true", "false"]:
        return value.lower() == "true"

    # Handle numeric columns
    try:
        if "." in value:
            return float(value)
        else:
            return int(value)
    except ValueError:
        pass

    # Return as string for everything else
    return value


async def clear_table(table_name: str, session) -> None:
    """Clear all data from a table."""
    try:
        # Disable foreign key constraints temporarily
        # await session.execute(text("SET session_replication_role = replica;"))

        # Delete all rows
        await session.execute(text(f"DELETE FROM {table_name}"))

        # Re-enable foreign key constraints
        # await session.execute(text("SET session_replication_role = DEFAULT;"))

        logger.info(f"Cleared all data from table: {table_name}")

    except Exception as e:
        logger.error(f"Error clearing table {table_name}: {e}")
        raise


async def import_table(table_name: str, import_dir: Path, clear_existing: bool = True) -> None:
    """Import a single table from CSV file."""
    csv_file = import_dir / f"{table_name}.csv"

    if not csv_file.exists():
        logger.warning(f"CSV file not found: {csv_file}")
        return

    logger.info(f"Starting import of table: {table_name}")

    session = SessionFactory.make_local_session()
    try:
        # Clear existing data if requested
        if clear_existing:
            await clear_table(table_name, session)

        # Read CSV file
        with open(csv_file, encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            rows_imported = 0
            batch_size = 100
            batch = []

            for row in reader:
                # Deserialize values
                processed_row = {}
                for column_name, value in row.items():
                    processed_row[column_name] = deserialize_value(value, column_name)

                processed_row = apply_key_overwrites(processed_row, KEY_OVERWRITES)

                batch.append(processed_row)

                # Process batch
                if len(batch) >= batch_size:
                    await import_batch(table_name, batch, session)
                    rows_imported += len(batch)
                    batch = []

            # Process remaining rows
            if batch:
                await import_batch(table_name, batch, session)
                rows_imported += len(batch)

        await session.commit()
        logger.info(f"Successfully imported {rows_imported} rows into {table_name}")

    except Exception as e:
        await session.rollback()
        logger.error(f"Error importing table {table_name}: {e}")
        raise
    finally:
        await session.close()


async def import_batch(table_name: str, batch: list[dict], session) -> None:
    """Import a batch of rows into a table."""
    if not batch:
        return

    try:
        # Build INSERT statement
        columns = list(batch[0].keys())
        placeholders = ", ".join([f":{col}" for col in columns])
        columns_str = ", ".join(columns)

        insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

        # Execute batch insert
        await session.execute(text(insert_sql), batch)

    except IntegrityError as e:
        # Handle foreign key or unique constraint violations
        logger.warning(f"Integrity error in {table_name}: {e}")
        # Try inserting rows one by one to identify problematic rows
        for row in batch:
            try:
                await session.execute(text(insert_sql), [row])
            except IntegrityError as ie:
                logger.warning(
                    f"Skipping row in {table_name} due to constraint violation: {row.get('id', 'unknown')} - {ie}"
                )
                continue
    except Exception as e:
        logger.error(f"Error importing batch to {table_name}: {e}")
        raise


async def import_all_tables(clear_existing: bool = True) -> None:
    """Import all specified tables."""
    if not IMPORT_DIR.exists():
        logger.error(f"Import directory not found: {IMPORT_DIR}")
        return

    # Check for metadata file
    metadata_file = IMPORT_DIR / "export_metadata.json"
    if metadata_file.exists():
        with open(metadata_file, encoding="utf-8") as f:
            metadata = json.load(f)
            logger.info("Import metadata:")
            logger.info(f"  Export timestamp: {metadata.get('export_timestamp', 'unknown')}")
            logger.info(f"  Source database: {metadata.get('database_name', 'unknown')}")

    logger.info(f"Starting database import from {IMPORT_DIR}")
    logger.info(f"Tables to import: {TABLES_TO_IMPORT}")
    logger.info(f"Clear existing data: {clear_existing}")

    # Initialize database connection
    settings = SettingsFactory.settings()
    logger.info(f"Target database: {settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}")

    # Create a session for global foreign key constraint management
    global_session = SessionFactory.make_local_session()
    try:
        # Disable foreign key constraints for the entire import process
        # For PostgreSQL
        await global_session.execute(text("SET session_replication_role = replica;"))
        # For MySQL (uncomment if using MySQL instead)
        # await global_session.execute(text("SET FOREIGN_KEY_CHECKS = 0;"))
        await global_session.commit()
        logger.info("Disabled foreign key constraints for import")

        # Import each table in order
        for table_name in TABLES_TO_IMPORT:
            try:
                await import_table(table_name, IMPORT_DIR, clear_existing)
            except Exception as e:
                logger.error(f"Failed to import table {table_name}: {e}")
                continue

    finally:
        # Re-enable foreign key constraints
        # For PostgreSQL
        await global_session.execute(text("SET session_replication_role = DEFAULT;"))
        # For MySQL (uncomment if using MySQL instead)
        # await global_session.execute(text("SET FOREIGN_KEY_CHECKS = 1;"))
        await global_session.commit()
        await global_session.close()
        logger.info("Re-enabled foreign key constraints")

    logger.info("Import completed!")


def parse_key_overwrites(overwrites_str: str = None) -> dict[str, Any]:
    """Parse key overwrites from command line string or use defaults."""
    if overwrites_str:
        try:
            # Parse JSON string
            return json.loads(overwrites_str)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format for key overwrites: {e}")
            logger.info("Using default key overwrites instead")

    # Return default overwrites
    return KEY_OVERWRITES.copy()


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="Import database tables from CSV files")
    parser.add_argument("--clear-existing", action="store_true", help="Clear existing data before import")

    args = parser.parse_args()

    try:
        asyncio.run(import_all_tables(clear_existing=args.clear_existing))
    except KeyboardInterrupt:
        logger.info("Import cancelled by user")
    except Exception as e:
        logger.error(f"Import failed: {e}")
        raise


if __name__ == "__main__":
    main()
