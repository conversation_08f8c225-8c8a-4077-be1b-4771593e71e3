#!/usr/bin/env python3
"""
Script to insert mock data for MEMU-PRO-v20250731 subscription plan.
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from loguru import logger

from app.modules import cruds
from app.modules.subscription_plan.schema import ISubscriptionPlanCreate
from app.services.db.session import SessionFactory


async def insert_pro_subscription_plan():
    """Insert the MEMU-PRO-v20250731 subscription plan."""

    # Define the pro subscription plan data
    pro_plan_data = ISubscriptionPlanCreate(
        name="Pro",
        code="MEMU-PRO-v20250731",
        description="Pro plan with enhanced features and higher token quota",
        token_quota=50000,  # 50k tokens for pro plan
        extra_features={
            "priority_support": True,
            "advanced_analytics": True,
            "custom_integrations": True,
            "api_rate_limit_multiplier": 5,
            "max_projects": 50,
            "max_team_members": 25,
            "data_retention_days": 365,
            "export_capabilities": True,
        },
        is_active=True,
    )

    async with SessionFactory.make_local_session() as session:
        try:
            # Check if the plan already exists
            existing_plan = await cruds.subscription_plan.get_by_code(code=pro_plan_data.code, session=session)

            if existing_plan:
                logger.info(f"Subscription plan with code '{pro_plan_data.code}' already exists")
                logger.info(f"Existing plan: {existing_plan.name} (ID: {existing_plan.id})")
                return existing_plan
            else:
                # Create the new subscription plan
                logger.info(f"Creating new subscription plan: {pro_plan_data.name}")
                new_plan = await cruds.subscription_plan.create(new=pro_plan_data, db_session=session)

                await session.commit()
                logger.success(f"Successfully created subscription plan: {new_plan.name}")
                logger.info("Plan details:")
                logger.info(f"  - ID: {new_plan.id}")
                logger.info(f"  - Name: {new_plan.name}")
                logger.info(f"  - Code: {new_plan.code}")
                logger.info(f"  - Token Quota: {new_plan.token_quota:,}")
                logger.info(f"  - Description: {new_plan.description}")
                logger.info(f"  - Extra Features: {new_plan.extra_features}")
                logger.info(f"  - Is Active: {new_plan.is_active}")

                return new_plan

        except Exception as e:
            await session.rollback()
            logger.error(f"Error creating subscription plan: {e}")
            raise


async def main():
    """Main function to run the script."""
    logger.info("Starting subscription plan insertion script...")

    try:
        await insert_pro_subscription_plan()
        logger.success("Script completed successfully!")

        # Clear and rebuild the subscription plan cache
        logger.info("Clearing and rebuilding subscription plan cache...")
        cruds.subscription_plan.clear_cache()

        async with SessionFactory.make_local_session() as session:
            await cruds.subscription_plan.build_cache(session=session)

        logger.success("Cache rebuilt successfully!")

    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
