import pytest
import pytest_asyncio
from sqlmodel import select

from app.modules import cruds
from app.modules.models import (
    Identity,
    Organization,
    OrganizationMember,
    PlatformMember,
    Project,
    ProjectMember,
)
from app.modules.permission.usecases import enforce_permission
from app.modules.shared.base_policy import Action
from app.services.db.session import AsyncSession


@pytest_asyncio.fixture
async def data(session: AsyncSession):
    """Setup predefined test data"""
    alice = Identity(slug="alice", name="Alice Admin", email="<EMAIL>")
    bob = Identity(slug="bob", name="Bob User", email="<EMAIL>")
    charlie = Identity(slug="charlie", name="Charlie Developer", email="<EMAIL>")
    session.add_all([alice, bob, charlie])
    await session.commit()

    test_org = Organization(id="test_org_id", slug="test-org", name="Test Organization", created_by=bob.id)
    session.add(test_org)
    await session.commit()

    test_project = Project(
        id="test_project_id",
        slug="test-project",
        name="Test Project",
        organization_id=test_org.id,
        created_by=bob.id,
    )
    session.add(test_project)
    await session.commit()

    platform_member = PlatformMember(identity=alice, is_active=True)
    session.add(platform_member)
    await session.commit()
    # platform_role = PlatformMemberRole(
    #     member=platform_member,
    #     role=cruds.role.platform_super_admin,
    # )
    # session.add(platform_role)
    # await session.commit()  # Commit platform role

    # Assign organization memberships
    org_memberships = [
        OrganizationMember(organization=test_org, identity=bob, role=cruds.role.org_owner, is_active=True),
        # OrganizationMember(
        #     organization=test_org,
        #     identity=bob,
        #     role=cruds.role.proj_owner,
        #     is_active=True
        # ),
    ]
    session.add_all(org_memberships)

    # Assign project membership
    project_membership = ProjectMember(project=test_project, identity=bob, role=cruds.role.proj_owner, is_active=True)
    session.add(project_membership)

    await session.commit()

    return {
        "identities": {"alice": alice, "bob": bob, "charlie": charlie},
        "organization": test_org,
        "project": test_project,
    }


class TestPermissionEnforcement:
    """Test permission enforcement logic"""

    @pytest.mark.asyncio
    async def test_data_setup(self, session: AsyncSession, data):
        """Ensure test data is set up correctly"""
        super_admin = data["identities"]["alice"]
        org = data["organization"]
        result = await session.execute(select(Identity))
        identities = result.scalars().all()
        assert len(identities) == 3
        # assert session.query(Role).count() == 6  # Assuming roles are created in setup
        # assert session.query(Organization).count() == 1
        # assert session.query(Project).count() == 1

        # def test_platform_super_admin_permissions(self, session: Session, test_data):
        #     """Test that platform super admin has all permissions"""
        #     alice = test_data['identities']['alice']

        #     # Should have platform permissions
        assert await enforce_permission(super_admin, Action.CREATE, "any_resource", org, session=session)

    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "organization", Action.DELETE)
    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "user", Action.UPDATE)

    #     # Should have organization permissions through platform role
    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "project", Action.CREATE)
    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "member", Action.DELETE)

    # def test_organization_admin_permissions(self, session: Session, test_data):
    #     """Test organization admin permissions within their domain"""
    #     bob = test_data['identities']['bob']
    #     org = test_data['organization']

    #     # Should have org admin permissions in their organization
    #     assert enforce_permission(session, bob, RoleDomain.ORGANIZATION, "organization", Action.READ, org.id)
    #     assert enforce_permission(session, bob, RoleDomain.ORGANIZATION, "organization", Action.UPDATE, org.id)
    #     assert enforce_permission(session, bob, RoleDomain.ORGANIZATION, "project", Action.CREATE, org.id)
    #     assert enforce_permission(session, bob, RoleDomain.ORGANIZATION, "member", Action.CREATE, org.id)

    #     # Should NOT have delete permissions (not in role permissions)
    #     assert not enforce_permission(session, bob, RoleDomain.ORGANIZATION, "organization", Action.DELETE, org.id)
    #     assert not enforce_permission(session, bob, RoleDomain.ORGANIZATION, "project", Action.DELETE, org.id)

    #     # Should NOT have permissions without domain_id
    #     assert not enforce_permission(session, bob, RoleDomain.ORGANIZATION, "organization", Action.READ)

    # def test_organization_viewer_permissions(self, session: Session, test_data):
    #     """Test organization viewer has only read permissions"""
    #     charlie = test_data['identities']['charlie']
    #     org = test_data['organization']

    #     # Should have read permissions only
    #     assert enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "organization", Action.READ, org.id)
    #     assert enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "project", Action.READ, org.id)
    #     assert enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "member", Action.READ, org.id)

    #     # Should NOT have write permissions
    #     assert not enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "organization", Action.UPDATE, org.id)
    #     assert not enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "project", Action.CREATE, org.id)
    #     assert not enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "member", Action.DELETE, org.id)

    # def test_project_admin_permissions(self, session: Session, test_data):
    #     """Test project admin permissions within their project"""
    #     charlie = test_data['identities']['charlie']
    #     project = test_data['project']

    #     # Should have project admin permissions in their project
    #     assert enforce_permission(session, charlie, RoleDomain.PROJECT, "project", Action.READ, project.id)
    #     assert enforce_permission(session, charlie, RoleDomain.PROJECT, "project", Action.UPDATE, project.id)
    #     assert enforce_permission(session, charlie, RoleDomain.PROJECT, "dataset", Action.CREATE, project.id)
    #     assert enforce_permission(session, charlie, RoleDomain.PROJECT, "dataset", Action.READ, project.id)
    #     assert enforce_permission(session, charlie, RoleDomain.PROJECT, "dataset", Action.UPDATE, project.id)

    #     # Should NOT have delete permissions (not in role permissions)
    #     assert not enforce_permission(session, charlie, RoleDomain.PROJECT, "project", Action.DELETE, project.id)
    #     assert not enforce_permission(session, charlie, RoleDomain.PROJECT, "dataset", Action.DELETE, project.id)

    # def test_no_permissions_without_role(self, session: Session, test_data):
    #     """Test that users without roles have no permissions"""
    #     alice = test_data['identities']['alice']
    #     org = test_data['organization']
    #     project = test_data['project']

    #     # Alice has no org/project roles, only platform role
    #     assert not enforce_permission(session, alice, RoleDomain.ORGANIZATION, "organization", Action.READ, org.id)
    #     assert not enforce_permission(session, alice, RoleDomain.PROJECT, "project", Action.READ, project.id)

    # def test_wildcard_permissions(self, session: Session, test_data):
    #     """Test that wildcard permissions work correctly"""
    #     alice = test_data['identities']['alice']

    #     # Super admin has wildcard permissions, should work for any resource
    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "unknown_resource", Action.CREATE)
    #     assert enforce_permission(session, alice, RoleDomain.PLATFORM, "another_resource", Action.DELETE)

    # def test_inactive_membership(self, session: Session, test_data):
    #     """Test that inactive memberships don't grant permissions"""
    #     bob = test_data['identities']['bob']
    #     org = test_data['organization']

    #     # Deactivate Bob's membership
    #     from sqlmodel import select
    #     statement = select(OrganizationMember).where(OrganizationMember.identity_id == bob.id)
    #     org_member = session.exec(statement).first()
    #     org_member.is_active = False
    #     session.commit()

    #     # Should no longer have permissions
    #     assert not enforce_permission(session, bob, RoleDomain.ORGANIZATION, "organization", Action.READ, org.id)
    #     assert not enforce_permission(session, bob, RoleDomain.ORGANIZATION, "project", Action.CREATE, org.id)

    # def test_permission_enforcer_class_directly(self, session: Session, test_data):
    #     """Test using PermissionEnforcer class directly"""
    #     enforcer = PermissionEnforcer(session)
    #     alice = test_data['identities']['alice']

    #     # Test direct usage
    #     assert enforcer.enforce(alice, RoleDomain.PLATFORM, "organization", Action.CREATE)
    #     assert enforcer.enforce(alice, RoleDomain.PLATFORM, "system", Action.DELETE)
    #     assert not enforcer.enforce(alice, RoleDomain.ORGANIZATION, "test", Action.READ, "nonexistent_org")

    # def test_cross_domain_isolation(self, session: Session, test_data):
    #     """Test that permissions are isolated across domains"""
    #     bob = test_data['identities']['bob']
    #     charlie = test_data['identities']['charlie']
    #     org = test_data['organization']
    #     project = test_data['project']

    #     # Bob is org admin but shouldn't have project-specific permissions
    #     # (unless explicitly granted at project level)
    #     assert not enforce_permission(session, bob, RoleDomain.PROJECT, "dataset", Action.CREATE, project.id)

    #     # Charlie is project admin but shouldn't have org admin permissions
    #     assert not enforce_permission(session, charlie, RoleDomain.ORGANIZATION, "organization", Action.UPDATE, org.id)
