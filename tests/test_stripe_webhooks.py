import hashlib
import hmac
import json
import time
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from app.main import app
from app.modules.stripe_webhook.models import StripeWebhookEvent, WebhookEventStatus
from app.services.billing.webhook_verification import WebhookSignatureError, webhook_verification_service


class TestWebhookSignatureVerification:
    """Test webhook signature verification functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.test_secret = "whsec_test_secret"
        self.test_payload = '{"id": "evt_test", "type": "invoice.paid"}'
        self.timestamp = int(time.time())

    def _generate_signature(self, payload: str, timestamp: int, secret: str) -> str:
        """Generate a valid Stripe signature for testing."""
        signed_payload = f"{timestamp}.{payload}"
        signature = hmac.new(
            secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return f"t={timestamp},v1={signature}"

    @patch('app.services.billing.webhook_verification.SettingsFactory')
    def test_verify_signature_success(self, mock_settings_factory):
        """Test successful signature verification."""
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.STRIPE_WEBHOOK_ENDPOINT_SECRET = self.test_secret
        mock_settings_factory.settings.return_value = mock_settings
        
        # Create new service instance with mocked settings
        service = webhook_verification_service.__class__()
        
        signature = self._generate_signature(self.test_payload, self.timestamp, self.test_secret)
        
        result = service.verify_signature(
            payload=self.test_payload.encode('utf-8'),
            signature_header=signature
        )
        
        assert result["id"] == "evt_test"
        assert result["type"] == "invoice.paid"

    @patch('app.services.billing.webhook_verification.SettingsFactory')
    def test_verify_signature_invalid_signature(self, mock_settings_factory):
        """Test signature verification with invalid signature."""
        mock_settings = MagicMock()
        mock_settings.STRIPE_WEBHOOK_ENDPOINT_SECRET = self.test_secret
        mock_settings_factory.settings.return_value = mock_settings
        
        service = webhook_verification_service.__class__()
        
        # Use wrong secret to generate invalid signature
        signature = self._generate_signature(self.test_payload, self.timestamp, "wrong_secret")
        
        with pytest.raises(WebhookSignatureError, match="Signature verification failed"):
            service.verify_signature(
                payload=self.test_payload.encode('utf-8'),
                signature_header=signature
            )

    @patch('app.services.billing.webhook_verification.SettingsFactory')
    def test_verify_signature_expired_timestamp(self, mock_settings_factory):
        """Test signature verification with expired timestamp."""
        mock_settings = MagicMock()
        mock_settings.STRIPE_WEBHOOK_ENDPOINT_SECRET = self.test_secret
        mock_settings_factory.settings.return_value = mock_settings
        
        service = webhook_verification_service.__class__()
        
        # Use timestamp from 10 minutes ago
        old_timestamp = int(time.time()) - 600
        signature = self._generate_signature(self.test_payload, old_timestamp, self.test_secret)
        
        with pytest.raises(WebhookSignatureError, match="Webhook timestamp too old"):
            service.verify_signature(
                payload=self.test_payload.encode('utf-8'),
                signature_header=signature,
                tolerance=300  # 5 minutes
            )

    @patch('app.services.billing.webhook_verification.SettingsFactory')
    def test_verify_signature_missing_secret(self, mock_settings_factory):
        """Test signature verification when secret is not configured."""
        mock_settings = MagicMock()
        mock_settings.STRIPE_WEBHOOK_ENDPOINT_SECRET = ""
        mock_settings_factory.settings.return_value = mock_settings
        
        service = webhook_verification_service.__class__()
        
        signature = self._generate_signature(self.test_payload, self.timestamp, self.test_secret)
        
        from fastapi import HTTPException
        with pytest.raises(HTTPException, match="Webhook endpoint secret not configured"):
            service.verify_signature(
                payload=self.test_payload.encode('utf-8'),
                signature_header=signature
            )


class TestWebhookEventProcessing:
    """Test webhook event processing functionality."""

    @pytest.fixture
    def sample_invoice_paid_event(self):
        """Sample invoice.paid webhook event data."""
        return {
            "id": "evt_test_invoice_paid",
            "type": "invoice.paid",
            "created": int(time.time()),
            "livemode": False,
            "data": {
                "object": {
                    "id": "in_test_invoice",
                    "object": "invoice",
                    "customer": "cus_test_customer",
                    "subscription": "sub_test_subscription",
                    "status": "paid",
                    "amount_paid": 2000,
                    "amount_due": 2000,
                    "currency": "usd",
                    "metadata": {}
                }
            }
        }

    @pytest.mark.asyncio
    async def test_process_invoice_paid_event_new(self, sample_invoice_paid_event):
        """Test processing a new invoice.paid event."""
        from app.services.billing.webhook_processor import stripe_webhook_processor
        
        # Mock database session
        mock_session = AsyncMock()
        
        # Mock webhook event CRUD operations
        with patch('app.services.billing.webhook_processor.stripe_webhook_event_crud') as mock_crud:
            # No existing event
            mock_crud.get_by_stripe_event_id.return_value = None
            
            # Mock creating new event
            mock_webhook_event = MagicMock()
            mock_webhook_event.invoice_id = None
            mock_webhook_event.customer_id = None
            mock_webhook_event.subscription_id = None
            mock_crud.create_from_stripe_event.return_value = mock_webhook_event
            mock_crud.update_processing_status.return_value = mock_webhook_event
            mock_crud.set_identity_and_organization.return_value = mock_webhook_event
            
            # Mock subscription lookup
            with patch('sqlmodel.select') as mock_select:
                mock_session.exec.return_value.first.return_value = None  # No subscription found
                
                result = await stripe_webhook_processor.process_event(
                    event_data=sample_invoice_paid_event,
                    session=mock_session
                )
                
                assert result["status"] == "success"
                assert result["invoice_id"] == "in_test_invoice"
                assert result["customer_id"] == "cus_test_customer"
                assert result["subscription_id"] == "sub_test_subscription"
                assert result["amount_paid"] == 2000
                assert result["currency"] == "usd"
                assert "payment_logged" in result["actions_taken"]

    @pytest.mark.asyncio
    async def test_process_duplicate_event(self, sample_invoice_paid_event):
        """Test processing a duplicate event."""
        from app.services.billing.webhook_processor import stripe_webhook_processor
        
        mock_session = AsyncMock()
        
        with patch('app.services.billing.webhook_processor.stripe_webhook_event_crud') as mock_crud:
            # Mock existing completed event
            mock_existing_event = MagicMock()
            mock_existing_event.status = WebhookEventStatus.COMPLETED
            mock_crud.get_by_stripe_event_id.return_value = mock_existing_event
            mock_crud.update_processing_status.return_value = mock_existing_event
            
            result = await stripe_webhook_processor.process_event(
                event_data=sample_invoice_paid_event,
                session=mock_session
            )
            
            assert result["status"] == "duplicate"
            assert result["message"] == "Event already processed"


class TestWebhookEndpoint:
    """Test the webhook endpoint functionality."""

    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
        self.test_secret = "whsec_test_secret"

    def _generate_signature(self, payload: str, timestamp: int, secret: str) -> str:
        """Generate a valid Stripe signature for testing."""
        signed_payload = f"{timestamp}.{payload}"
        signature = hmac.new(
            secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return f"t={timestamp},v1={signature}"

    @patch('app.services.billing.webhook_verification.SettingsFactory')
    @patch('app.services.billing.webhook_processor.stripe_webhook_processor')
    def test_webhook_endpoint_success(self, mock_processor, mock_settings_factory):
        """Test successful webhook processing."""
        # Mock settings
        mock_settings = MagicMock()
        mock_settings.STRIPE_WEBHOOK_ENDPOINT_SECRET = self.test_secret
        mock_settings_factory.settings.return_value = mock_settings
        
        # Mock processor
        mock_processor.process_event.return_value = {
            "status": "success",
            "message": "Event processed successfully"
        }
        
        payload = json.dumps({"id": "evt_test", "type": "invoice.paid"})
        timestamp = int(time.time())
        signature = self._generate_signature(payload, timestamp, self.test_secret)
        
        response = self.client.post(
            "/api/v1/webhooks/stripe",
            content=payload,
            headers={
                "stripe-signature": signature,
                "content-type": "application/json"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["event_id"] == "evt_test"
        assert data["event_type"] == "invoice.paid"
        assert data["processed"] is True

    def test_webhook_endpoint_missing_signature(self):
        """Test webhook endpoint with missing signature header."""
        payload = json.dumps({"id": "evt_test", "type": "invoice.paid"})
        
        response = self.client.post(
            "/api/v1/webhooks/stripe",
            content=payload,
            headers={"content-type": "application/json"}
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Missing Stripe-Signature header" in response.json()["detail"]

    def test_webhook_health_check(self):
        """Test webhook health check endpoint."""
        response = self.client.get("/api/v1/webhooks/stripe/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "stripe-webhooks"

    def test_webhook_test_endpoint(self):
        """Test webhook test endpoint."""
        payload = json.dumps({"test": "data"})
        
        response = self.client.post(
            "/api/v1/webhooks/stripe/test",
            content=payload,
            headers={"content-type": "application/json"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "received"
        assert data["body_size"] == str(len(payload))
        assert data["content_type"] == "application/json"


class TestWebhookIntegration:
    """Integration tests for webhook functionality."""

    @pytest.mark.asyncio
    async def test_webhook_event_model_lifecycle(self):
        """Test the complete lifecycle of a webhook event model."""
        from app.modules.stripe_webhook.models import StripeWebhookEvent, WebhookEventStatus

        # Create a webhook event
        event = StripeWebhookEvent(
            stripe_event_id="evt_test_lifecycle",
            event_type="invoice.paid",
            stripe_created=datetime.utcnow(),
            livemode=False,
            event_data={"test": "data"}
        )

        # Test initial state
        assert event.status == WebhookEventStatus.PENDING
        assert event.attempts == 0
        assert event.processed_at is None

        # Test marking as processing
        event.mark_processing()
        assert event.status == WebhookEventStatus.PROCESSING
        assert event.attempts == 1
        assert event.last_attempt_at is not None

        # Test marking as completed
        result = {"success": True}
        event.mark_completed(result)
        assert event.status == WebhookEventStatus.COMPLETED
        assert event.processed_at is not None
        assert event.processing_result == result

        # Test marking as failed
        event.mark_failed("Test error")
        assert event.status == WebhookEventStatus.FAILED
        assert event.error_message == "Test error"

        # Test marking as duplicate
        event.mark_duplicate()
        assert event.status == WebhookEventStatus.DUPLICATE
        assert event.processed_at is not None
