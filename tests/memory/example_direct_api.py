#!/usr/bin/env python3
"""
Direct API call example for Hippocampus Cloud Memory API

This script demonstrates how to use the memory API directly without using the MemuClient SDK.
It implements all the functionality from the original example using direct HTTP calls.

Usage:
    export MEMU_API_KEY="your-api-key-here"
    python example_direct_api.py
"""

import os
import json
import time
from typing import List, Dict, Any
import requests
from datetime import datetime

# Import models from the memory module
from app.modules.memory.schema import (
    IMemorizeRequest,
    IMemorizeResponse,
    IRetrieveDefaultCategoriesRequest,
    IRetrieveDefaultCategoriesResponse,
    IRetrieveRelatedMemoryItemsRequest,
    IRetrieveRelatedMemoryItemsResponse,
    IRetrieveRelatedClusteredCategoriesRequest,
    IRetrieveRelatedClusteredCategoriesResponse,
    IMemoryDeleteRequest,
    IMemoryDeleteResponse,
    IMessageContent,
)


class DirectMemoryAPIClient:
    """Direct HTTP client for Memory API without using MemU SDK"""

    def __init__(
        self,
        base_url: str = None,
        api_key: str = None,
        timeout: float = 30.0,
    ):
        """
        Initialize Direct Memory API client

        Args:
            base_url: Base URL for Memory API server
            api_key: API key for authentication
            timeout: Request timeout in seconds
        """
        self.base_url = base_url or os.getenv("MEMU_API_BASE_URL", "https://api.memu.so")
        self.api_key = api_key or os.getenv("MEMU_API_KEY")
        self.timeout = timeout

        if not self.base_url:
            raise ValueError(
                "base_url is required. Set MEMU_API_BASE_URL environment variable or pass base_url parameter."
            )

        if not self.api_key:
            raise ValueError(
                "api_key is required. Set MEMU_API_KEY environment variable or pass api_key parameter."
            )

        # Ensure base_url ends with /
        if not self.base_url.endswith("/"):
            self.base_url += "/"

        # Set up headers
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        print(f"Direct Memory API client initialized with base_url: {self.base_url}")

    def _make_request(
        self,
        method: str,
        endpoint: str,
        json_data: Dict[str, Any] = None,
        params: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Make HTTP request with error handling

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (relative to base_url)
            json_data: JSON request body
            params: Query parameters

        Returns:
            Dict[str, Any]: Response JSON data

        Raises:
            Exception: For API errors
        """
        url = f"{self.base_url}{endpoint}"

        try:
            print(f"Making {method} request to {url}")
            
            response = requests.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                headers=self.headers,
                timeout=self.timeout,
            )

            # Handle HTTP status codes
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 422:
                error_data = response.json()
                raise Exception(f"Validation error: {error_data}")
            elif response.status_code == 401:
                raise Exception("Authentication failed. Check your API key.")
            elif response.status_code == 403:
                raise Exception("Access forbidden. Check your API key permissions.")
            else:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f": {error_data}"
                except:
                    error_msg += f": {response.text}"
                raise Exception(error_msg)

        except requests.RequestException as e:
            raise Exception(f"Connection error: {str(e)}")

    def memorize_conversation(
        self,
        conversation: str | list[dict[str, str]],
        user_id: str,
        user_name: str,
        agent_id: str,
        agent_name: str,
        session_date: str | None = None,
    ) -> Dict[str, Any]:
        """
        Start a Celery task to memorize conversation text with agent processing

        Args:
            conversation: Conversation content to memorize, either as a string or a list of dictionaries
            user_id: User identifier
            user_name: User display name
            agent_id: Agent identifier
            agent_name: Agent display name
            session_date: Session date in ISO 8601 format (optional)

        Returns:
            Dict with task_id and status for tracking the memorization process
        """
        # Prepare conversation data
        _conversation = {}
        if isinstance(conversation, str):
            _conversation["conversation_text"] = conversation
        elif isinstance(conversation, list):
            # Convert list of dicts to IMessageContent format
            message_objects = []
            for msg in conversation:
                message_objects.append(IMessageContent(**msg))
            _conversation["conversation"] = [msg.model_dump() for msg in message_objects]
        else:
            raise ValueError("Conversation must be as a string for flatten text, or as a list of dictionaries for structured messages")

        session_date = session_date or datetime.now().astimezone().isoformat()

        # Create request data
        request_data = IMemorizeRequest(
            **_conversation,
            user_id=user_id,
            user_name=user_name,
            agent_id=agent_id,
            agent_name=agent_name,
            session_date=session_date,
        )

        print(f"Starting memorization for user {user_id} and agent {agent_id}")

        # Make API request
        response_data = self._make_request(
            method="POST",
            endpoint="api/v1/memory/memorize",
            json_data=request_data.model_dump(),
        )

        print(f"Memorization task started: {response_data.get('task_id')}")
        return response_data

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of a memorization task

        Args:
            task_id: Task identifier returned from memorize_conversation

        Returns:
            Dict with task status, progress, and results
        """
        print(f"Getting status for task: {task_id}")

        # Make API request to the correct endpoint
        response_data = self._make_request(
            method="GET", 
            endpoint=f"api/v1/memory/memorize/status/{task_id}"
        )

        print(f"Task {task_id} status: {response_data.get('status')}")
        return response_data

    def retrieve_default_categories(
        self,
        *,
        user_id: str,
        agent_id: str | None = None,
        include_inactive: bool = False,
        want_summary: bool = True,
    ) -> Dict[str, Any]:
        """
        Retrieve default categories for a project

        Args:
            user_id: User ID
            agent_id: Agent ID (None for all agents)
            include_inactive: Whether to include inactive categories
            want_summary: Whether to return summary instead of raw memory items

        Returns:
            Dict with default categories information
        """
        # Create request data
        request_data = IRetrieveDefaultCategoriesRequest(
            user_id=user_id, 
            agent_id=agent_id, 
            include_inactive=include_inactive, 
            want_summary=want_summary
        )

        # Make API request
        response_data = self._make_request(
            method="POST",
            endpoint="api/v1/memory/retrieve/default-categories",
            json_data=request_data.model_dump(),
        )

        print(f"Retrieved {response_data.get('total_categories', 0)} categories")
        return response_data

    def retrieve_related_memory_items(
        self,
        *,
        user_id: str,
        agent_id: str | None = None,
        query: str,
        top_k: int = 10,
        min_similarity: float = 0.3,
        include_categories: List[str] | None = None,
    ) -> Dict[str, Any]:
        """
        Retrieve related memory items for a user query

        Args:
            user_id: User ID
            agent_id: Agent ID (None for all agents)
            query: Search query for memory retrieval
            top_k: Number of top results to return
            min_similarity: Minimum similarity threshold
            include_categories: Categories to include in search

        Returns:
            Dict with related memory items
        """
        # Create request data
        request_data = IRetrieveRelatedMemoryItemsRequest(
            user_id=user_id,
            agent_id=agent_id,
            query=query,
            top_k=top_k,
            min_similarity=min_similarity,
            include_categories=include_categories,
        )

        print(f"Retrieving related memories for user {user_id}, query: '{query}'")

        # Make API request
        response_data = self._make_request(
            method="POST",
            endpoint="api/v1/memory/retrieve/related-memory-items",
            json_data=request_data.model_dump(),
        )

        print(f"Retrieved {response_data.get('total_found', 0)} related memories")
        return response_data

    def retrieve_related_clustered_categories(
        self,
        *,
        user_id: str,
        agent_id: str | None = None,
        category_query: str,
        top_k: int = 5,
        min_similarity: float = 0.3,
        want_summary: bool = True,
    ) -> Dict[str, Any]:
        """
        Retrieve related clustered categories for a user

        Args:
            user_id: User ID
            agent_id: Agent ID (None for all agents)
            category_query: Category search query
            top_k: Number of top categories to return
            min_similarity: Minimum similarity threshold
            want_summary: Whether to return summary instead of raw memory items

        Returns:
            Dict with related clustered categories
        """
        # Create request data
        request_data = IRetrieveRelatedClusteredCategoriesRequest(
            user_id=user_id,
            agent_id=agent_id,
            category_query=category_query,
            top_k=top_k,
            min_similarity=min_similarity,
        )

        print(f"Retrieving clustered categories for user {user_id}, query: '{category_query}'")

        # Make API request
        response_data = self._make_request(
            method="POST",
            endpoint="api/v1/memory/retrieve/related-clustered-categories",
            json_data=request_data.model_dump(),
        )

        print(f"Retrieved {response_data.get('total_categories_found', 0)} clustered categories")
        return response_data

    def delete_memories(
        self,
        user_id: str,
        agent_id: str | None = None,
    ) -> Dict[str, Any]:
        """
        Delete memories for a given user. If agent_id is provided, delete only that agent's memories; 
        otherwise delete all memories for the user within the project.

        Args:
            user_id: User identifier
            agent_id: Agent identifier (optional, delete all user memories if not provided)

        Returns:
            Dict with deletion status and count
        """
        # Create request data
        request_data = IMemoryDeleteRequest(
            user_id=user_id,
            agent_id=agent_id,
        )

        print(
            f"Deleting memories for user {user_id}"
            + (f" and agent {agent_id}" if agent_id else " (all agents)")
        )

        # Make API request
        response_data = self._make_request(
            method="POST",
            endpoint="api/v1/memory/delete",
            json_data=request_data.model_dump(),
        )

        print(f"Successfully deleted memories: {response_data.get('deleted_count', 0)} memories deleted")
        return response_data


def load_conversations_from_file(file_path: str) -> List[Dict[str, str]]:
    """Load conversations from a JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        conversation = json.load(f)
    return conversation


def wait_for_task_completion(client: DirectMemoryAPIClient, task_id: str) -> None:
    """Wait for a memorization task to complete."""
    while True:
        status_response = client.get_task_status(task_id)
        current_status = status_response.get('status')
        print(f"Task status: {current_status}")
        
        if current_status in ['SUCCESS', 'FAILURE', 'REVOKED']:
            break
        time.sleep(2)


def main():
    """Main function demonstrating direct API usage"""
    
    # Initialize Direct Memory API client
    client = DirectMemoryAPIClient(
        base_url="https://api.memu.so", 
        api_key=os.getenv("MEMU_API_KEY")
    )

    # Load conversation from JSON file (if exists)
    conversation_file = os.path.join(os.path.dirname(__file__), "conversation.json")
    
    # Create sample conversation if file doesn't exist
    if not os.path.exists(conversation_file):
        print("Creating sample conversation data...")
        sample_conversation = [
            {"role": "user", "content": "Hello, I'm John. I work as a software engineer at Google."},
            {"role": "assistant", "content": "Nice to meet you, John! What kind of software do you work on at Google?"},
            {"role": "user", "content": "I work on the Google Cloud Platform team, specifically on Kubernetes and container orchestration."},
            {"role": "assistant", "content": "That sounds interesting! How long have you been working on that?"},
            {"role": "user", "content": "About 3 years now. I started as a junior engineer and now I'm a senior engineer."},
            {"role": "assistant", "content": "That's great progress! What are some of the biggest challenges you've faced?"},
            {"role": "user", "content": "The biggest challenge was scaling our container orchestration system to handle millions of containers across multiple regions."},
            {"role": "assistant", "content": "That's quite a scale! What technologies did you use to solve that?"},
            {"role": "user", "content": "We used a combination of Kubernetes, custom operators, and distributed systems patterns."},
        ]
    else:
        sample_conversation = load_conversations_from_file(conversation_file)
    
    if not sample_conversation:
        print("No conversation loaded. Exiting.")
        return

    print("\n🚀 Processing multi-turn conversation")
    
    # Save conversation to Memory API
    memo_response = client.memorize_conversation(
        conversation=sample_conversation,
        user_id="user003", 
        user_name="User 003", 
        agent_id="assistant003", 
        agent_name="Assistant 003"
    )
    print(f"✅ Saved! Task ID: {memo_response.get('task_id')}")

    task_id = memo_response.get('task_id')
    if not task_id:
        print("❌ No task ID received, exiting.")
        return

    time.sleep(5)
    
    # Wait for completion
    print("\n⏳ Waiting for task completion...")
    wait_for_task_completion(client, task_id)
    print("✅ Conversation completed successfully!")

    print("\n🎯 Conversation has been processed and saved to Memory API!")

    # Retrieve related memory items
    print("\n🔍 Retrieving related memories...")
    memories = client.retrieve_related_memory_items(
        user_id="user003", 
        query="software engineering at Google", 
        top_k=3
    )
    
    related_memories = memories.get('related_memories', [])
    print(f"Found {len(related_memories)} related memories:")
    for memory_item in related_memories:
        memory = memory_item.get('memory', {})
        content = memory.get('content', '')
        print(f"  - Memory: {content[:100]}...")

    # Retrieve default categories
    print("\n📂 Retrieving default categories...")
    default_categories = client.retrieve_default_categories(
        user_id="user003",
        agent_id="assistant003",
        include_inactive=False
    )
    
    categories = default_categories.get('categories', [])
    total_categories = default_categories.get('total_categories', 0)
    print(f"Found {total_categories} default categories:")
    for category in categories:
        print(f"  - {category.get('name')}: {category.get('description', 'No description')}")

    # Retrieve related clustered categories
    print("\n🔗 Retrieving related clustered categories...")
    related_categories = client.retrieve_related_clustered_categories(
        user_id="user003",
        category_query="software development work",
        top_k=5,
        min_similarity=0.3
    )

    clustered_categories = related_categories.get('clustered_categories', [])
    print(f"Found {len(clustered_categories)} related categories:")
    for category in clustered_categories:
        print(f"  - {category.get('name')}")

    # Example: Delete memories
    print("\n🗑️ Deleting memories examples:")
    
    # Delete memories for a specific user and agent
    print("Deleting memories for user003 and assistant003...")
    delete_response = client.delete_memories(
        user_id="user003",
        agent_id="assistant003"
    )
    print(f"✅ Success: {delete_response.get('success')}")
    print(f"   Deleted {delete_response.get('deleted_count')} memories")

    # Example: Delete all memories for a user (without specifying agent)
    print("\nDeleting all memories for user003...")
    delete_all_response = client.delete_memories(user_id="user003")
    print(f"✅ Success: {delete_all_response.get('success')}")
    print(f"   Deleted {delete_all_response.get('deleted_count')} memories")

    print("\n🎉 Example completed successfully!")


if __name__ == "__main__":
    main()
