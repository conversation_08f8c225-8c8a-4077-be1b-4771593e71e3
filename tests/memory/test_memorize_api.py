#!/usr/bin/env python3
"""
Test script for memorize API

This script tests the memorize API endpoints:
1. POST /api/v1/memory/memorize - Start a memorization task
2. GET /api/v1/memory/memorize/status/{task_id} - Check task status

Usage:
    python test_memorize_api.py
"""

import json
import time
from typing import Any

import requests


class MemorizeAPITester:
    """Test class for memorize API endpoints"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_prefix = "/api/v1/memory"

    def test_memorize_endpoint(self) -> dict[str, Any]:
        """Test the memorize endpoint"""
        print("🧪 Testing memorize endpoint...")

        # Sample conversation data
        conversation_text = """
User: Hello, I'm <PERSON>. I work as a software engineer at Google.
Assistant: Nice to meet you, <PERSON>! What kind of software do you work on at Google?
User: I work on the Google Cloud Platform team, specifically on Kubernetes and container orchestration.
Assistant: That sounds interesting! How long have you been working on that?
User: About 3 years now. I started as a junior engineer and now I'm a senior engineer.
Assistant: That's great progress! What are some of the biggest challenges you've faced?
User: The biggest challenge was scaling our container orchestration system to handle millions of containers across multiple regions.
Assistant: That's quite a scale! What technologies did you use to solve that?
User: We used a combination of Kubernetes, custom operators, and distributed systems patterns.
        """

        # Prepare request data
        request_data = {
            "conversation_text": conversation_text,
            "user_id": "test_user_123",
            "user_name": "John",
            "agent_id": "test_agent_456",
            "agent_name": "Assistant",
        }

        try:
            # Make POST request to memorize endpoint
            response = requests.post(
                f"{self.base_url}{self.api_prefix}/memorize",
                json=request_data,
                headers={"Content-Type": "application/json"},
            )

            print(f"📡 Response Status: {response.status_code}")
            print(f"📡 Response Headers: {dict(response.headers)}")

            if response.status_code == 200:
                result = response.json()
                print("✅ Memorize request successful!")
                print(f"📋 Task ID: {result.get('task_id')}")
                print(f"📋 Status: {result.get('status')}")
                print(f"📋 Message: {result.get('message')}")
                return result
            else:
                print("❌ Memorize request failed!")
                print(f"📋 Error: {response.text}")
                return {"error": response.text, "status_code": response.status_code}

        except Exception as e:
            print(f"❌ Exception occurred: {e}")
            return {"error": str(e)}

    def test_status_endpoint(self, task_id: str) -> Dict[str, Any]:
        """Test the status endpoint"""
        print(f"🧪 Testing status endpoint for task: {task_id}")

        try:
            # Make GET request to status endpoint
            response = requests.get(f"{self.base_url}{self.api_prefix}/memorize/status/{task_id}")

            print(f"📡 Response Status: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print("✅ Status request successful!")
                print(f"📋 Task Status: {result.get('status')}")
                print(f"📋 Ready: {result.get('ready')}")
                print(f"📋 Message: {result.get('message')}")

                if result.get("result"):
                    print(f"📋 Result: {json.dumps(result.get('result'), indent=2)}")

                return result
            else:
                print("❌ Status request failed!")
                print(f"📋 Error: {response.text}")
                return {"error": response.text, "status_code": response.status_code}

        except Exception as e:
            print(f"❌ Exception occurred: {e}")
            return {"error": str(e)}

    def monitor_task_progress(self, task_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """Monitor task progress until completion or timeout"""
        print(f"🔍 Monitoring task progress for: {task_id}")
        print(f"⏱️  Max wait time: {max_wait_time} seconds")

        start_time = time.time()
        last_status = None

        while time.time() - start_time < max_wait_time:
            status_result = self.test_status_endpoint(task_id)

            if "error" in status_result:
                print(f"❌ Error checking status: {status_result['error']}")
                return status_result

            current_status = status_result.get("status")
            is_ready = status_result.get("ready", False)

            # Print status change
            if current_status != last_status:
                print(f"🔄 Status changed: {current_status}")
                last_status = current_status

            # Check if task is complete
            if is_ready:
                if current_status == "SUCCESS":
                    print("✅ Task completed successfully!")
                    return status_result
                elif current_status == "FAILURE":
                    print("❌ Task failed!")
                    return status_result
                else:
                    print(f"⚠️  Task ready but with status: {current_status}")
                    return status_result

            # Wait before next check
            print("⏳ Waiting 5 seconds before next check...")
            time.sleep(5)

        print(f"⏰ Timeout reached after {max_wait_time} seconds")
        return {"error": "Timeout", "status": "TIMEOUT"}

    def run_full_test(self) -> None:
        """Run the complete test suite"""
        print("🚀 Starting Memorize API Test Suite")
        print("=" * 50)

        # Step 1: Test memorize endpoint
        memorize_result = self.test_memorize_endpoint()

        if "error" in memorize_result:
            print("❌ Memorize test failed, stopping here")
            return

        task_id = memorize_result.get("task_id")
        if not task_id:
            print("❌ No task ID received, stopping here")
            return

        print("\n" + "=" * 50)

        # Step 2: Monitor task progress
        final_result = self.monitor_task_progress(task_id)

        print("\n" + "=" * 50)
        print("🏁 Test Suite Complete")

        if "error" not in final_result:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")


def main():
    """Main function to run the test"""
    print("🧪 Memorize API Test Script")
    print("This script tests the memorize API endpoints")
    print()

    # Create tester instance
    tester = MemorizeAPITester()

    # Run the full test suite
    tester.run_full_test()


if __name__ == "__main__":
    main()
