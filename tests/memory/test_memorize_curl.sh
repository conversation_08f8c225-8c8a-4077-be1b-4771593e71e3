#!/bin/bash

# Test script for memorize API using curl
# This script tests the memorize API endpoints using curl commands
#
# Authentication:
# - This script uses a fake access token for testing
# - To use with real authentication, set FAKE_ACCESS_TOKEN to your real token
# - Or modify the script to use environment variables for real tokens

set -e

# Configuration
BASE_URL="http://localhost:8000"
API_PREFIX="/api/v1/memory"

# Fake access token for testing (skip authentication)
# This token is used to bypass authentication in development/testing
# In production, you would need a real access token
FAKE_ACCESS_TOKEN="fake_test_token_12345"

# Use test endpoints to skip authentication
USE_TEST_ENDPOINTS=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to check if server is running
check_server() {
    print_status "Checking if server is running at $BASE_URL..."

    if curl -s "$BASE_URL/health/web" > /dev/null; then
        print_success "Server is running!"
        return 0
    else
        print_error "Server is not running at $BASE_URL"
        print_status "Please start the server first:"
        print_status "  uv run python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
        return 1
    fi
}

# Function to test memorize endpoint
test_memorize() {
    print_status "Testing memorize endpoint..."

    # Sample conversation data
    cat > /tmp/conversation.json << 'EOF'
{
    "conversation_text": "Andy: Hey Bob! Long time no see. How's everything going at school?\n
Bob: Oh, you know, the usual chaos. Kids are wild this time of year. How about you?\n
Andy: Just got promoted, actually! But I'm drowning in new tasks.\n
Bob: Congrats! You always were the tech whiz. I bet you're handling it like a pro.\n
Andy: Thanks! I'm trying, but it feels like I'm learning to swim in deep water.\n
Bob: I can relate. I went to this workshop last week, and it made me rethink everything. Ugh.\n
Andy: Workshops can be a double-edged sword, huh?\n
Bob: Yeah, they're supposed to help, but sometimes they just make you doubt yourself more.\n
Andy: Exactly! I mean, I want to be good at my job, but it's hard to keep up with expectations.\n
Bob: Tell me about it. The admin keeps piling on new initiatives, and I feel like I'm just treading water.\n
Andy: Sounds rough. I sometimes feel the same with my projects.\n
Bob: But you're so good at what you do! I always thought you had it all figured out.\n
Andy: If only! I'm just trying to keep my head above water.\n
Bob: You know, if you ever need to vent, I'm here.\n
Andy: I appreciate that. I've been pretty tied up lately, though.\n
Bob: I get it. Work-life balance is a myth, right?\n
Andy: Right! It's like we're all juggling too many balls.\n
Bob: I've been thinking… maybe I should juggle something else? Like a new career?\n
Andy: Really? That sounds like a big leap. What's making you think about that?\n
Bob: Just feeling stuck, I guess. I want to make a real impact, you know?\n
Andy: Totally understand that. You have so much talent.\n
Bob: Thanks! It's just hard to see it sometimes.\n
Andy: You've got a gift for connecting with the kids.\n
Bob: Maybe. But sometimes I feel like I'm just spinning my wheels.\n
Andy: I think you just need a little spark to reignite your passion.\n
Bob: Maybe a different approach? I don't know.\n
Andy: Have you thought about what that might look like?\n
Bob: A lot, actually. But stepping into the unknown is scary.\n
Andy: I get that. I've been thinking about a side project myself.\n
Bob: Oh? What's cooking in that brain of yours?\n
Andy: Just something fun, a way to express myself outside of work. Could be a nice break.\n
Bob: Sounds exciting! I wish I had the courage to do that.\n
Andy: You do! Just find what inspires you, and take that leap.\n
Bob: Easier said than done, my friend.\n
Andy: True. But hey, if we keep talking about it, maybe we can motivate each other.\n
Bob: That sounds like a plan. Let's keep each other accountable.\n
Andy: Absolutely! I'll be your tech support if you need it.\n
Bob: And I'll be your cheerleader!\n
Andy: Deal! Now, let's grab a coffee and brainstorm.\n
Bob: Perfect! I need a caffeine boost before diving back into chaos.\n
Andy: Same here! Let's make it happen.\n
Bob: Alright, lead the way!",
    "user_id": "test_user_123",
    "user_name": "Andy",
    "agent_id": "test_agent_456",
    "agent_name": "Bob"
}
EOF

    # Make the request
    if [ "$USE_TEST_ENDPOINTS" = "true" ]; then
        # Use test endpoint without authentication
        response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d @/tmp/conversation.json \
            "$BASE_URL$API_PREFIX/memorize/test")
    else
        # Use regular endpoint with authentication
        response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $FAKE_ACCESS_TOKEN" \
            -d @/tmp/conversation.json \
            "$BASE_URL$API_PREFIX/memorize")
    fi

    # Extract status code and body
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [ "$http_code" -eq 200 ]; then
        print_success "Memorize request successful!"
        echo "$body" | jq '.'

        # Extract task_id for status checking
        task_id=$(echo "$body" | jq -r '.task_id')
        if [ "$task_id" != "null" ] && [ "$task_id" != "" ]; then
            echo "$task_id" > /tmp/task_id.txt
            print_success "Task ID saved: $task_id"
        else
            print_error "No task ID received"
            return 1
        fi
    else
        print_error "Memorize request failed with status $http_code"
        echo "$body"
        return 1
    fi
}

# Function to test status endpoint
test_status() {
    local task_id="$1"

    if [ -z "$task_id" ]; then
        print_error "No task ID provided"
        return 1
    fi

    print_status "Testing status endpoint for task: $task_id"

    if [ "$USE_TEST_ENDPOINTS" = "true" ]; then
        # Use test endpoint without authentication
        response=$(curl -s -w "\n%{http_code}" \
            -X GET \
            "$BASE_URL$API_PREFIX/memorize/status/$task_id/test")
    else
        # Use regular endpoint with authentication
        response=$(curl -s -w "\n%{http_code}" \
            -X GET \
            -H "Authorization: Bearer $FAKE_ACCESS_TOKEN" \
            "$BASE_URL$API_PREFIX/memorize/status/$task_id")
    fi

    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [ "$http_code" -eq 200 ]; then
        print_success "Status request successful!"
        echo "$body" | jq '.'

        # Check if task is ready
        ready=$(echo "$body" | jq -r '.ready')
        status=$(echo "$body" | jq -r '.status')

        if [ "$ready" = "true" ]; then
            if [ "$status" = "SUCCESS" ]; then
                print_success "Task completed successfully!"
                return 0
            elif [ "$status" = "FAILURE" ]; then
                print_error "Task failed!"
                return 1
            else
                print_warning "Task ready but with status: $status"
                return 0
            fi
        else
            print_status "Task is still running (status: $status)"
            return 2  # Still running
        fi
    else
        print_error "Status request failed with status $http_code"
        echo "$body"
        return 1
    fi
}

# Function to monitor task progress
monitor_task() {
    local task_id="$1"
    local max_wait_time="${2:-300}"  # Default 5 minutes
    local check_interval="${3:-5}"   # Default 5 seconds

    print_status "Monitoring task progress for: $task_id"
    print_status "Max wait time: ${max_wait_time}s, Check interval: ${check_interval}s"

    local start_time=$(date +%s)
    local last_status=""

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [ $elapsed -gt $max_wait_time ]; then
            print_error "Timeout reached after ${max_wait_time}s"
            return 1
        fi

        # Test status
        test_status "$task_id"
        local status_code=$?

        if [ $status_code -eq 0 ]; then
            print_success "Task monitoring completed successfully!"
            return 0
        elif [ $status_code -eq 1 ]; then
            print_error "Task monitoring failed!"
            return 1
        else
            # Task is still running
            print_status "Waiting ${check_interval}s before next check... (${elapsed}s elapsed)"
            sleep $check_interval
        fi
    done
}

# Function to run full test suite
run_full_test() {
    print_status "Starting Memorize API Test Suite"
    echo "=================================================="

    # Check server
    if ! check_server; then
        exit 1
    fi

    echo ""
    echo "=================================================="

    # Test memorize endpoint
    if ! test_memorize; then
        print_error "Memorize test failed, stopping here"
        exit 1
    fi

    # Get task ID
    if [ ! -f /tmp/task_id.txt ]; then
        print_error "No task ID file found"
        exit 1
    fi

    task_id=$(cat /tmp/task_id.txt)

    echo ""
    echo "=================================================="

    # Monitor task progress
    if monitor_task "$task_id"; then
        print_success "All tests passed!"
    else
        print_error "Some tests failed!"
        exit 1
    fi

    echo ""
    echo "=================================================="
    print_success "Test Suite Complete"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  --check-server    Check if server is running"
    echo "  --memorize        Test memorize endpoint only"
    echo "  --status TASK_ID  Test status endpoint for specific task"
    echo "  --monitor TASK_ID Monitor task progress"
    echo "  --full            Run full test suite (default)"
    echo "  --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run full test suite"
    echo "  $0 --check-server     # Check server status"
    echo "  $0 --memorize         # Test memorize endpoint"
    echo "  $0 --status abc123    # Check status of task abc123"
    echo "  $0 --monitor abc123   # Monitor task abc123"
}

# Main script logic
main() {
    case "${1:---full}" in
        --check-server)
            check_server
            ;;
        --memorize)
            check_server && test_memorize
            ;;
        --status)
            if [ -z "$2" ]; then
                print_error "Task ID required for --status option"
                show_usage
                exit 1
            fi
            check_server && test_status "$2"
            ;;
        --monitor)
            if [ -z "$2" ]; then
                print_error "Task ID required for --monitor option"
                show_usage
                exit 1
            fi
            check_server && monitor_task "$2"
            ;;
        --full)
            run_full_test
            ;;
        --help)
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    print_status "On Ubuntu/Debian: sudo apt-get install jq"
    print_status "On macOS: brew install jq"
    print_status "On CentOS/RHEL: sudo yum install jq"
    exit 1
fi

# Run main function with all arguments
main "$@"
