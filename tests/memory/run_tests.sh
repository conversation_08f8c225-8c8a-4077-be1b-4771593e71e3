#!/bin/bash

# Quick test runner for memorize API tests
# This script provides easy access to run different types of tests

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to show menu
show_menu() {
    echo "🧪 Memorize API Test Runner"
    echo "=========================="
    echo ""
    echo "Choose a test option:"
    echo "1) Run Python test script"
    echo "2) Run curl test script (full test)"
    echo "3) Run curl test script (memorize only)"
    echo "4) Run curl test script (check server only)"
    echo "5) Show help for curl script"
    echo "6) Exit"
    echo ""
    read -p "Enter your choice (1-6): " choice
}

# Function to run Python test
run_python_test() {
    print_info "Running Python test script..."
    if command -v uv &> /dev/null; then
        uv run python test_memorize_api.py
    else
        python test_memorize_api.py
    fi
}

# Function to run curl test
run_curl_test() {
    local option="$1"
    print_info "Running curl test script with option: $option"
    ./test_memorize_curl.sh "$option"
}

# Function to show curl help
show_curl_help() {
    print_info "Showing curl script help..."
    ./test_memorize_curl.sh --help
}

# Main script logic
main() {
    if [ "$1" = "--python" ]; then
        run_python_test
        exit 0
    elif [ "$1" = "--curl" ]; then
        run_curl_test
        exit 0
    elif [ "$1" = "--curl-memorize" ]; then
        run_curl_test "--memorize"
        exit 0
    elif [ "$1" = "--curl-check" ]; then
        run_curl_test "--check-server"
        exit 0
    elif [ "$1" = "--help" ]; then
        show_curl_help
        exit 0
    fi

    # Interactive mode
    while true; do
        show_menu

        case $choice in
            1)
                run_python_test
                ;;
            2)
                run_curl_test
                ;;
            3)
                run_curl_test "--memorize"
                ;;
            4)
                run_curl_test "--check-server"
                ;;
            5)
                show_curl_help
                ;;
            6)
                print_info "Exiting..."
                exit 0
                ;;
            *)
                print_warning "Invalid choice. Please enter a number between 1 and 6."
                ;;
        esac

        echo ""
        read -p "Press Enter to continue..."
        echo ""
    done
}

# Check if we're in the right directory
if [ ! -f "test_memorize_api.py" ] || [ ! -f "test_memorize_curl.sh" ]; then
    echo "Error: Test files not found. Please run this script from the tests/memory directory."
    exit 1
fi

# Check if curl script is executable
if [ ! -x "test_memorize_curl.sh" ]; then
    print_warning "Making curl script executable..."
    chmod +x test_memorize_curl.sh
fi

# Run main function
main "$@"
