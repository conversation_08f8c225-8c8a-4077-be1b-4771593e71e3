# Memorize API Test Scripts

This directory contains test scripts for the memorize API endpoints.

## Scripts

### 1. `test_memorize_api.py` - Python Test Script

A comprehensive Python test script that tests the memorize API endpoints using the `requests` library.

**Features:**
- Tests the `/api/v1/memory/memorize` endpoint
- Tests the `/api/v1/memory/memorize/status/{task_id}` endpoint
- Monitors task progress until completion
- Provides detailed logging and error handling

**Usage:**
```bash
# Run the full test suite
python test_memorize_api.py

# Or run with uv
uv run python test_memorize_api.py
```

**Requirements:**
- Python 3.12+
- `requests` library
- `httpx` library (optional, for async support)

### 2. `test_memorize_curl.sh` - Bash Test Script

A bash script that uses `curl` commands to test the memorize API endpoints.

**Features:**
- Uses `curl` for HTTP requests
- Uses `jq` for JSON parsing
- Colored output for better readability
- Multiple test modes (check server, memorize only, status only, full test)
- Task progress monitoring

**Usage:**
```bash
# Make the script executable
chmod +x test_memorize_curl.sh

# Run full test suite
./test_memorize_curl.sh

# Check if server is running
./test_memorize_curl.sh --check-server

# Test memorize endpoint only
./test_memorize_curl.sh --memorize

# Check status of a specific task
./test_memorize_curl.sh --status <task_id>

# Monitor task progress
./test_memorize_curl.sh --monitor <task_id>

# Show help
./test_memorize_curl.sh --help
```

**Requirements:**
- `curl` (usually pre-installed)
- `jq` (JSON processor)

### 3. `run_tests.sh` - Quick Test Runner

A convenient bash script that provides an interactive menu to run different types of tests.

**Features:**
- Interactive menu for easy test selection
- Direct command-line options for automation
- Automatic script permission management
- Support for both Python and curl tests

**Usage:**
```bash
# Make the script executable
chmod +x run_tests.sh

# Interactive mode
./run_tests.sh

# Direct command options
./run_tests.sh --python          # Run Python test
./run_tests.sh --curl            # Run curl full test
./run_tests.sh --curl-memorize   # Run curl memorize only
./run_tests.sh --curl-check      # Run curl check server only
./run_tests.sh --help            # Show curl script help
```

**Requirements:**
- Bash shell
- Python and/or curl scripts (see above)

## Prerequisites

Before running the tests, make sure you have:

1. **Database running:**
   ```bash
   docker run -d \
     --name hippocampus_db \
     -e POSTGRES_DB=cloud_dev \
     -e POSTGRES_USER=postgres \
     -e POSTGRES_PASSWORD=postgres \
     -p 5432:5432 \
     -v nevamind_pgdata:/var/lib/postgresql/data \
     pgvector/pgvector:pg17
   ```

2. **Redis running (for Celery):**
   ```bash
   docker run -d --name redis-server -p 6379:6379 redis:latest
   ```

3. **Environment variables set:**
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   export DATABASE_USER="postgres"
   export DATABASE_PASSWORD="postgres"
   export DATABASE_HOST="localhost"
   export DATABASE_PORT="5432"
   export DATABASE_NAME="cloud_dev"
   ```

4. **Server running:**
   ```bash
   # Start the FastAPI server
   uv run python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

5. **Celery worker running:**
   ```bash
   # Start the Celery worker
   celery -A app.services.task_queue.celery_app worker --loglevel=info
   ```

## API Endpoints Tested

### 1. POST `/api/v1/memory/memorize`

**Request Body:**
```json
{
    "conversation_text": "User: Hello, I'm John...\nAssistant: Nice to meet you...",
    "user_id": "test_user_123",
    "user_name": "John",
    "agent_id": "test_agent_456",
    "agent_name": "Assistant"
}
```

**Response:**
```json
{
    "task_id": "abc123-def456-ghi789",
    "status": "submitted",
    "message": "Memorization task started for user John and agent Assistant"
}
```

### 2. GET `/api/v1/memory/memorize/status/{task_id}`

**Response (Pending):**
```json
{
    "task_id": "abc123-def456-ghi789",
    "status": "PENDING",
    "ready": false,
    "result": null,
    "message": "Task is queued and waiting to be processed"
}
```

**Response (Processing):**
```json
{
    "task_id": "abc123-def456-ghi789",
    "status": "PROCESSING",
    "ready": false,
    "result": {
        "current": 50,
        "total": 100,
        "status": "Processing with memory agent..."
    },
    "message": "Task is currently being processed"
}
```

**Response (Success):**
```json
{
    "task_id": "abc123-def456-ghi789",
    "status": "SUCCESS",
    "ready": true,
    "result": {
        "status": "completed",
        "conversation_id": "conv-123",
        "memory_items_extracted": 5,
        "memories_stored": 5
    },
    "message": "Memorization completed successfully"
}
```

## Troubleshooting

### Common Issues

1. **Server not running:**
   - Make sure the FastAPI server is running on port 8000
   - Check if the server is accessible at `http://localhost:8000/health/web`

2. **Database connection issues:**
   - Ensure PostgreSQL is running with pgvector extension
   - Check database credentials in environment variables
   - Run database migrations: `alembic upgrade head`

3. **Celery worker not running:**
   - Make sure Redis is running
   - Start the Celery worker with the correct app path
   - Check Celery logs for errors

4. **OpenAI API issues:**
   - Ensure `OPENAI_API_KEY` is set correctly
   - Check if the API key has sufficient credits
   - Verify the API key has access to the required models

5. **Task timeout:**
   - The default timeout is 5 minutes (300 seconds)
   - Increase timeout in the test scripts if needed
   - Check Celery worker logs for processing issues

### Debug Commands

```bash
# Check server health
curl http://localhost:8000/health/web

# Check if Redis is running
redis-cli ping

# Check database connection
psql -h localhost -U postgres -d cloud_dev -c "SELECT 1;"

# Check Celery worker status
celery -A app.services.task_queue.celery_app inspect active

# View Celery logs
celery -A app.services.task_queue.celery_app worker --loglevel=debug
```

## Test Data

The test scripts use a sample conversation about a software engineer named John who works at Google. This conversation includes:

- Personal information (name, job, company)
- Technical details (Kubernetes, container orchestration)
- Career progression (junior to senior engineer)
- Technical challenges and solutions

This provides a good test case for the memory system to extract and categorize different types of information.

## Contributing

When adding new test cases:

1. Update the sample conversation data in both scripts
2. Add new test scenarios for different conversation types
3. Test edge cases (empty conversations, very long conversations, etc.)
4. Update this README with new features or requirements
