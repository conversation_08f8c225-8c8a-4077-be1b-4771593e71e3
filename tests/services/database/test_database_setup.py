import pytest
import pytest_asyncio
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules import cruds
from app.modules.models import Identity, Role
from app.modules.role.policies import RoleDomain


@pytest_asyncio.fixture
async def data(session: AsyncSession):
    """Fixture to create test data for platform member and role"""
    identity = Identity(slug="testuser", name="Test User", email="<EMAIL>")
    session.add(identity)
    await session.commit()
    super_admin_role = cruds.role.platform_super_admin
    return {"identity": identity, "role": super_admin_role}


@pytest.mark.asyncio
async def test_migration_and_cleanup_works(session: AsyncSession, data: dict):
    """Test that database migration and cleanup works correctly"""
    result = await session.execute(text("SELECT 1"))
    assert result.fetchall() == [(1,)]
    assert data["role"].name == "super_admin"
    assert data["role"].id is not None
    assert data["identity"].slug == "testuser"
    assert data["identity"].id is not None


@pytest.mark.asyncio
async def test_transaction_rollback_isolation(session: AsyncSession):
    """Test that transactions are properly rolled back between tests"""
    # Create a test role
    test_role = Role(name="test", domain=RoleDomain.PLATFORM, description="Test role for rollback")
    session.add(test_role)
    await session.commit()

    # Verify role exists in current session
    result = await session.execute(text("SELECT COUNT(*) FROM roles WHERE name = 'test' AND domain = 'platform'"))
    count = result.scalar()
    assert count == 1


@pytest.mark.asyncio
async def test_transaction_rollback_verification(session: AsyncSession):
    """Verify that data from previous test was rolled back"""
    # Check that test role from previous test doesn't exist
    result = await session.execute(text("SELECT COUNT(*) FROM roles WHERE name = 'test' AND domain = 'platform'"))
    count = result.scalar()
    assert count == 0, "Transaction rollback failed - test role still exists"
