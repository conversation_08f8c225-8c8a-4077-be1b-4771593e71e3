import pytest
import pytest_asyncio

from app.api.v1.endpoints import organizations
from app.modules.organization.schema import IOrganizationDetailsRead
from app.services.authentication.firebase import FirebaseUser
from app.usecases.identity_manager import create_identity_with_defaults


@pytest_asyncio.fixture(autouse=True)
async def setup(app, mock_firebase_token, session):
    """Fixture to include the router in the FastAPI app"""
    app.include_router(organizations.router, prefix="/organizations", tags=["organizations"])
    firebase_user = FirebaseUser.model_validate(mock_firebase_token)
    await create_identity_with_defaults(firebase_user, session)


@pytest.mark.asyncio
async def test_create_organization(authenticated_client):
    """Test creating a new organization"""
    org_data = {
        "name": "Test Organization",
        "description": "A test organization",
        "data_location": "us-east-1",
        "currency": "USD",
    }

    response = authenticated_client.post(
        "/organizations/", json=org_data, headers={"Authorization": "Bearer fake-token"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Organization created successfully"
    new_org = IOrganizationDetailsRead.model_validate(data["data"])
    assert new_org.name == "Test Organization"
    assert new_org.description == "A test organization"
    assert new_org.data_location == "us-east-1"
    assert new_org.currency == "USD"
    assert new_org.slug.startswith("test-org")


# @pytest.mark.asyncio
# async def test_get_organization_by_slug(authenticated_client):
#     """Test getting an organization by slug"""
#     # First login to create user with default org
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Get the default organization
#     response = authenticated_client.get(
#         "/organizations/test-org",
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 200
#     data = response.json()
#     assert data["message"] == "Organization retrieved successfully"
#     assert data["data"]["slug"] == "test-org"
#     assert data["data"]["name"] == "Test Org"


# @pytest.mark.asyncio
# async def test_update_organization(authenticated_client):
#     """Test updating an organization"""
#     # First login to create user with default org
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Update the organization
#     update_data = {
#         "name": "Updated Organization Name",
#         "description": "Updated description"
#     }

#     response = authenticated_client.patch(
#         "/organizations/test-org",
#         json=update_data,
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 200
#     data = response.json()
#     assert data["message"] == "Organization updated successfully"
#     assert data["data"]["name"] == "Updated Organization Name"
#     assert data["data"]["description"] == "Updated description"
#     assert data["data"]["slug"] == "test-org"


# @pytest.mark.asyncio
# async def test_delete_organization(authenticated_client):
#     """Test deleting an organization (soft delete)"""
#     # First login and create an organization
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     org_data = {"name": "Organization to Delete"}
#     create_response = authenticated_client.post(
#         "/organizations/",
#         json=org_data,
#         headers={"Authorization": "Bearer fake-token"}
#     )
#     assert create_response.status_code == 200
#     created_slug = create_response.json()["data"]["slug"]

#     # Delete the organization
#     response = authenticated_client.delete(
#         f"/organizations/{created_slug}",
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 200
#     data = response.json()
#     assert data["message"] == "Organization deleted successfully"
#     assert data["data"]["slug"] == created_slug


# @pytest.mark.asyncio
# async def test_get_organization_members(authenticated_client):
#     """Test getting organization members"""
#     # First login to create user with default org
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Get organization members
#     response = authenticated_client.get(
#         "/organizations/test-org/members",
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 200
#     data = response.json()
#     assert data["message"] == "Organization members retrieved successfully"
#     assert isinstance(data["data"], list)


# def test_create_organization_without_authentication(client):
#     """Test creating organization without authentication"""
#     org_data = {"name": "Test Organization"}
#     response = client.post("/organizations/", json=org_data)

#     assert response.status_code == 401


# def test_get_organization_without_authentication(client):
#     """Test getting organization without authentication"""
#     response = client.get("/organizations/test-org")

#     assert response.status_code == 401


# def test_update_organization_without_authentication(client):
#     """Test updating organization without authentication"""
#     update_data = {"name": "Updated Name"}
#     response = client.patch("/organizations/test-org", json=update_data)

#     assert response.status_code == 401


# def test_delete_organization_without_authentication(client):
#     """Test deleting organization without authentication"""
#     response = client.delete("/organizations/test-org")

#     assert response.status_code == 401


# def test_get_organization_members_without_authentication(client):
#     """Test getting organization members without authentication"""
#     response = client.get("/organizations/test-org/members")

#     assert response.status_code == 401


# @pytest.mark.asyncio
# async def test_get_nonexistent_organization(authenticated_client):
#     """Test getting a nonexistent organization"""
#     # First login
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Try to get nonexistent organization
#     response = authenticated_client.get(
#         "/organizations/nonexistent-org",
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 404


# @pytest.mark.asyncio
# async def test_update_nonexistent_organization(authenticated_client):
#     """Test updating a nonexistent organization"""
#     # First login
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Try to update nonexistent organization
#     update_data = {"name": "Updated Name"}
#     response = authenticated_client.patch(
#         "/organizations/nonexistent-org",
#         json=update_data,
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 404


# @pytest.mark.asyncio
# async def test_delete_nonexistent_organization(authenticated_client):
#     """Test deleting a nonexistent organization"""
#     # First login
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Try to delete nonexistent organization
#     response = authenticated_client.delete(
#         "/organizations/nonexistent-org",
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 404


# @pytest.mark.asyncio
# async def test_create_organization_with_minimal_data(authenticated_client):
#     """Test creating organization with minimal required data"""
#     # First login
#     login_response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
#     assert login_response.status_code == 200

#     # Create organization with minimal data
#     org_data = {}  # All fields are optional in IOrganizationCreate

#     response = authenticated_client.post(
#         "/organizations/",
#         json=org_data,
#         headers={"Authorization": "Bearer fake-token"}
#     )

#     assert response.status_code == 200
#     data = response.json()
#     assert data["message"] == "Organization created successfully"
#     assert data["data"]["data_location"] == "us-east-1"  # default value
#     assert data["data"]["currency"] == "USD"  # default value
