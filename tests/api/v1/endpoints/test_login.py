from unittest.mock import patch

import pytest

from app.api.v1.endpoints import login
from app.modules.identity.schema import IIdentityReadWithProject


@pytest.fixture(autouse=True)
def include_router(app):
    """Fixture to include the router in the FastAPI app"""

    app.include_router(login.router, prefix="/login", tags=["login"])


@pytest.mark.asyncio
async def test_register_new_user_without_invitation(authenticated_client):
    """Test registering a new user without an invitation"""
    # Make request
    response = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})

    # Verify response
    assert response.status_code == 200
    data = response.json()
    print(f"Response data: {data}")  # Debug print
    assert data["message"] == "Account created successfully with default organization and project"
    identity = IIdentityReadWithProject.model_validate(data["data"]["identity"])
    assert identity.email == "<EMAIL>"
    assert identity.email_verified is True
    assert identity.default_project.slug == "test-org-proj"
    assert identity.default_project.organization.slug == "test-org"
    assert data["data"]["invitation"] is None


def test_login_without_authentication(client):
    """Test accessing login endpoint without authentication"""
    response = client.get("/login/")

    # Should return 401 Unauthorized
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_login_with_invalid_token(client):
    """Test login with invalid authentication token"""
    with patch("app.services.authentication.deps.verify_token", side_effect=Exception("Invalid token")):
        response = client.get("/login/", headers={"Authorization": "Bearer invalid-token"})

        # Should return 401 Unauthorized
        assert response.status_code == 401
        assert response.json()["error_code"] == "UNAUTHENTICATED"


@pytest.mark.asyncio
async def test_register_with_different_email(client):
    """Test registering a new user with a different email address"""
    # Create a Firebase token for different user
    different_user_token = {
        "iss": "https://securetoken.google.com/test-project",
        "aud": "test-project",
        "auth_time": **********,
        "user_id": "different-user-456",
        "sub": "different-user-456",
        "iat": **********,
        "exp": **********,
        "email": "<EMAIL>",
        "email_verified": False,
        "firebase": {"identities": {"email": ["<EMAIL>"]}, "sign_in_provider": "password"},
        "uid": "different-user-456",
    }

    with patch("app.services.authentication.deps.verify_token", return_value=different_user_token):
        # Make request
        response = client.get("/login/", headers={"Authorization": "Bearer fake-token"})

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Account created successfully with default organization and project"
        identity = IIdentityReadWithProject.model_validate(data["data"]["identity"])
        assert identity.email == "<EMAIL>"
        assert identity.email_verified is False
        assert identity.default_project.slug == "different-org-proj"
        assert identity.default_project.organization.slug == "different-org"
        assert data["data"]["invitation"] is None


@pytest.mark.asyncio
async def test_multiple_logins_same_user(authenticated_client, session):
    """Test multiple login attempts by the same user"""
    # First login (creates user)
    response1 = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
    assert response1.status_code == 200
    assert response1.json()["message"] == "Account created successfully with default organization and project"

    # Second login (existing user)
    response2 = authenticated_client.get("/login/", headers={"Authorization": "Bearer fake-token"})
    assert response2.status_code == 200
    assert response2.json()["message"] == "Login successful"
    assert response2.json()["data"]["identity"]["email"] == "<EMAIL>"
