import os
from unittest.mock import patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient

from alembic import command
from alembic.config import Config
from app.services.db.deps import get_session
from app.services.db.session import SessionFactory
from app.startup import startup
from config.loader import BaseAppSettings, Environment, SettingsFactory


def pytest_configure():
    """Configure pytest environment"""
    os.environ["ENV"] = Environment.TEST.value


@pytest.fixture(scope="session", autouse=True)
def settings() -> BaseAppSettings:
    return SettingsFactory.settings()


@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_database():
    """Run migrations once per test session"""
    alembic_config = Config("alembic.ini")
    command.upgrade(alembic_config, "head")
    yield


@pytest_asyncio.fixture(autouse=True)
async def session():
    """Create a test database session with transaction rollback for isolation"""
    SessionFactory.reset()
    engine = SessionFactory.engine()
    connection = await engine.connect()
    transaction = await connection.begin()
    session = SessionFactory.local_session_maker(bind=connection)()
    await connection.begin_nested()
    await startup()
    yield session

    await session.rollback()
    await session.close()
    await transaction.rollback()
    await connection.close()


@pytest.fixture
def app(session):
    """Create FastAPI app for testing"""
    from app.main import app

    # Override the database dependency to use the test session
    async def override_get_session():
        yield session

    app.dependency_overrides[get_session] = override_get_session
    return app


@pytest.fixture
def client(app):
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_firebase_token():
    """Create a mock Firebase token response"""
    return {
        "iss": "https://securetoken.google.com/test-project",
        "aud": "test-project",
        "auth_time": **********,
        "user_id": "test-user-123",
        "sub": "test-user-123",
        "iat": **********,
        "exp": **********,
        "email": "<EMAIL>",
        "email_verified": True,
        "firebase": {"identities": {"email": ["<EMAIL>"]}, "sign_in_provider": "password"},
        "uid": "test-user-123",
    }


@pytest.fixture
def authenticated_client(client, mock_firebase_token):
    """Create an authenticated test client with mocked Firebase token"""
    with patch("app.services.authentication.deps.verify_token", return_value=mock_firebase_token):
        yield client
