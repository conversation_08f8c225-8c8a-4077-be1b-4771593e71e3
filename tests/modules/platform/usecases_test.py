import pytest
import pytest_asyncio
from sqlmodel import select

from app.modules import cruds
from app.modules.identity.models import Identity
from app.modules.platform.member.model import PlatformMember
from app.modules.platform.member_role.model import PlatformMemberRole
from app.modules.platform.usecases import create_platform_member_with_role
from app.services.db.session import AsyncSession


@pytest_asyncio.fixture
async def data(session: AsyncSession):
    """Setup test data for platform member tests"""
    # Create test identity
    identity = Identity(slug="testuser", name="Test User", email="<EMAIL>")
    session.add(identity)
    await session.flush()

    return {"identity": identity, "role": cruds.role.platform_super_admin}


@pytest.mark.asyncio
async def test_create_platform_member_with_role_success(session: AsyncSession, data):
    """Test successful creation of platform member with role"""
    identity = data["identity"]
    role = data["role"]
    # Call the function
    member, member_role = await create_platform_member_with_role(
        identity_id=identity.id, role_id=role.id, is_active=True, session=session
    )
    await session.flush()

    # Verify platform member was created
    assert member is not None
    assert member.identity_id == identity.id
    assert member.is_active is True

    # Verify platform member role was created
    assert member_role is not None
    assert member_role.platform_member_id == member.id
    assert member_role.role_id == role.id

    # Verify data exists in database
    db_member = await session.execute(select(PlatformMember).where(PlatformMember.id == member.id))
    assert db_member.scalar_one_or_none() is not None

    db_member_role = await session.execute(select(PlatformMemberRole).where(PlatformMemberRole.id == member_role.id))
    assert db_member_role.scalar_one_or_none() is not None

    # @pytest.mark.asyncio
    # async def test_create_platform_member_with_role_inactive(session: AsyncSession, data):
    #     """Test creation of inactive platform member with role"""
    #     identity = data["identity"]
    #     role = data["role"]

    #     # Call the function with is_active=False
    #     member, member_role = await create_platform_member_with_role(
    #         identity_id=identity.id,
    #         role_id=role.id,
    #         is_active=False,
    #         session=session
    #     )

    #     # Verify platform member was created as inactive
    #     assert member.is_active is False
    #     assert member_role.platform_member_id == member.id

    # @pytest.mark.asyncio
    # async def test_create_platform_member_with_role_default_active(session: AsyncSession, data):
    """Test creation with default active state"""
    identity = data["identity"]
    role = data["role"]

    # Call the function without specifying is_active (should default to True)
    member, member_role = await create_platform_member_with_role(
        identity_id=identity.id, role_id=role.id, session=session
    )

    # Verify platform member is active by default
    assert member.is_active is True
