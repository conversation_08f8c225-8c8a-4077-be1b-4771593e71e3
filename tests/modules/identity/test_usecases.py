from unittest.mock import patch

import pytest

from app.modules.identity import Identity, identity_auth_provider
from app.modules.identity.usecases import create_identity_resources
from app.services.authentication.firebase import FirebaseUser


@pytest.mark.asyncio
async def test_create_identity_resources(session):
    # Mock Firebase user data
    firebase_user_data = {
        "iss": "https://securetoken.google.com/test-project",
        "aud": "test-project",
        "auth_time": **********,
        "user_id": "test-user-123",
        "sub": "test-user-123",
        "iat": **********,
        "exp": **********,
        "email": "<EMAIL>",
        "email_verified": True,
        "firebase": {"identities": {"email": ["<EMAIL>"]}, "sign_in_provider": "password"},
        "uid": "test-user-123",
    }

    firebase_user = FirebaseUser(**firebase_user_data)

    # Mock the Firebase verify_token function
    with patch("app.services.authentication.firebase.verify_token") as mock_verify_token:
        mock_verify_token.return_value = firebase_user_data

        # Call the function under test
        new_identity = await create_identity_resources(firebase_user, session)

        # Verify identity was created
        assert new_identity is not None
        assert isinstance(new_identity, Identity)
        assert new_identity.email == "<EMAIL>"
        assert new_identity.slug is not None
        assert new_identity.name is not None

        # Verify auth provider was created
        auth_provider = await identity_auth_provider.get_by_identity_id(identity_id=new_identity.id, session=session)
        assert auth_provider is not None
        assert auth_provider.provider_uid == "test-user-123"
        assert auth_provider.platform == "firebase"
        assert auth_provider.provider == "password"
        assert auth_provider.email == "<EMAIL>"
        assert auth_provider.email_verified is True
        assert auth_provider.extra_data == firebase_user_data


@pytest.mark.asyncio
async def test_create_identity_resources_unverified_email(session):
    # Mock Firebase user data with unverified email
    firebase_user_data = {
        "iss": "https://securetoken.google.com/test-project",
        "aud": "test-project",
        "auth_time": **********,
        "user_id": "unverified-user-456",
        "sub": "unverified-user-456",
        "iat": **********,
        "exp": **********,
        "email": "<EMAIL>",
        "email_verified": False,
        "firebase": {"identities": {"email": ["<EMAIL>"]}, "sign_in_provider": "google.com"},
        "uid": "unverified-user-456",
    }

    firebase_user = FirebaseUser(**firebase_user_data)

    # Mock the Firebase verify_token function
    with patch("app.services.authentication.firebase.verify_token") as mock_verify_token:
        mock_verify_token.return_value = firebase_user_data

        # Call the function under test
        new_identity = await create_identity_resources(firebase_user, session)

        # Verify identity was created
        assert new_identity is not None
        assert new_identity.email == "<EMAIL>"

        # Verify auth provider was created with unverified email
        auth_provider = await identity_auth_provider.get_by_identity_id(identity_id=new_identity.id, session=session)
        assert auth_provider is not None
        assert auth_provider.email_verified is False
        assert auth_provider.provider == "google.com"


@pytest.mark.asyncio
async def test_create_identity_resources_unique_slug(session):
    # Create first identity
    firebase_user_data_1 = {
        "iss": "https://securetoken.google.com/test-project",
        "aud": "test-project",
        "auth_time": **********,
        "user_id": "duplicate-user-1",
        "sub": "duplicate-user-1",
        "iat": **********,
        "exp": **********,
        "email": "<EMAIL>",
        "email_verified": True,
        "firebase": {"identities": {"email": ["<EMAIL>"]}, "sign_in_provider": "password"},
        "uid": "duplicate-user-1",
    }

    firebase_user_1 = FirebaseUser(**firebase_user_data_1)

    # Create second identity with same email (different uid)
    firebase_user_data_2 = {
        **firebase_user_data_1,
        "user_id": "duplicate-user-2",
        "sub": "duplicate-user-2",
        "uid": "duplicate-user-2",
    }

    firebase_user_2 = FirebaseUser(**firebase_user_data_2)

    # Create both identities
    identity_1 = await create_identity_resources(firebase_user_1, session)
    identity_2 = await create_identity_resources(firebase_user_2, session)

    # Verify both identities were created with unique slugs
    assert identity_1.slug != identity_2.slug
    assert identity_1.email == identity_2.email
    assert identity_1.id != identity_2.id
