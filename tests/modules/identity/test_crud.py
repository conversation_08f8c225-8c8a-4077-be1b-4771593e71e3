import pytest
from sqlmodel import select

from app.modules.identity.crud import IdentityRepo
from app.modules.identity.model import Identity


@pytest.mark.asyncio
async def test_list_slugs(session):
    # Create test identities with different slugs
    test_identities = [
        Identity(slug="alice-smith", name="<PERSON>", email="<EMAIL>"),
        Identity(slug="bob-jones", name="<PERSON>", email="<EMAIL>"),
        Identity(slug="charlie-brown", name="<PERSON>", email="<EMAIL>"),
    ]

    # Add all identities to session
    for test_identity in test_identities:
        session.add(test_identity)
    await session.commit()

    # Test list_slugs
    slugs = await IdentityRepo.list_slugs(session=session)

    # Verify the result
    assert isinstance(slugs, set)
    assert len(slugs) >= 3  # At least our test identities

    # Check that all our test slugs are in the result
    expected_slugs = {"alice-smith", "bob-jones", "charlie-brown"}
    assert expected_slugs.issubset(slugs)


@pytest.mark.asyncio
async def test_list_slugs_empty_database(session):
    # Clear any existing identities
    stmt = select(Identity)
    result = await session.execute(stmt)
    existing_identities = result.scalars().all()
    for existing_identity in existing_identities:
        await session.delete(existing_identity)
    await session.commit()

    # Test list_slugs on empty database
    slugs = await IdentityRepo.list_slugs(session=session)

    # Verify the result is an empty set
    assert isinstance(slugs, set)
    assert len(slugs) == 0


@pytest.mark.asyncio
async def test_list_slugs_with_duplicate_prevention(session):
    # Create identities to test that list_slugs returns unique values
    test_identities = [
        Identity(slug="unique-slug-1", name="Test User 1", email="<EMAIL>"),
        Identity(slug="unique-slug-2", name="Test User 2", email="<EMAIL>"),
    ]

    for test_identity in test_identities:
        session.add(test_identity)
    await session.commit()

    # Get slugs
    slugs = await IdentityRepo.list_slugs(session=session)

    # Verify slugs is a set (no duplicates by definition)
    assert isinstance(slugs, set)

    # Convert to list to check uniqueness
    slug_list = list(slugs)
    assert len(slug_list) == len(set(slug_list))  # No duplicates

    # Verify our test slugs are present
    assert "unique-slug-1" in slugs
    assert "unique-slug-2" in slugs
