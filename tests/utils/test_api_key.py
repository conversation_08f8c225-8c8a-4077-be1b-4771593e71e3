"""Tests for API key encryption and decryption utilities."""
# ruff: noqa: S105

import pytest

from app.utils.api_key import decrypt_api_key_data, encrypt_api_key_data


def test_encrypt_decrypt_basic():
    """Test basic encryption and decryption with all parameters."""
    # Test data
    app_id = "app_123"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ1234"  # 27 char <PERSON>SU<PERSON>
    created_at = "2024-01-15T10:30:00Z"
    expires_at = "2024-12-31T23:59:59Z"
    secret = "test_secret_key_123"

    # Encrypt
    encrypted = encrypt_api_key_data(
        app_id=app_id,
        project_id=project_id,
        created_at=created_at,
        expires_at=expires_at,
        secret=secret,
    )

    # Verify format
    assert encrypted.startswith("mu_")
    assert len(encrypted) > 50  # Should be reasonably long

    # Decrypt
    decrypted = decrypt_api_key_data(encrypted, secret)

    # Verify decrypted data
    assert decrypted["project_id"] == project_id
    assert decrypted["expires_at"] == expires_at
    # Note: app_id and created_at are not stored in the payload


def test_encrypt_decrypt_without_expiration():
    """Test encryption and decryption without expiration date."""
    # Test data
    app_id = "app_456"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ5678"
    created_at = "2024-01-15T10:30:00Z"
    secret = "another_secret_key"

    # Encrypt without expires_at
    encrypted = encrypt_api_key_data(
        app_id=app_id,
        project_id=project_id,
        created_at=created_at,
        expires_at=None,
        secret=secret,
    )

    # Decrypt
    decrypted = decrypt_api_key_data(encrypted, secret)

    # Verify decrypted data
    assert decrypted["project_id"] == project_id
    assert decrypted["expires_at"] is None


def test_decrypt_with_wrong_secret_fails():
    """Test that decryption with wrong secret fails."""
    # Test data
    app_id = "app_789"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ9012"
    created_at = "2024-01-15T10:30:00Z"
    expires_at = "2024-12-31T23:59:59Z"
    secret = "correct_secret"
    wrong_secret = "wrong_secret"

    # Encrypt
    encrypted = encrypt_api_key_data(
        app_id=app_id,
        project_id=project_id,
        created_at=created_at,
        expires_at=expires_at,
        secret=secret,
    )

    # Try to decrypt with wrong secret
    with pytest.raises(ValueError) as exc_info:
        decrypt_api_key_data(encrypted, wrong_secret)

    assert "Invalid or corrupted API key" in str(exc_info.value)


def test_decrypt_corrupted_token_fails():
    """Test that decryption of corrupted token fails."""
    secret = "test_secret"

    # Test various corrupted tokens
    corrupted_tokens = [
        "mu_corrupted_data",
        "mu_dGVzdA==",  # Valid base64 but not valid encrypted data
        "invalid_prefix_abc123",
        "mu_",  # Empty data
        "",  # Empty string
    ]

    for token in corrupted_tokens:
        with pytest.raises(ValueError) as exc_info:
            decrypt_api_key_data(token, secret)
        assert "Invalid or corrupted API key" in str(exc_info.value)


def test_encryption_produces_different_tokens():
    """Test that multiple encryptions produce different tokens due to random nonce."""
    # Test data
    app_id = "app_same"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ0000"
    created_at = "2024-01-15T10:30:00Z"
    expires_at = "2024-12-31T23:59:59Z"
    secret = "test_secret"

    # Encrypt same data multiple times
    tokens = []
    for _ in range(5):
        token = encrypt_api_key_data(
            app_id=app_id,
            project_id=project_id,
            created_at=created_at,
            expires_at=expires_at,
            secret=secret,
        )
        tokens.append(token)

    # All tokens should be different due to random nonce
    assert len(set(tokens)) == 5

    # But all should decrypt to same data
    for token in tokens:
        decrypted = decrypt_api_key_data(token, secret)
        assert decrypted["project_id"] == project_id
        assert decrypted["expires_at"] == expires_at


def test_empty_secret_handling():
    """Test encryption with empty secret."""
    # Test data
    app_id = "app_empty"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ1111"
    created_at = "2024-01-15T10:30:00Z"
    secret = ""  # Empty secret

    # Should still work with empty secret
    encrypted = encrypt_api_key_data(
        app_id=app_id,
        project_id=project_id,
        created_at=created_at,
        secret=secret,
    )

    decrypted = decrypt_api_key_data(encrypted, secret)
    assert decrypted["project_id"] == project_id


def test_project_id_with_pipe_character():
    """Test that project IDs containing | character work correctly."""
    secret = "test_secret"

    # Test project IDs with pipe character
    project_ids = [
        "ABC123",  # Normal
        "2fBqQhJKLMNOPQRSTUVWXYZ1234",  # Standard 27 chars
        "project|with|pipes",  # Contains pipe characters
    ]

    for project_id in project_ids:
        encrypted = encrypt_api_key_data(
            app_id="app_test",
            project_id=project_id,
            created_at="2024-01-15T10:30:00Z",
            secret=secret,
        )

        decrypted = decrypt_api_key_data(encrypted, secret)
        assert decrypted["project_id"] == project_id


def test_special_characters_in_data():
    """Test encryption with special characters in data."""
    # Test data with special characters
    app_id = "app_special!@#$%"
    project_id = "2fBqQhJKLMNOPQRSTUVWXYZ_+=/"
    created_at = "2024-01-15T10:30:00+00:00"
    expires_at = "2024-12-31T23:59:59-05:00"
    secret = "secret_with_特殊文字"

    # Encrypt
    encrypted = encrypt_api_key_data(
        app_id=app_id,
        project_id=project_id,
        created_at=created_at,
        expires_at=expires_at,
        secret=secret,
    )

    # Decrypt
    decrypted = decrypt_api_key_data(encrypted, secret)

    # Verify
    assert decrypted["project_id"] == project_id
    assert decrypted["expires_at"] == expires_at
