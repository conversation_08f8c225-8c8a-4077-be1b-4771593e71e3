import pendulum

from app.utils.slug import create_base_slug_from_email, generate_uniq_slug, sanitize_slug


class TestSanitizeSlug:
    def test_converts_to_lowercase(self):
        assert sanitize_slug("HELLO-WORLD") == "hello-world"

    def test_replaces_spaces_with_hyphens(self):
        assert sanitize_slug("hello world") == "hello-world"

    def test_removes_special_characters(self):
        assert sanitize_slug("hello@world!#$%") == "hello-world"

    def test_preserves_alphanumeric_and_hyphens(self):
        assert sanitize_slug("hello-world-123") == "hello-world-123"

    def test_removes_multiple_consecutive_hyphens(self):
        assert sanitize_slug("hello---world") == "hello-world"
        assert sanitize_slug("hello----world") == "hello-world"

    def test_removes_leading_and_trailing_hyphens(self):
        assert sanitize_slug("-hello-world-") == "hello-world"
        assert sanitize_slug("---hello-world---") == "hello-world"

    def test_handles_empty_string(self):
        assert sanitize_slug("") == ""

    def test_handles_only_special_characters(self):
        assert sanitize_slug("@#$%^&*()") == ""

    def test_complex_example(self):
        assert sanitize_slug("  Hello---World!!! 123  ") == "hello-world-123"


class TestCreateBaseSlugFromEmail:
    def test_extracts_username_part(self):
        assert create_base_slug_from_email("<EMAIL>") == "john-doe"

    def test_sanitizes_username(self):
        assert create_base_slug_from_email("<EMAIL>") == "john-doe123"

    def test_handles_special_characters_in_username(self):
        assert create_base_slug_from_email("<EMAIL>") == "john-doe"

    def test_handles_underscores(self):
        assert create_base_slug_from_email("<EMAIL>") == "john-doe"

    def test_handles_numbers(self):
        assert create_base_slug_from_email("<EMAIL>") == "user123"

    def test_handles_complex_email(self):
        assert create_base_slug_from_email("<EMAIL>") == "first-last-tag"


class TestGenerateUniqSlug:
    def test_base_slug_is_unique(self):
        existing_slugs = {"other-slug", "another-slug"}
        result = generate_uniq_slug("test-slug", existing_slugs)
        assert result == "test-slug"

    def test_adds_year_suffix_when_base_exists(self):
        existing_slugs = {"test-slug"}
        result = generate_uniq_slug("test-slug", existing_slugs)
        current_year = pendulum.now().year
        assert result == f"test-slug-{current_year}"

    def test_progressively_adds_time_suffixes(self):
        now = pendulum.now()
        existing_slugs = {
            "test-slug",
            f"test-slug-{now.year}",
            f"test-slug-{now.format('YYMM')}",
            f"test-slug-{now.format('YYMMDD')}",
        }
        result = generate_uniq_slug("test-slug", existing_slugs)
        expected = f"test-slug-{now.format('YYMMDDHH')}"
        assert result == expected

    def test_adds_random_suffix_when_all_time_suffixes_exist(self):
        now = pendulum.now()
        existing_slugs = {
            "test-slug",
            f"test-slug-{now.year}",
            f"test-slug-{now.format('YYMM')}",
            f"test-slug-{now.format('YYMMDD')}",
            f"test-slug-{now.format('YYMMDDHH')}",
            f"test-slug-{now.format('YYMMDDHHMM')}",
            f"test-slug-{now.format('YYMMDDHHMMSS')}",
        }
        result = generate_uniq_slug("test-slug", existing_slugs)

        expected_prefix = f"test-slug-{now.format('YYMMDDHHMMSS')}-"
        assert result.startswith(expected_prefix)
        assert len(result) == len(expected_prefix) + 8  # 8 random characters

    def test_random_suffix_is_unique(self):
        now = pendulum.now()
        base_with_all_suffixes = f"test-slug-{now.format('YYMMDDHHMMSS')}"
        existing_slugs = {
            "test-slug",
            f"test-slug-{now.year}",
            f"test-slug-{now.format('YYMM')}",
            f"test-slug-{now.format('YYMMDD')}",
            f"test-slug-{now.format('YYMMDDHH')}",
            f"test-slug-{now.format('YYMMDDHHMM')}",
            base_with_all_suffixes,
        }

        # Generate multiple slugs to ensure they're different
        results = set()
        for _ in range(5):
            result = generate_uniq_slug("test-slug", existing_slugs)
            results.add(result)
            existing_slugs.add(result)

        # All generated slugs should be unique
        assert len(results) == 5
        # All should start with the expected prefix
        for result in results:
            assert result.startswith(f"{base_with_all_suffixes}-")
