import boto3

# Set up AWS Bedrock client
bedrock_runtime = boto3.client(
    service_name="bedrock-runtime",
    region_name="us-east-1",  # Or your desired region
    # You can also configure credentials using environment variables or AWS CLI
    # aws_access_key_id='********************',
    # aws_secret_access_key='gs1ym18EsQkVkDhFQrV1375W4HfblCennFtCayrQ'
    # aws_access_key_id='********************',
    # aws_secret_access_key='VyQoDIhDEvRckzbhyLCTCI+E1OtKiebegX0dq1a7'
)

# Define the message for the DeepSeek-R1 model
user_message = "This is a test message, is everything going well?"

# Invoke the DeepSeek-R1 model using converse API
try:
    response = bedrock_runtime.converse(
        # modelId="deepseek.r1-v1:0",
        modelId="anthropic.claude-3-7-sonnet-20250219-v1:0",
        messages=[{"role": "user", "content": [{"text": user_message}]}],
        inferenceConfig={"maxTokens": 250, "temperature": 0.5, "topP": 1.0},
    )

    # Extract and print the model's response
    output_message = response["output"]["message"]
    print("Response from DeepSeek R1:")
    print(output_message["content"][0]["text"])

    # Print usage information if available
    if "usage" in response:
        usage = response["usage"]
        print("\nToken usage:")
        print(f"Input tokens: {usage.get('inputTokens', 'N/A')}")
        print(f"Output tokens: {usage.get('outputTokens', 'N/A')}")
        print(f"Total tokens: {usage.get('totalTokens', 'N/A')}")

except Exception as e:
    print(f"Error calling DeepSeek R1 model: {e!s}")
    print("Make sure you have the correct AWS credentials and the model is available in your region.")
