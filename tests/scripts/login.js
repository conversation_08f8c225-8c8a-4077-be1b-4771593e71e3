// scripts/getToken.js
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';
import { readFileSync } from 'fs';
import { join } from 'path';

const firebaseConfig = JSON.parse(readFileSync(join(process.cwd(), 'config/secrets/firebase-config.json'), 'utf8'));
const firebaseUser = JSON.parse(readFileSync(join(process.cwd(), 'config/secrets/firebase-user.json'), 'utf8'));
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

signInWithEmailAndPassword(auth, firebaseUser.email, firebaseUser.password)
  .then(async (userCredential) => {
    const idToken = await userCredential.user.getIdToken();
    console.log("idToken:", idToken);
  });
