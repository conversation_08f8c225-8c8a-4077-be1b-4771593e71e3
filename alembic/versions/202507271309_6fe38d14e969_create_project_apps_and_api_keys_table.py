"""create project apps and api keys table

Revision ID: 6fe38d14e969
Revises: a83e821ae1eb
Create Date: 2025-07-27 13:09:31.826166

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6fe38d14e969"
down_revision: str | Sequence[str] | None = "a83e821ae1eb"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "project_apps",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("app_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("app_secret", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_id"),
    )
    op.create_index(op.f("ix_project_apps_id"), "project_apps", ["id"], unique=False)
    op.create_table(
        "project_app_api_keys",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("project_app_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("encrypted_key", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("hashed_key", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(length=128), nullable=True),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True),
        sa.Column("scopes", sqlmodel.sql.sqltypes.AutoString(length=256), nullable=True),
        sa.Column("disabled", sa.Boolean(), nullable=False),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_used_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_app_id"],
            ["project_apps.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_project_app_api_keys_hashed_key"), "project_app_api_keys", ["hashed_key"], unique=True)
    op.create_index(op.f("ix_project_app_api_keys_id"), "project_app_api_keys", ["id"], unique=False)
    op.create_index(
        op.f("ix_project_app_api_keys_organization_id"), "project_app_api_keys", ["organization_id"], unique=False
    )
    op.create_index(
        op.f("ix_project_app_api_keys_project_app_id"), "project_app_api_keys", ["project_app_id"], unique=False
    )
    op.create_index(op.f("ix_project_app_api_keys_project_id"), "project_app_api_keys", ["project_id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_project_app_api_keys_project_id"), table_name="project_app_api_keys")
    op.drop_index(op.f("ix_project_app_api_keys_project_app_id"), table_name="project_app_api_keys")
    op.drop_index(op.f("ix_project_app_api_keys_organization_id"), table_name="project_app_api_keys")
    op.drop_index(op.f("ix_project_app_api_keys_id"), table_name="project_app_api_keys")
    op.drop_index(op.f("ix_project_app_api_keys_hashed_key"), table_name="project_app_api_keys")
    op.drop_table("project_app_api_keys")
    op.drop_index(op.f("ix_project_apps_id"), table_name="project_apps")
    op.drop_table("project_apps")
