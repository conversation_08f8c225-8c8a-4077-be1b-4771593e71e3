"""create stripe subscriptions table

Revision ID: 9e5bc6f0edaa
Revises: ea796d20dcfe
Create Date: 2025-08-01 01:18:27.873174

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9e5bc6f0edaa"
down_revision: str | Sequence[str] | None = "ea796d20dcfe"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "stripe_subscriptions",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("stripe_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("organization_subscription_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("identity_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("latest_invoice", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
        sa.Column("status", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
        sa.ForeignKeyConstraint(
            ["identity_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_subscription_id"],
            ["organization_subscriptions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_stripe_subscriptions_id"), "stripe_subscriptions", ["id"], unique=False)
    op.create_index(op.f("ix_stripe_subscriptions_identity_id"), "stripe_subscriptions", ["identity_id"], unique=False)
    op.create_index(
        op.f("ix_stripe_subscriptions_organization_id"), "stripe_subscriptions", ["organization_id"], unique=False
    )
    op.create_index(op.f("ix_stripe_subscriptions_status"), "stripe_subscriptions", ["status"], unique=False)
    op.create_index(op.f("ix_stripe_subscriptions_stripe_id"), "stripe_subscriptions", ["stripe_id"], unique=True)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_stripe_subscriptions_stripe_id"), table_name="stripe_subscriptions")
    op.drop_index(op.f("ix_stripe_subscriptions_status"), table_name="stripe_subscriptions")
    op.drop_index(op.f("ix_stripe_subscriptions_organization_id"), table_name="stripe_subscriptions")
    op.drop_index(op.f("ix_stripe_subscriptions_identity_id"), table_name="stripe_subscriptions")
    op.drop_index(op.f("ix_stripe_subscriptions_id"), table_name="stripe_subscriptions")
    op.drop_table("stripe_subscriptions")
