"""create service incidents table

Revision ID: 4724721ddb91
Revises: 0fd9e0f16023
Create Date: 2025-07-28 01:01:26.959459

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4724721ddb91"
down_revision: str | Sequence[str] | None = "0fd9e0f16023"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "service_incidents",
        sa.Column("service_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("content", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column(
            "level", sa.Enum("critical", "warning", "info", name="incidentlevel", native_enum=False), nullable=False
        ),
        sa.Column("status", sa.Enum("active", "resolved", name="incidentstatus", native_enum=False), nullable=False),
        sa.Column("extra", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("created_by", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("updated_by", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_service_incidents_id"), "service_incidents", ["id"], unique=False)
    op.create_index(op.f("ix_service_incidents_service_name"), "service_incidents", ["service_name"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_service_incidents_service_name"), table_name="service_incidents")
    op.drop_index(op.f("ix_service_incidents_id"), table_name="service_incidents")
    op.drop_table("service_incidents")
