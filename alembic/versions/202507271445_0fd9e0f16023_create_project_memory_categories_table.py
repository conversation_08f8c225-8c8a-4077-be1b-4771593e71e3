"""create project memory categories table

Revision ID: 0fd9e0f16023
Revises: eda4af67920f
Create Date: 2025-07-27 14:45:23.221723

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0fd9e0f16023"
down_revision: str | Sequence[str] | None = "eda4af67920f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "project_memory_categories",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("type", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("config", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("project_id", "name", "type"),
    )
    op.create_index(op.f("ix_project_memory_categories_id"), "project_memory_categories", ["id"], unique=False)
    op.create_index(op.f("ix_project_memory_categories_name"), "project_memory_categories", ["name"], unique=False)
    op.create_index(op.f("ix_project_memory_categories_type"), "project_memory_categories", ["type"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_project_memory_categories_type"), table_name="project_memory_categories")
    op.drop_index(op.f("ix_project_memory_categories_name"), table_name="project_memory_categories")
    op.drop_index(op.f("ix_project_memory_categories_id"), table_name="project_memory_categories")
    op.drop_table("project_memory_categories")
