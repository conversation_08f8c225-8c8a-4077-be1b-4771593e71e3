"""create stripe checkout sessions table

Revision ID: 4cde6f312d1a
Revises: d5baea84dee6
Create Date: 2025-07-27 13:22:04.137034

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4cde6f312d1a"
down_revision: str | Sequence[str] | None = "d5baea84dee6"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "stripe_checkout_sessions",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("identity_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("payment_status", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=True),
        sa.Column("status", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=True),
        sa.Column("stripe_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
        sa.ForeignKeyConstraint(
            ["identity_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_stripe_checkout_sessions_id"), "stripe_checkout_sessions", ["id"], unique=False)
    op.create_index(
        op.f("ix_stripe_checkout_sessions_identity_id"), "stripe_checkout_sessions", ["identity_id"], unique=False
    )
    op.create_index(
        op.f("ix_stripe_checkout_sessions_organization_id"),
        "stripe_checkout_sessions",
        ["organization_id"],
        unique=False,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_stripe_checkout_sessions_organization_id"), table_name="stripe_checkout_sessions")
    op.drop_index(op.f("ix_stripe_checkout_sessions_identity_id"), table_name="stripe_checkout_sessions")
    op.drop_index(op.f("ix_stripe_checkout_sessions_id"), table_name="stripe_checkout_sessions")
    op.drop_table("stripe_checkout_sessions")
