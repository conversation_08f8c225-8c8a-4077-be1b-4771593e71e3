"""create memory tasks table

Revision ID: e0942ceb5cbb
Revises: 55ed8c45c3d9
Create Date: 2025-08-04 02:43:56.357471

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e0942ceb5cbb"
down_revision: str | Sequence[str] | None = "31fcc78d565f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "memory_celery_tasks",
        sa.Column("status", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("detail_info", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("finished_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("token_used", sa.Integer(), nullable=True),
        sa.Column("conversation_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["conversation_id"],
            ["conversations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_memory_celery_tasks_conversation_id"), "memory_celery_tasks", ["conversation_id"], unique=False
    )
    op.create_index(op.f("ix_memory_celery_tasks_id"), "memory_celery_tasks", ["id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_memory_celery_tasks_id"), table_name="memory_celery_tasks")
    op.drop_index(op.f("ix_memory_celery_tasks_conversation_id"), table_name="memory_celery_tasks")
    op.drop_table("memory_celery_tasks")
