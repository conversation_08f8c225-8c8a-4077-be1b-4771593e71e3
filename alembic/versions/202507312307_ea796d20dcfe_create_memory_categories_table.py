"""create memory categories table

Revision ID: ea796d20dcfe
Revises: c30092c2a204
Create Date: 2025-07-31 23:07:21.507087

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ea796d20dcfe"
down_revision: str | Sequence[str] | None = "c30092c2a204"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "memory_categories",
        sa.Column("agent_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("user_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("category_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("group", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("count", sa.Integer(), nullable=False),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
    )
    op.create_index(op.f("ix_memory_categories_project_id"), "memory_categories", ["project_id"], unique=False)
    op.create_index(op.f("ix_memory_categories_agent_id"), "memory_categories", ["agent_id"], unique=False)
    op.create_index(op.f("ix_memory_categories_group"), "memory_categories", ["group"], unique=False)
    op.create_index(op.f("ix_memory_categories_id"), "memory_categories", ["id"], unique=False)
    op.create_index(op.f("ix_memory_categories_user_id"), "memory_categories", ["user_id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_memory_categories_project_id"), table_name="memory_categories")
    op.drop_index(op.f("ix_memory_categories_user_id"), table_name="memory_categories")
    op.drop_index(op.f("ix_memory_categories_id"), table_name="memory_categories")
    op.drop_index(op.f("ix_memory_categories_group"), table_name="memory_categories")
    op.drop_index(op.f("ix_memory_categories_agent_id"), table_name="memory_categories")
    op.drop_table("memory_categories")
