"""init subscription plans for organizations

Revision ID: c30092c2a204
Revises: f926d7b355e1
Create Date: 2025-07-30 03:39:41.612240

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import Session

from alembic import op
from app.modules.subscription_plan import SubscriptionPlan

# revision identifiers, used by Alembic.
revision: str = "c30092c2a204"
down_revision: str | Sequence[str] | None = "16191f38a1ad"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def _get_default_subscription_plan() -> SubscriptionPlan:
    """Get default roles configuration.

    Returns:
        List of default Role instances
    """
    return SubscriptionPlan(
        name="free",
        code="MEMU-FREE-v20250730",
        token_quota=500000,
        description="Free plan with limited features",
    )


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "subscription_plans",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("code", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("token_quota", sa.BigInteger(), nullable=False),
        sa.Column("memorize_calls", sa.BigInteger(), nullable=True),
        sa.Column("extra_features", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_subscription_plans_code"), "subscription_plans", ["code"], unique=True)
    op.create_index(op.f("ix_subscription_plans_id"), "subscription_plans", ["id"], unique=False)
    op.create_index(op.f("ix_subscription_plans_name"), "subscription_plans", ["name"], unique=False)
    op.create_table(
        "organization_subscriptions",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("plan_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("token_quota", sa.BigInteger(), nullable=True),
        sa.Column("memorize_calls", sa.BigInteger(), nullable=True),
        sa.Column("is_trial", sa.Boolean(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("ACTIVE", "CANCELLED", "EXPIRED", name="subscriptionstatus", native_enum=False, length=50),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["plan_id"],
            ["subscription_plans.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_organization_subscriptions_id"), "organization_subscriptions", ["id"], unique=False)
    op.add_column(
        "organizations", sa.Column("current_subscription_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True)
    )
    op.create_foreign_key(
        "organizations_current_subscription_id_fkey",
        "organizations",
        "organization_subscriptions",
        ["current_subscription_id"],
        ["id"],
        use_alter=True,
    )
    try:
        session = Session(bind=op.get_bind())
        default_subscription_plan = _get_default_subscription_plan()
        session.add(default_subscription_plan)
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_constraint("organizations_current_subscription_id_fkey", "organizations", type_="foreignkey")
    op.drop_column("organizations", "current_subscription_id")
    op.drop_index(op.f("ix_organization_subscriptions_id"), table_name="organization_subscriptions")
    op.drop_table("organization_subscriptions")
    op.drop_index(op.f("ix_subscription_plans_name"), table_name="subscription_plans")
    op.drop_index(op.f("ix_subscription_plans_id"), table_name="subscription_plans")
    op.drop_index(op.f("ix_subscription_plans_code"), table_name="subscription_plans")
    op.drop_table("subscription_plans")
