"""create stripe products and prices table

Revision ID: d5baea84dee6
Revises: 780e4d731976
Create Date: 2025-07-27 13:19:51.394788

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d5baea84dee6"
down_revision: str | Sequence[str] | None = "780e4d731976"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "stripe_products",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("stripe_product_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("stripe_metadata", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("stripe_product_id"),
    )
    op.create_index(op.f("ix_stripe_products_id"), "stripe_products", ["id"], unique=False)
    op.create_table(
        "stripe_product_prices",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("stripe_price_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("product_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("nickname", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
        sa.Column("amount", sa.Integer(), nullable=False),
        sa.Column("currency", sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
        sa.Column("active", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["product_id"],
            ["stripe_products.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("stripe_price_id"),
    )
    op.create_index(op.f("ix_stripe_product_prices_id"), "stripe_product_prices", ["id"], unique=False)
    op.create_index(op.f("ix_stripe_product_prices_product_id"), "stripe_product_prices", ["product_id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_stripe_product_prices_product_id"), table_name="stripe_product_prices")
    op.drop_index(op.f("ix_stripe_product_prices_id"), table_name="stripe_product_prices")
    op.drop_table("stripe_product_prices")
    op.drop_index(op.f("ix_stripe_products_id"), table_name="stripe_products")
    op.drop_table("stripe_products")
