"""create org subscription usage events table

Revision ID: 31fcc78d565f
Revises: 9e5bc6f0edaa
Create Date: 2025-08-01 07:55:36.797587

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "31fcc78d565f"
down_revision: str | Sequence[str] | None = "9e5bc6f0edaa"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "organization_subscription_usage_events",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column(
            "event_type",
            sa.Enum(
                "TOKEN_USAGE",
                "API_CALL",
                "MEMORY_CREATION",
                "MEMORY_RETRIEVAL",
                "CUSTOM",
                name="usageeventtype",
                native_enum=False,
                length=50,
            ),
            nullable=False,
        ),
        sa.Column("feature_name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("quantity", sa.BigInteger(), nullable=False),
        sa.Column("data", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("subscription_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("identity_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("api_key_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["api_key_id"],
            ["project_app_api_keys.id"],
        ),
        sa.ForeignKeyConstraint(
            ["identity_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["subscription_id"],
            ["organization_subscriptions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_api_key_id"),
        "organization_subscription_usage_events",
        ["api_key_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_id"),
        "organization_subscription_usage_events",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_identity_id"),
        "organization_subscription_usage_events",
        ["identity_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_organization_id"),
        "organization_subscription_usage_events",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_project_id"),
        "organization_subscription_usage_events",
        ["project_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_subscription_usage_events_subscription_id"),
        "organization_subscription_usage_events",
        ["subscription_id"],
        unique=False,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_subscription_id"),
        table_name="organization_subscription_usage_events",
    )
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_project_id"),
        table_name="organization_subscription_usage_events",
    )
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_organization_id"),
        table_name="organization_subscription_usage_events",
    )
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_identity_id"),
        table_name="organization_subscription_usage_events",
    )
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_id"), table_name="organization_subscription_usage_events"
    )
    op.drop_index(
        op.f("ix_organization_subscription_usage_events_api_key_id"),
        table_name="organization_subscription_usage_events",
    )
    op.drop_table("organization_subscription_usage_events")
