"""add task debug info

Revision ID: 1d715c81c616
Revises: 3aeec4c1f724
Create Date: 2025-08-19 05:53:09.620632

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '1d715c81c616'
down_revision: Union[str, Sequence[str], None] = '3aeec4c1f724'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('memory_celery_tasks', sa.Column('debug_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('memory_celery_tasks', 'debug_info')
    # ### end Alembic commands ###
