"""create invitations table

Revision ID: 780e4d731976
Revises: 6fe38d14e969
Create Date: 2025-07-27 13:10:41.013730

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "780e4d731976"
down_revision: str | Sequence[str] | None = "6fe38d14e969"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "invitations",
        sa.Column("email", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("token", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("used_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("target_organization_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("target_project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("sender_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("receiver_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("target_role_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.ForeignKeyConstraint(
            ["receiver_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["sender_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["target_organization_id"],
            ["organizations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["target_project_id"],
            ["projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["target_role_id"],
            ["roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_invitations_email"), "invitations", ["email"], unique=False)
    op.create_index(op.f("ix_invitations_id"), "invitations", ["id"], unique=False)
    op.create_index(op.f("ix_invitations_receiver_id"), "invitations", ["receiver_id"], unique=False)
    op.create_index(op.f("ix_invitations_sender_id"), "invitations", ["sender_id"], unique=False)
    op.create_index(op.f("ix_invitations_token"), "invitations", ["token"], unique=True)
    op.add_column("organization_members", sa.Column("invitation_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.create_foreign_key(
        "organization_members_invitation_id_fkey", "organization_members", "invitations", ["invitation_id"], ["id"]
    )
    op.add_column("project_members", sa.Column("invitation_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.create_foreign_key(
        "project_members_invitation_id_fkey", "project_members", "invitations", ["invitation_id"], ["id"]
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_constraint("project_members_invitation_id_fkey", "project_members", type_="foreignkey")
    op.drop_column("project_members", "invitation_id")
    op.drop_constraint("organization_members_invitation_id_fkey", "organization_members", type_="foreignkey")
    op.drop_column("organization_members", "invitation_id")
    op.drop_index(op.f("ix_invitations_token"), table_name="invitations")
    op.drop_index(op.f("ix_invitations_sender_id"), table_name="invitations")
    op.drop_index(op.f("ix_invitations_receiver_id"), table_name="invitations")
    op.drop_index(op.f("ix_invitations_id"), table_name="invitations")
    op.drop_index(op.f("ix_invitations_email"), table_name="invitations")
    op.drop_table("invitations")
