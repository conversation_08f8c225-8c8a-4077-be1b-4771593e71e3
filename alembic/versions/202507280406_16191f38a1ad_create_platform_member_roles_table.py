"""create platform member roles table

Revision ID: 16191f38a1ad
Revises: 4724721ddb91
Create Date: 2025-07-28 04:06:47.403820

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "16191f38a1ad"
down_revision: str | Sequence[str] | None = "4724721ddb91"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "platform_member_roles",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("member_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("role_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.ForeignKeyConstraint(
            ["member_id"],
            ["platform_members.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_platform_member_roles_id"), "platform_member_roles", ["id"], unique=False)
    op.create_index(op.f("ix_platform_members_identity_id"), "platform_members", ["identity_id"], unique=True)
    op.drop_constraint(op.f("platform_members_role_id_fkey"), "platform_members", type_="foreignkey")
    op.drop_column("platform_members", "role_id")


def downgrade() -> None:
    """Downgrade schema."""
    op.add_column("platform_members", sa.Column("role_id", sa.VARCHAR(), autoincrement=False, nullable=False))
    op.create_foreign_key(op.f("platform_members_role_id_fkey"), "platform_members", "roles", ["role_id"], ["id"])
    op.drop_index(op.f("ix_platform_members_identity_id"), table_name="platform_members")
    op.drop_index(op.f("ix_platform_member_roles_id"), table_name="platform_member_roles")
    op.drop_table("platform_member_roles")
