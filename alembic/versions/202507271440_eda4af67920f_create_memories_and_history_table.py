"""create memories, and history table

Revision ID: eda4af67920f
Revises: 58f461784bdd
Create Date: 2025-07-27 14:40:17.279829

"""

from collections.abc import Sequence

import pgvector
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "eda4af67920f"
down_revision: str | Sequence[str] | None = "58f461784bdd"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "memories",
        sa.Column("memory_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("agent_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("user_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("conversation_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("category", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("content", sa.Text(), nullable=True),
        sa.Column("embedding", pgvector.sqlalchemy.vector.VECTOR(dim=1536), nullable=True),
        sa.Column("links", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("happened_at", sa.DateTime(), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("api_key_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("project_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["conversation_id"],
            ["conversations.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    (op.create_index(op.f("ix_memories_project_id"), "memories", ["project_id"], unique=False),)
    op.create_index(op.f("ix_memories_memory_id"), "memories", ["memory_id"], unique=False)
    op.create_index(op.f("ix_memories_agent_id"), "memories", ["agent_id"], unique=False)
    op.create_index(op.f("ix_memories_api_key_id"), "memories", ["api_key_id"], unique=False)
    op.create_index(op.f("ix_memories_category"), "memories", ["category"], unique=False)
    op.create_index(op.f("ix_memories_conversation_id"), "memories", ["conversation_id"], unique=False)
    op.create_index(op.f("ix_memories_id"), "memories", ["id"], unique=False)
    op.create_index(op.f("ix_memories_user_id"), "memories", ["user_id"], unique=False)
    op.create_table(
        "memory_history",
        sa.Column("memory_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("agent_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("user_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("conversation_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("action", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column("category", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("content_before", sa.Text(), nullable=True),
        sa.Column("content_after", sa.Text(), nullable=True),
        sa.Column("links_before", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("links_after", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_memory_history_agent_id"), "memory_history", ["agent_id"], unique=False)
    op.create_index(op.f("ix_memory_history_conversation_id"), "memory_history", ["conversation_id"], unique=False)
    op.create_index(op.f("ix_memory_history_id"), "memory_history", ["id"], unique=False)
    op.create_index(op.f("ix_memory_history_memory_id"), "memory_history", ["memory_id"], unique=False)
    op.create_index(op.f("ix_memory_history_user_id"), "memory_history", ["user_id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_memories_project_id"), table_name="memories")
    op.drop_index(op.f("ix_memories_user_id"), table_name="memories")
    op.drop_index(op.f("ix_memories_id"), table_name="memories")
    op.drop_index(op.f("ix_memories_conversation_id"), table_name="memories")
    op.drop_index(op.f("ix_memories_category"), table_name="memories")
    op.drop_index(op.f("ix_memories_api_key_id"), table_name="memories")
    op.drop_index(op.f("ix_memories_agent_id"), table_name="memories")
    op.drop_table("memories")
    op.drop_index(op.f("ix_memory_history_user_id"), table_name="memory_history")
    op.drop_index(op.f("ix_memory_history_memory_id"), table_name="memory_history")
    op.drop_index(op.f("ix_memory_history_id"), table_name="memory_history")
    op.drop_index(op.f("ix_memory_history_conversation_id"), table_name="memory_history")
    op.drop_index(op.f("ix_memory_history_agent_id"), table_name="memory_history")
    op.drop_table("memory_history")
