"""add cancel_at column to stripe_subscriptions table

Revision ID: a7b3c9d2e5f8
Revises: 1d715c81c616
Create Date: 2025-08-21 18:41:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'a7b3c9d2e5f8'
down_revision: Union[str, Sequence[str], None] = '1d715c81c616'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add cancel_at column to stripe_subscriptions table
    op.add_column('stripe_subscriptions', sa.Column('cancel_at', sa.DateTime(timezone=True), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove cancel_at column from stripe_subscriptions table
    op.drop_column('stripe_subscriptions', 'cancel_at')
