"""create permissions table

Revision ID: e23b59b061c3
Revises: 630a8f8f8b08
Create Date: 2025-07-27 12:54:48.313861

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e23b59b061c3"
down_revision: str | Sequence[str] | None = "630a8f8f8b08"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "permissions",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("role_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("resource", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
        sa.Column(
            "action",
            sa.Enum("create", "read", "update", "delete", name="action", native_enum=False, length=50),
            nullable=False,
        ),
        sa.Column("scope", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("role_id", "resource", "action", "scope"),
    )
    op.create_index(op.f("ix_permissions_id"), "permissions", ["id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.add_column("identities", sa.Column("default_project_id", sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_index(op.f("ix_permissions_id"), table_name="permissions")
    op.drop_table("permissions")
