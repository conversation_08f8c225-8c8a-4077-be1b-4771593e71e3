"""initialize roles table and data

Revision ID: f4d12a432c52
Revises: c5feaa6e352b
Create Date: 2025-07-23 01:21:18.439894

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel
from sqlalchemy.orm import Session

from alembic import op
from app.modules.role.models import DomainRole, PlatformRole, Role, RoleDomain

# revision identifiers, used by Alembic.
revision: str = "f4d12a432c52"
down_revision: str | Sequence[str] | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def _get_default_roles() -> list[Role]:
    """Get default roles configuration.

    Returns:
        List of default Role instances
    """
    return [
        # Platform roles
        Role(
            name=PlatformRole.SUPER_ADMIN.value,
            domain=RoleDomain.PLATFORM.value,
            description="Super administrator with full platform access",
        ),
        # Organization roles
        Role(
            name=DomainRole.OWNER.value,
            domain=RoleDomain.ORGANIZATION.value,
            description="Organization owner with full administrative access",
        ),
        Role(
            name=DomainRole.ADMIN.value,
            domain=RoleDomain.ORGANIZATION.value,
            description="Organization administrator with management privileges",
        ),
        Role(
            name=DomainRole.VIEWER.value,
            domain=RoleDomain.ORGANIZATION.value,
            description="Organization viewer with read-only access",
        ),
        # Project roles
        Role(
            name=DomainRole.OWNER.value,
            domain=RoleDomain.PROJECT.value,
            description="Project owner with full administrative access",
        ),
        Role(
            name=DomainRole.ADMIN.value,
            domain=RoleDomain.PROJECT.value,
            description="Project administrator with management privileges",
        ),
        Role(
            name=DomainRole.VIEWER.value,
            domain=RoleDomain.PROJECT.value,
            description="Project viewer with read-only access",
        ),
    ]


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "roles",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
        sa.Column(
            "domain",
            sa.Enum("platform", "organization", "project", name="roledomain", native_enum=False),
            nullable=False,
        ),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "domain"),
    )
    op.create_index(op.f("ix_roles_id"), "roles", ["id"], unique=False)
    try:
        session = Session(bind=op.get_bind())
        default_roles = _get_default_roles()
        session.add_all(default_roles)
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_roles_id"), table_name="roles")
    op.drop_table("roles")
