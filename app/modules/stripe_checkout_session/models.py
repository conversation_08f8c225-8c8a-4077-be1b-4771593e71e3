from typing import TYPE_CHECKING

from sqlmodel import Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..identity.model import Identity
    from ..organization.model import Organization


class StripeCheckoutSession(BaseModelMixin, table=True):
    __tablename__ = "stripe_checkout_sessions"

    identity_id: str = Field(foreign_key="identities.id", nullable=False, max_length=255, index=True)
    organization_id: str = Field(foreign_key="organizations.id", nullable=False, max_length=255, index=True)
    payment_status: str | None = Field(default=None, max_length=50)
    status: str | None = Field(default=None, max_length=50)
    stripe_id: str | None = Field(default=None, max_length=255)
    session_id: str | None = Field(default=None, max_length=255)

    identity: "Identity" = Relationship()
    organization: "Organization" = Relationship()
