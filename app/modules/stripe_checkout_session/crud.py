from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import new_session

from .models import StripeCheckoutSession


class CRUDCheckoutSession(CRUDBase[StripeCheckoutSession, dict, dict]):
    async def get(self, *, checkout_id: str, session: AsyncSession | None = None) -> StripeCheckoutSession | None:
        """Get checkout session by ID."""
        session = session or new_session()
        stmt = select(StripeCheckoutSession).where(StripeCheckoutSession.id == checkout_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_stripe_id(
        self, *, stripe_id: str, session: AsyncSession | None = None
    ) -> StripeCheckoutSession | None:
        """Get checkout session by Stripe checkout session ID."""
        session = session or new_session()
        stmt = select(StripeCheckoutSession).where(StripeCheckoutSession.stripe_id == stripe_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_identity_id(
        self, *, identity_id: str, session: AsyncSession | None = None
    ) -> list[StripeCheckoutSession]:
        """Get all checkout sessions for an identity."""
        session = session or new_session()
        stmt = (
            select(StripeCheckoutSession)
            .where(StripeCheckoutSession.identity_id == identity_id)
            .order_by(StripeCheckoutSession.created_at.desc())
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_organization_id(
        self, *, organization_id: str, session: AsyncSession | None = None
    ) -> list[StripeCheckoutSession]:
        """Get all checkout sessions for an organization."""
        session = session or new_session()
        stmt = (
            select(StripeCheckoutSession)
            .where(StripeCheckoutSession.organization_id == organization_id)
            .order_by(StripeCheckoutSession.created_at.desc())
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_status(self, *, status: str, session: AsyncSession | None = None) -> list[StripeCheckoutSession]:
        """Get all checkout sessions by status."""
        session = session or new_session()
        stmt = select(StripeCheckoutSession).where(StripeCheckoutSession.status == status)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_payment_status(
        self, *, payment_status: str, session: AsyncSession | None = None
    ) -> list[StripeCheckoutSession]:
        """Get all checkout sessions by payment status."""
        session = session or new_session()
        stmt = select(StripeCheckoutSession).where(StripeCheckoutSession.payment_status == payment_status)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def upsert_from_stripe(
        self, *, stripe_checkout: dict, identity_id: str, organization_id: str, session: AsyncSession | None = None
    ) -> StripeCheckoutSession:
        """Create or update checkout session from Stripe data."""
        session = session or new_session()

        # Check if checkout session already exists
        existing = None
        if stripe_checkout.get("id"):
            existing = await self.get_by_stripe_id(stripe_id=stripe_checkout["id"], session=session)

        if existing:
            # Update existing checkout session
            existing.identity_id = identity_id
            existing.organization_id = organization_id
            existing.payment_status = stripe_checkout.get("payment_status")
            existing.status = stripe_checkout.get("status")
            existing.stripe_id = stripe_checkout.get("id")
            session.add(existing)
            await session.commit()
            await session.refresh(existing)
            return existing
        else:
            # Create new checkout session
            new_checkout = StripeCheckoutSession(
                identity_id=identity_id,
                organization_id=organization_id,
                payment_status=stripe_checkout.get("payment_status"),
                status=stripe_checkout.get("status"),
                stripe_id=stripe_checkout.get("id"),
            )
            session.add(new_checkout)
            await session.commit()
            await session.refresh(new_checkout)
            return new_checkout


# Singleton instance
strip_checkout_session = CRUDCheckoutSession(StripeCheckoutSession)
