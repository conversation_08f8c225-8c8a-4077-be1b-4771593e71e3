"""Authorization usecase for permission enforcement.

This module provides a clean interface for checking user permissions across
different contexts (platform, organization, project).
"""

from app.exceptions import UnauthorizedException
from app.modules import cruds
from app.modules.identity.model import Identity
from app.modules.organization import Organization
from app.modules.organization.project import Project
from app.modules.role.models import Role
from app.modules.shared.base_policy import Action
from app.services.db.session import AsyncSession


async def _role_has_permission(role: Role, resource: str, action: Action, db_session: AsyncSession) -> bool:
    """Check if a role has permission for a specific resource and action.

    Args:
        role: The role to check
        resource: The resource type
        action: The action to perform
        db_session: Database db_session for queries

    Returns:
        True if the role has permission, False otherwise
    """
    return await cruds.permission.role_has_permission(
        role_id=role.id, resource=resource, action=action, db_session=db_session
    )


async def has_permission(
    identity: Identity,
    action: Action,
    resource: str,
    *,
    domain: Organization | Project | None = None,
    is_platform: bool = False,
    db_session: AsyncSession,
) -> bool:
    """Check if a user has permission to perform an action on a resource.

    This function checks permissions incrementally across different contexts,
    returning as soon as a matching permission is found for better performance.

    Args:
        identity: The user identity to check permissions for
        action: The action being performed (CREATE, READ, UPDATE, DELETE)
        resource: The resource type being accessed
        db_session: Database db_session for queries
        domain: Optional organization or project context
        is_platform: Whether this is a platform-level operation

    Returns:
        True if permission is granted, False otherwise

    Examples:
        # Check organization-level permission
        can_create_project = await enforce_permission(
            identity=current_user,
            action=Action.CREATE,
            resource="project",
            db_session=db_session,
            domain=organization
        )

        # Check platform-level permission
        can_create_org = await enforce_permission(
            identity=current_user,
            action=Action.CREATE,
            resource="organization",
            db_session=db_session,
            is_platform=True
        )

        # Check project-level permission
        can_update_project = await enforce_permission(
            identity=current_user,
            action=Action.UPDATE,
            resource="project_settings",
            db_session=db_session,
            domain=project
        )
    """
    # Check platform-level roles first (always applicable)
    platform_roles = await cruds.platform_member.list_roles_by_identity_id(
        identity_id=identity.id, db_session=db_session
    )
    from loguru import logger

    # Check permissions for platform roles
    for role in platform_roles:
        if await _role_has_permission(role, resource, action, db_session):
            return True

    # If only platform roles are needed, we're done
    if is_platform or domain is None:
        return False

    # Check organization-specific roles
    if isinstance(domain, Organization):
        org_roles = await cruds.OrganizationMemberRepo.list_roles_by_identity_id(
            identity_id=identity.id, organization_id=domain.id, db_session=db_session
        )
        from loguru import logger

        logger.debug(
            f"Organization roles for {identity.id} in organization {domain.id}: {[role.name for role in org_roles]}"
        )
        for role in org_roles:
            if await _role_has_permission(role, resource, action, db_session):
                return True

    # Check project-specific roles
    elif isinstance(domain, Project):
        # Check direct project membership roles
        project_roles = await cruds.ProjectMemberRepo.list_roles_by_identity_id(
            identity_id=identity.id, project_id=domain.id, db_session=db_session
        )
        from loguru import logger

        logger.debug(f"Project roles for {identity.id} in project {domain.id}: {[role.name for role in project_roles]}")

        for role in project_roles:
            if await _role_has_permission(role, resource, action, db_session):
                return True

        # Check inherited organization roles
        org_roles = await cruds.OrganizationMemberRepo.list_roles_by_identity_id(
            identity_id=identity.id, organization_id=domain.organization_id, db_session=db_session
        )

        for role in org_roles:
            if await _role_has_permission(role, resource, action, db_session):
                return True

    return False


async def assert_permission(
    identity: Identity,
    action: Action,
    resource: str,
    *,
    domain: Organization | Project | None = None,
    is_platform: bool = False,
    db_session: AsyncSession,
) -> None:
    """Assert permission by raising UnauthorizedException if check fails.

    Args:
        identity: The user identity to check permissions for
        action: The action being performed (CREATE, READ, UPDATE, DELETE)
        resource: The resource type being accessed
        db_session: Database db_session for queries
        domain: Optional organization or project context
        is_platform: Whether this is a platform-level operation

    Raises:
        UnauthorizedException: If the user lacks permission
    """
    if not await has_permission(
        identity=identity,
        action=action,
        resource=resource,
        domain=domain,
        is_platform=is_platform,
        db_session=db_session,
    ):
        raise UnauthorizedException(f"Permission denied for {action.value} on {resource}")
