from pydantic import BaseModel, ConfigDict, Field

from app.modules.shared.base_policy import Action


class IPermissionBase(BaseModel):
    """Base schema for Permission"""

    role_id: str = Field(..., description="Foreign key to Role")
    resource: str = Field(..., max_length=100, description="Resource being accessed")
    action: Action = Field(..., description="Action being performed")


class IPermissionCreate(IPermissionBase):
    """Schema for creating a new Permission"""

    pass


class IPermissionUpdate(BaseModel):
    """Schema for updating a Permission"""

    role_id: str | None = Field(None, description="Foreign key to Role")
    resource: str | None = Field(None, max_length=100, description="Resource being accessed")
    action: Action | None = Field(None, description="Action being performed")


class IPermissionRead(IPermissionBase):
    """Schema for reading Permission data"""

    model_config = ConfigDict(from_attributes=True)

    id: str = Field(..., description="Permission ID")
