from typing import TYPE_CHECKING, Optional

import sqlalchemy as sa
from sqlmodel import Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin
from app.modules.shared.base_policy import Action

if TYPE_CHECKING:
    from app.modules.role import Role


class Permission(BaseModelMixin, table=True):
    __tablename__ = "permissions"
    __table_args__ = (UniqueConstraint("role_id", "resource", "action", "scope"),)

    role_id: str = Field(foreign_key="roles.id", description="Foreign key to Role")
    resource: str = Field(max_length=100, description="Resource being accessed")
    action: Action = Field(sa_type=sa.Enum(Action, native_enum=False, length=50), description="Action being performed")
    scope: str = Field(default="*", max_length=100, description="Optional scope for the permission")

    role: Optional["Role"] = Relationship(back_populates="permissions", sa_relationship_kwargs={"lazy": "selectin"})

    def __str__(self) -> str:
        role_name = self.role.name if self.role else str(self.role_id)
        return f"{role_name}:{self.resource}:{self.action.value}"

    def __repr__(self) -> str:
        return f'<Permission {self.id}: "{self!s}">'


class PolicyRule:
    """Helper class to define policy rules before storing as Permission records."""

    def __init__(self, role_id: int, resource: str, action: Action):
        self.role_id = role_id
        self.resource = resource
        self.action = action

    def to_permission(self) -> Permission:
        """Convert policy rule to Permission model instance."""
        return Permission(role_id=self.role_id, resource=self.resource, action=self.action)
