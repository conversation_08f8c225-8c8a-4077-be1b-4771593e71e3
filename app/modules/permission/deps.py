from typing import Annotated

from fastapi import Depends, HTTPException

from app.modules import cruds
from app.modules.organization.project.app.api_key.deps import get_current_project
from app.modules.organization.subscription.usage_events.crud import usage_event
from app.modules.organization.subscription.usage_events.model import UsageEventType
from app.modules.shared.base_policy import Action
from app.services.authentication.deps import FirebaseUser, get_current_user
from app.services.db.deps import DatabaseSessionDep

from ..identity import Identity
from ..identity.deps import _get_current_identity, _IdentityDep
from .usecases import assert_permission


def need_any_firebase_user() -> Annotated[FirebaseUser, Depends]:
    """Dependency factory for Firebase user"""
    return Depends(get_current_user)


def need_any_identity() -> Annotated[Identity, Depends]:
    """Dependency factory for any identity"""
    return Depends(_get_current_identity)


def need_org_permission(
    action: Action, resource: str = "*", is_platform: bool = False, need_email_verified: bool = False
) -> Annotated[Identity, Depends]:
    """Dependency factory for permission checking"""

    async def check_permission(
        organization_id: str, identity: _IdentityDep, db_session: DatabaseSessionDep
    ) -> Identity:
        org = await cruds.OrganizationRepo.get(model_id=organization_id, db_session=db_session)
        await assert_permission(identity, action, resource, domain=org, is_platform=is_platform, db_session=db_session)
        return identity

    return Depends(check_permission)


def need_proj_permission(
    action: Action, resource: str = "*", is_platform: bool = False, need_email_verified: bool = False
) -> Annotated[Identity, Depends]:
    """Dependency factory for permission checking"""

    async def check_permission(project_id: str, identity: _IdentityDep, db_session: DatabaseSessionDep) -> Identity:
        proj = await cruds.ProjectRepo.get(model_id=project_id, db_session=db_session)
        # if need_email_verified and not identity.auth_provider.email_verified:
        #     raise EmailNotVerifiedException()
        await assert_permission(identity, action, resource, domain=proj, is_platform=is_platform, db_session=db_session)
        return identity

    return Depends(check_permission)


def need_api_key() -> Annotated[tuple[str, str], Depends]:
    """Dependency factory for API key permission checking"""
    return Depends(get_current_project)


APIKEY_DEP = Annotated[tuple[str, str], Depends(get_current_project)]


def need_memorize_calls() -> Annotated[tuple[str, str], Depends]:
    """Dependency factory for memory calls permission checking"""

    async def check_permission(project_api_key: APIKEY_DEP, db_session: DatabaseSessionDep) -> tuple[str, str]:
        # Extract project_id and api_key_id from the tuple
        (project_id, api_key_id) = project_api_key

        # Get the project object
        project = await cruds.ProjectRepo.get(model_id=project_id, db_session=db_session)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get the organization's active subscription
        org_subscription = await cruds.organization_subscription.get_active_by_org(
            org_id=project.organization_id, db_session=db_session
        )

        if not org_subscription:
            raise HTTPException(status_code=403, detail="No active subscription found for this organization")

        # Get the subscription plan to check the memorize_calls limit
        plan = await cruds.subscription_plan.get(model_id=org_subscription.plan_id, db_session=db_session)

        if not plan:
            raise HTTPException(status_code=500, detail="Subscription plan not found")

        # Get the plan's memorize_calls limit (use subscription override if exists)
        memorize_calls_limit = (
            org_subscription.memorize_calls if org_subscription.memorize_calls is not None else plan.memorize_calls
        )

        # If no limit is set, allow unlimited usage
        if memorize_calls_limit is None:
            return (project_id, api_key_id)

        # Check current usage against the limit
        quota_check = await usage_event.check_quota(
            organization_id=project.organization_id,
            subscription_id=org_subscription.id,
            event_type=UsageEventType.MEMORIZE_CALL,
            requested_quantity=1,
            quota_limit=memorize_calls_limit,
            period_start=org_subscription.started_at,
            session=db_session,
        )

        if not quota_check["is_allowed"]:
            raise HTTPException(
                status_code=429,
                detail=f"Memorize calls quota exceeded. Current usage: {quota_check['current_usage']}/{memorize_calls_limit}",
            )

        return (project_id, api_key_id)

    return Depends(check_permission)
