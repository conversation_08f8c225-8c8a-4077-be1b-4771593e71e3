from __future__ import annotations

from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.modules.shared.base_policy import Action
from app.services.db.session import AsyncSession

from .models import Permission
from .schema import IPermissionCreate, IPermissionUpdate


class CRUDPermission(CRUDBase[Permission, IPermissionCreate, IPermissionUpdate]):
    """CRUD operations for Permission model"""

    async def get_by_role_id(self, *, role_id: str, db_session: AsyncSession) -> list[Permission]:
        """Get all permissions for a specific role."""
        stmt = select(Permission).where(Permission.role_id == role_id)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_resource(self, *, resource: str, db_session: AsyncSession) -> list[Permission]:
        """Get all permissions for a specific resource."""
        stmt = select(Permission).where(Permission.resource == resource)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_action(self, *, action: Action, db_session: AsyncSession) -> list[Permission]:
        """Get all permissions for a specific action."""
        stmt = select(Permission).where(Permission.action == action)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_role_and_resource(
        self, *, role_id: str, resource: str, db_session: AsyncSession
    ) -> list[Permission]:
        """Get all permissions for a specific role and resource combination."""
        stmt = select(Permission).where(Permission.role_id == role_id, Permission.resource == resource)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_permission(
        self, *, role_id: str, resource: str, action: Action, db_session: AsyncSession
    ) -> Permission | None:
        """Get a specific permission by role, resource, and action."""
        stmt = select(Permission).where(
            Permission.role_id == role_id, Permission.resource == resource, Permission.action == action
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def remove_by_role_id(self, *, role_id: str, db_session: AsyncSession) -> int:
        """Remove all permissions for a specific role. Returns count of removed permissions."""
        permissions = await self.get_by_role_id(role_id=role_id, db_session=db_session)

        count = 0
        for permission in permissions:
            await self.remove(id=permission.id, db_session=db_session)
            count += 1

        return count

    async def role_has_permission(
        self, *, role_id: str, resource: str, action: Action, db_session: AsyncSession
    ) -> bool:
        """Check if a role has permission for a specific resource and action.

        This method checks both exact and wildcard (*) matches for resources and actions
        in a single query for better performance.
        """
        from sqlalchemy import or_

        from app.modules.shared.base_policy import Action as ActionEnum

        stmt = select(Permission).where(
            Permission.role_id == role_id,
            or_(Permission.resource == resource, Permission.resource == "*"),
            or_(Permission.action == action, Permission.action == ActionEnum.ALL),
        )
        result = await db_session.execute(stmt)
        permission = result.scalar_one_or_none()

        return permission is not None


permission = CRUDPermission(Permission)
