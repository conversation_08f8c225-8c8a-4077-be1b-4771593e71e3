from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, Field


class StripeWebhookEventData(BaseModel):
    """Base class for Stripe webhook event data."""
    object: dict[str, Any]


class StripeInvoiceData(BaseModel):
    """Stripe invoice data from webhook events."""
    id: str
    object: Literal["invoice"]
    customer: str
    subscription: str | None = None
    status: str
    amount_paid: int
    amount_due: int
    currency: str
    created: int
    metadata: dict[str, Any] = Field(default_factory=dict)


class StripeInvoiceEventData(StripeWebhookEventData):
    """Stripe invoice event data."""
    object: StripeInvoiceData


class StripeWebhookEvent(BaseModel):
    """Stripe webhook event structure."""
    id: str
    object: Literal["event"]
    type: str
    created: int
    data: StripeWebhookEventData
    livemode: bool
    pending_webhooks: int
    request: dict[str, Any] | None = None


class StripeInvoicePaidEvent(BaseModel):
    """Specific model for invoice.paid webhook events."""
    id: str
    object: Literal["event"]
    type: Literal["invoice.paid"]
    created: int
    data: StripeInvoiceEventData
    livemode: bool
    pending_webhooks: int
    request: dict[str, Any] | None = None


class WebhookProcessingResponse(BaseModel):
    """Response model for webhook processing."""
    event_id: str
    event_type: str
    processed: bool
    message: str
    timestamp: datetime = Field(default_factory=lambda: datetime.utcnow())


class WebhookErrorResponse(BaseModel):
    """Error response model for webhook processing."""
    error: str
    event_id: str | None = None
    event_type: str | None = None
    timestamp: datetime = Field(default_factory=lambda: datetime.utcnow())
