from pydantic import BaseModel

from app.modules.organization.model import OrganizationBase


class IOrganizationRead(OrganizationBase):
    pass


class IOrganizationDetailsRead(IOrganizationRead):
    role: str | None
    owner: str | None = None
    projects: list["IProjectInOrganization"] = []


class IProjectInOrganization(BaseModel):
    id: str
    slug: str
    name: str | None = None
    description: str | None = None
    status: str
    environment: str
    data_location: str | None = None


class IOrgReadInclude(OrganizationBase):
    pass


class IOrganizationCreate(BaseModel):
    name: str | None = None
    description: str | None = None
    data_location: str = "us-east-1"
    currency: str = "USD"


class IOrganizationWithProjects(BaseModel):
    id: str
    slug: str
    name: str | None = None
    description: str | None = None
    status: str
    data_location: str
    currency: str
    role: str | None = None
    projects: list[IProjectInOrganization] = []


class IOrganizationUpdate(BaseModel):
    slug: str | None = None
    name: str | None = None
    description: str | None = None
    status: str | None = None
    data_location: str | None = None
    currency: str | None = None
