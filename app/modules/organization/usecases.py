"""Organization usecase functions."""

import pendulum
from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.exceptions import InternalErrorException, NotFoundException
from app.modules.role.crud import role
from app.modules.subscription_plan import subscription_plan
from app.services.db.session import AsyncSession
from app.utils import slug

from .crud import OrganizationRepo
from .member.crud import OrganizationMemberRepo
from .member.schema import IOrganizationMemberCreate
from .model import Organization
from .project import Project
from .project.crud import ProjectRepo
from .project.schema import IProjectCreate
from .project.usecases import create_proj_with_defaults
from .schema import IOrganizationCreate, IOrganizationDetailsRead
from .subscription import IOrganizationSubscriptionCreate, organization_subscription


async def _generate_uniq_org_slug(
    identity_slug: str,
    db_session: AsyncSession,
) -> str:
    """Generate a unique organization slug"""
    existing_slugs = await OrganizationRepo.list_slugs(session=db_session)
    base_slug = f"{identity_slug}-org"
    return slug.generate_uniq_slug(base_slug, existing_slugs)


async def create_org_resources(
    identity_id: str,
    identity_slug: str,
    organization_params: IOrganizationCreate,
    db_session: AsyncSession,
) -> Organization:
    """Create organization with organization member as owner"""

    # Generate unique slug
    org_slug = await _generate_uniq_org_slug(identity_slug, db_session=db_session)

    # Create organization object with auto-generated fields
    org_obj = Organization(
        **organization_params.model_dump(),
        slug=org_slug,
        status="active",  # Default status
        created_by=identity_id,
    )

    # Create the organization
    new_organization = await OrganizationRepo.create(new=org_obj, db_session=db_session)

    await db_session.flush()
    # Create organization member with owner role
    owner_role = role.org_owner
    if not owner_role:
        raise InternalErrorException()

    member_create = IOrganizationMemberCreate(
        organization_id=new_organization.id, identity_id=identity_id, role_id=owner_role.id, is_active=True
    )
    await OrganizationMemberRepo.create(new=member_create, db_session=db_session)

    # Create organization subscription with free plan
    default_plan = subscription_plan.free
    if not default_plan:
        raise InternalErrorException()

    subscription_create = IOrganizationSubscriptionCreate(
        organization_id=new_organization.id, plan_id=default_plan.id, started_at=pendulum.now()
    )
    default_subscription = await organization_subscription.create(new=subscription_create, db_session=db_session)
    await db_session.flush()
    new_organization.current_subscription_id = default_subscription.id

    return new_organization


async def create_org_with_defaults(
    identity_id, identity_slug, *, org_params: IOrganizationCreate | None = None, db_session: AsyncSession
) -> tuple[Organization, Project]:
    if not org_params:
        org_params = IOrganizationCreate(name=f"{identity_slug}'s default organization")
    default_org = await create_org_resources(identity_id, identity_slug, org_params, db_session)

    await db_session.flush()
    await db_session.refresh(default_org)
    proj_params = IProjectCreate(name=f"{identity_slug}'s default project")
    default_proj = await create_proj_with_defaults(
        identity_id, default_org.id, default_org.slug, proj_params, db_session
    )

    return default_org, default_proj


async def delete_org_soft_cascade(
    organization_id: str,
    db_session: AsyncSession,
) -> Organization:
    """Soft delete organization and its projects"""
    stmt = select(Organization).options(selectinload(Organization.projects)).where(Organization.id == organization_id)
    result = await db_session.execute(stmt)
    org = result.scalar_one_or_none()

    if not org:
        raise NotFoundException(Organization)

    # Soft delete the organizatio
    # Optionally, handle related resources like projects, members, etc.
    for proj in org.projects:
        await ProjectRepo.soft_delete(model_id=proj.id, db_session=db_session)
    deleted_org = await OrganizationRepo.soft_delete(org_id=organization_id, db_session=db_session)
    return deleted_org


async def get_org_with_role_and_owner(
    identity_id: str,
    org: Organization,
    db_session: AsyncSession,
) -> IOrganizationDetailsRead:
    """Get organization with role information"""
    if not org:
        raise NotFoundException(Organization)
    projects = await ProjectRepo.list_by_organization_id(organization_id=org.id, db_session=db_session)
    projects = [proj.model_dump() for proj in projects]
    role_name = await OrganizationMemberRepo.get_member_role(
        identity_id=identity_id, organization_id=org.id, db_session=db_session
    )
    owner = await OrganizationMemberRepo.get_organization_owner(organization_id=org.id, db_session=db_session)
    if owner:
        owner_email = owner.identity.email if owner.identity else None

    return IOrganizationDetailsRead(**org.model_dump(), role=role_name, owner=owner_email, projects=projects)
