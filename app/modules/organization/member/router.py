from typing import Annotated

from fastapi import APIRouter, Depends
from fastapi_pagination import Params
from sqlmodel import select

from app.exceptions import NotFoundException
from app.modules.identity.crud import IdentityRepo
from app.modules.organization.crud import OrganizationRepo
from app.modules.organization.member.crud import OrganizationMemberRepo
from app.modules.organization.member.model import OrganizationMember
from app.modules.organization.member.schema import IOrganizationMemberRead
from app.modules.organization.model import Organization
from app.modules.permission.deps import need_org_permission
from app.modules.role.crud import role
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IGetResponsePaginated, IOrderEnum
from app.services.db.deps import DatabaseSessionDep

resource = "organization"
CAN_READ = need_org_permission(Action.READ, resource)

router = APIRouter(prefix="/organizations/{organization_id}/members", tags=["organization-members"])


@router.get("/")
async def list_organization_members(
    *,
    identity=CAN_READ,
    organization_id: str,
    params: Annotated[Params, Depends()],
    order_by: str | None = "id",
    order: IOrderEnum | None = IOrderEnum.ascendent,
    db_session: DatabaseSessionDep,
):
    """List all members of an organization"""
    # Verify organization exists
    organization = await OrganizationRepo.get(model_id=organization_id, db_session=db_session)
    if not organization:
        raise NotFoundException(Organization)

    # Get all members for this organization
    query = select(OrganizationMember).where(OrganizationMember.organization_id == organization_id)
    members = await OrganizationMemberRepo.list_multi_paginated_ordered(
        query=query, params=params, order=order, order_by=order_by, db_session=db_session
    )

    # Build response with member details
    member_responses: list[IOrganizationMemberRead] = []
    for member in members.items:
        # Get identity details
        identity_obj = await IdentityRepo.get(model_id=member.identity_id, db_session=db_session)

        # Get role details
        role_obj = await role.get(model_id=member.role_id, db_session=db_session)

        member_response = IOrganizationMemberRead(
            id=member.id,
            identity_id=member.identity_id,
            role_id=member.role_id,
            organization_id=member.organization_id,
            is_active=member.is_active,
            invitation_id=member.invitation_id,
            joined_at=member.joined_at,
            created_at=member.created_at,
            updated_at=member.updated_at,
            identity_name=identity_obj.name if identity_obj else None,
            identity_email=identity_obj.email if identity_obj else None,
            role_name=role_obj.name if role_obj else "",
        )
        member_responses.append(member_response)
    resp = IGetResponsePaginated.create(items=member_responses, total=members.total, params=params)

    return resp
