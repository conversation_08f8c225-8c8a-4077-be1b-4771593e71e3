from datetime import datetime

from pydantic import BaseModel


class IOrganizationMemberRead(BaseModel):
    id: str
    identity_id: str
    role_id: str
    organization_id: str
    is_active: bool
    invitation_id: str | None = None
    joined_at: datetime | None = None
    created_at: datetime
    updated_at: datetime
    identity_name: str | None = None
    identity_email: str | None = None
    role_name: str


class IOrganizationMemberCreate(BaseModel):
    organization_id: str
    identity_id: str
    role_id: str
    is_active: bool = True
    invitation_id: str | None = None
    joined_at: datetime | None = None


class IOrganizationMemberUpdate(BaseModel):
    role_id: str | None = None
    is_active: bool | None = None
    joined_at: datetime | None = None
