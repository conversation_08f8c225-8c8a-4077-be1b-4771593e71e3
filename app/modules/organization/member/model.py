from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import DateTime, Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity.model import Identity
    from app.modules.invitation.models import Invitation
    from app.modules.role.models import Role

    from ..model import Organization


class OrganizationMember(BaseModelMixin, table=True):
    __tablename__ = "organization_members"
    __table_args__ = (UniqueConstraint("organization_id", "identity_id"),)

    organization_id: str = Field(foreign_key="organizations.id", nullable=False)
    identity_id: str = Field(foreign_key="identities.id", nullable=False)
    role_id: str = Field(foreign_key="roles.id", nullable=False)

    is_active: bool = Field(default=True)
    invitation_id: str | None = Field(foreign_key="invitations.id", nullable=True)
    joined_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))

    organization: "Organization" = Relationship(back_populates="members")
    identity: "Identity" = Relationship(back_populates="organizations")
    role: "Role" = Relationship()
    invitation: Optional["Invitation"] = Relationship()
