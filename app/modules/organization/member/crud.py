from fastapi_pagination import Params
from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.role.crud import role
from app.modules.role.models import Role
from app.modules.shared.base_crud import CRUDBase
from app.modules.shared.base_schema import IOrderEnum
from app.services.db.session import AsyncSession

from .model import OrganizationMember
from .schema import IOrganizationMemberCreate, IOrganizationMemberUpdate


class CRUDOrganizationMember(CRUDBase[OrganizationMember, IOrganizationMemberCreate, IOrganizationMemberUpdate]):
    async def create(self, *, new: IOrganizationMemberCreate, db_session: AsyncSession) -> OrganizationMember:
        # Create the organization member record
        organization_member = await super().create(new=new, db_session=db_session)

        return organization_member

    async def update(
        self, *, db_obj: OrganizationMember, new: IOrganizationMemberUpdate, db_session: AsyncSession
    ) -> OrganizationMember:
        if new.role_id and new.role_id != db_obj.role_id:
            # Get old role name
            old_role_stmt = select(Role).where(Role.id == db_obj.role_id)
            old_role_result = await db_session.execute(old_role_stmt)
            old_role_result.scalar_one_or_none()

            # Get new role name
            new_role_stmt = select(Role).where(Role.id == new.role_id)
            new_role_result = await db_session.execute(new_role_stmt)
            new_role_result.scalar_one_or_none()

        if new.is_active is False and db_obj.is_active is True:
            role_stmt = select(Role).where(Role.id == db_obj.role_id)
            role_result = await db_session.execute(role_stmt)
            role_result.scalar_one_or_none()

        if new.is_active is True and db_obj.is_active is False:
            role_stmt = select(Role).where(Role.id == db_obj.role_id)
            role_result = await db_session.execute(role_stmt)
            role_result.scalar_one_or_none()

        return await super().update(db_obj=db_obj, new=new, db_session=db_session)

    async def remove(self, *, id: str, db_session: AsyncSession) -> OrganizationMember:
        # Get the organization member before deletion
        organization_member = await self.get(model_id=id, db_session=db_session)
        if not organization_member:
            raise ValueError(f"OrganizationMember with id {id} not found")

        role_stmt = select(Role).where(Role.id == organization_member.role_id)
        role_result = await db_session.execute(role_stmt)
        role_result.scalar_one_or_none()

        # Remove the organization member record
        return await super().remove(id=id, db_session=db_session)

    async def list_by_organization_id(
        self,
        *,
        organization_id: str,
        params: Params | None = Params(),
        order_by: str | None = "id",
        order: IOrderEnum | None = IOrderEnum.ascendent,
        db_session: AsyncSession,
    ) -> list[OrganizationMember]:
        stmt = select(OrganizationMember).where(OrganizationMember.organization_id == organization_id)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_identity_id(self, *, identity_id: str, db_session: AsyncSession) -> list[OrganizationMember]:
        stmt = select(OrganizationMember).where(OrganizationMember.identity_id == identity_id)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_organization_and_identity(
        self, *, organization_id: str, identity_id: str, db_session: AsyncSession
    ) -> OrganizationMember | None:
        stmt = select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id, OrganizationMember.identity_id == identity_id
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_member_role(self, *, identity_id: str, organization_id: str, db_session: AsyncSession) -> str | None:
        stmt = (
            select(Role.name)
            .join(OrganizationMember, OrganizationMember.role_id == Role.id)
            .where(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.identity_id == identity_id,
                OrganizationMember.is_active.is_(True),
            )
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_members(self, *, organization_id: str, db_session: AsyncSession) -> list[OrganizationMember]:
        stmt = select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id, OrganizationMember.is_active is True
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_members_by_role(
        self, *, organization_id: str, role_id: str, db_session: AsyncSession
    ) -> list[OrganizationMember]:
        stmt = select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.role_id == role_id,
            OrganizationMember.is_active is True,
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def activate_member(
        self, *, organization_id: str, identity_id: str, db_session: AsyncSession
    ) -> OrganizationMember | None:
        """Activate an organization member and add their role back"""

        organization_member = await self.get_by_organization_and_identity(
            organization_id=organization_id, identity_id=identity_id, db_session=db_session
        )
        if not organization_member:
            return None

        # Update to active
        update_data = IOrganizationMemberUpdate(is_active=True)
        return await self.update(db_obj=organization_member, new=update_data, db_session=db_session)

    async def deactivate_member(
        self, *, organization_id: str, identity_id: str, db_session: AsyncSession
    ) -> OrganizationMember | None:
        """Deactivate an organization member and remove their role"""

        organization_member = await self.get_by_organization_and_identity(
            organization_id=organization_id, identity_id=identity_id, db_session=db_session
        )
        if not organization_member:
            return None

        # Update to inactive
        update_data = IOrganizationMemberUpdate(is_active=False)
        return await self.update(db_obj=organization_member, new=update_data, db_session=db_session)

    async def change_member_role(
        self, *, organization_id: str, identity_id: str, new_role_id: str, db_session: AsyncSession
    ) -> OrganizationMember | None:
        """Change an organization member's role"""

        organization_member = await self.get_by_organization_and_identity(
            organization_id=organization_id, identity_id=identity_id, db_session=db_session
        )
        if not organization_member:
            return None

        # Update role
        update_data = IOrganizationMemberUpdate(role_id=new_role_id)
        return await self.update(db_obj=organization_member, new=update_data, db_session=db_session)

    async def list_roles_by_identity_id(
        self, *, identity_id: str, organization_id: str, db_session: AsyncSession
    ) -> list[Role]:
        """Get all roles for an active organization member by identity ID and organization ID."""
        stmt = (
            select(Role)
            .join(OrganizationMember, OrganizationMember.role_id == Role.id)
            .where(
                OrganizationMember.identity_id == identity_id,
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.is_active == True,
            )
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_organization_owner(
        self, *, organization_id: str, db_session: AsyncSession
    ) -> OrganizationMember | None:
        """Get the owner of the organization."""
        stmt = (
            select(OrganizationMember)
            .options(selectinload(OrganizationMember.identity))
            .where(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.role_id == role.org_owner.id,
                OrganizationMember.is_active.is_(True),
            )
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()


OrganizationMemberRepo = CRUDOrganizationMember(OrganizationMember)
