from fastapi import APIRouter

from app.exceptions import NotFoundException
from app.modules.organization.crud import OrganizationRepo
from app.modules.organization.model import Organization
from app.modules.organization.schema import (
    IOrganizationCreate,
    IOrganizationDetailsRead,
    IOrganizationUpdate,
)
from app.modules.organization.usecases import (
    create_org_with_defaults,
    delete_org_soft_cascade,
    get_org_with_role_and_owner,
)
from app.modules.permission.deps import need_any_identity, need_org_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import (
    IDeleteResponseBase,
    IGetResponseBase,
    IPatchResponseBase,
    IPostResponseBase,
    create_response,
)
from app.services.db.deps import DatabaseSessionDep

resource = "organization"
CAN_CREATE = need_any_identity()
CAN_READ = need_org_permission(Action.READ, resource)
CAN_UPDATE = need_org_permission(Action.UPDATE, resource)
CAN_DELETE = need_org_permission(Action.DELETE, resource)

router = APIRouter(prefix="/organizations", tags=["organizations"])


@router.post("/")
async def create_organization(
    *,
    identity=CAN_CREATE,
    organization_create: IOrganizationCreate,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IOrganizationDetailsRead]:
    """Create a new organization"""
    new_org, _default_proj = await create_org_with_defaults(
        identity_id=identity.id,
        identity_slug=identity.slug,
        org_params=organization_create,
        db_session=db_session,
    )
    await db_session.flush()
    org_with_role = await get_org_with_role_and_owner(identity.id, new_org, db_session=db_session)
    return create_response(data=org_with_role, message="Organization created successfully")


@router.get("/{organization_id}")
async def get_organization(
    *,
    identity=CAN_READ,
    organization_id: str,
    db_session: DatabaseSessionDep,
) -> IGetResponseBase[IOrganizationDetailsRead]:
    """Get organization by id"""
    org = await OrganizationRepo.get(model_id=organization_id, db_session=db_session)

    org_with_role = await get_org_with_role_and_owner(identity.id, org, db_session=db_session)

    return create_response(data=org_with_role, message="Organization retrieved successfully")


@router.patch("/{organization_id}")
async def update_organization(
    *,
    identity=CAN_UPDATE,
    organization_id: str,
    organization_update: IOrganizationUpdate,
    db_session: DatabaseSessionDep,
) -> IPatchResponseBase[IOrganizationDetailsRead]:
    """Update organization by id"""
    organization = await OrganizationRepo.get(model_id=organization_id, db_session=db_session)

    if not organization:
        raise NotFoundException(Organization)

    # Update the organization
    updated_org = await OrganizationRepo.update(current=organization, new=organization_update, db_session=db_session)
    org_with_role = await get_org_with_role_and_owner(identity.id, updated_org, db_session=db_session)
    return create_response(data=org_with_role, message="Organization updated successfully")


@router.delete("/{organization_id}")
async def delete_organization(
    *,
    identity=CAN_DELETE,
    organization_id: str,
    db_session: DatabaseSessionDep,
) -> IDeleteResponseBase[IOrganizationDetailsRead]:
    """Delete organization by id (soft delete)"""
    # Get the organization first
    organization = await OrganizationRepo.get(model_id=organization_id, db_session=db_session)

    if not organization:
        raise NotFoundException(Organization)

    # Soft delete the organization
    deleted_org = await delete_org_soft_cascade(organization_id, db_session=db_session)
    org_with_role = await get_org_with_role_and_owner(identity.id, deleted_org, db_session=db_session)
    return create_response(data=org_with_role, message="Organization deleted successfully")
