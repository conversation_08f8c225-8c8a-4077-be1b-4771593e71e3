from datetime import datetime
from typing import TYPE_CHECKING

from sqlmodel import Column, DateTime, Field, ForeignKey, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from .member import OrganizationMember
    from .project.model import Project
    from .subscription.model import OrganizationSubscription


class OrganizationBase(BaseModelMixin):
    slug: str = Field(unique=True)
    name: str | None = None
    description: str | None = None
    status: str = Field(default="active")

    data_location: str = Field(default="us-east-1")
    currency: str = Field(default="USD")


class Organization(OrganizationBase, table=True):
    __tablename__ = "organizations"

    current_subscription_id: str | None = Field(
        default=None, sa_column=Column(ForeignKey("organization_subscriptions.id", use_alter=True), nullable=True)
    )

    created_by: str = Field(foreign_key="identities.id", nullable=False)

    deleted_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))

    projects: list["Project"] = Relationship(back_populates="organization")
    members: list["OrganizationMember"] = Relationship(back_populates="organization")
    current_subscription: "OrganizationSubscription" = Relationship(
        sa_relationship_kwargs={"foreign_keys": "Organization.current_subscription_id"}
    )
    subscriptions: list["OrganizationSubscription"] = Relationship(
        back_populates="organization",
        sa_relationship_kwargs={"foreign_keys": "OrganizationSubscription.organization_id"},
    )
