from datetime import datetime
from typing import TYPE_CHECKING

from sqlmodel import DateTime, Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.conversation import Conversation
    from app.modules.organization.model import Organization

    from .app.model import ProjectApp
    from .member.model import ProjectMember
    from .memory_category.model import ProjectMemoryCategory


class ProjectBase(BaseModelMixin):
    slug: str = Field(unique=False)
    name: str | None = None
    description: str | None = None
    data_location: str | None = Field(default=None)
    status: str = Field(default="active")  # e.g., active, archived, deleted
    environment: str = Field(default="production")  # optional: "staging", "dev" etc.


class Project(ProjectBase, table=True):
    __tablename__ = "projects"
    __table_args__ = (UniqueConstraint("slug", "organization_id"),)

    organization_id: str = Field(foreign_key="organizations.id", nullable=False)
    organization: "Organization" = Relationship(back_populates="projects")
    created_by: str = Field(foreign_key="identities.id", nullable=False)

    deleted_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))

    app: "ProjectApp" = Relationship(back_populates="project")
    members: list["ProjectMember"] = Relationship(back_populates="project")
    memory_categories: list["ProjectMemoryCategory"] = Relationship(back_populates="project")
    conversations: list["Conversation"] = Relationship(back_populates="project")
