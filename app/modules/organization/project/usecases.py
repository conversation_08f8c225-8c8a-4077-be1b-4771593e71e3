"""Project usecase functions."""

from app.modules.role.crud import role
from app.services.db.session import AsyncSession
from app.utils import slug

from .app.crud import project_app
from .crud import ProjectRepo
from .member.crud import ProjectMemberRepo
from .member.schema import IProjectMemberCreate
from .memory_category.crud import ProjectMemCategoryRepo
from .model import Project
from .schema import IProjectCreate, IProjectDetailsRead


async def _generate_uniq_proj_slug(
    org_slug: str,
    organization_id: str,
    db_session: AsyncSession,
) -> str:
    existing_slugs = await ProjectRepo.list_slugs_by_organization(organization_id=organization_id, session=db_session)
    base_slug = f"{org_slug}-proj"
    return slug.generate_uniq_slug(base_slug, existing_slugs)


async def create_proj_with_defaults(
    identity_id: str,
    organization_id: str,
    organization_slug: str,
    project_params: IProjectCreate,
    db_session: AsyncSession,
) -> Project:
    """create project with project member and project app"""
    slug = await _generate_uniq_proj_slug(
        org_slug=organization_slug, organization_id=organization_id, db_session=db_session
    )

    project_obj = Project(
        **project_params.model_dump(),
        slug=slug,
        organization_id=organization_id,
        created_by=identity_id,
    )
    new_project = await ProjectRepo.create(new=project_obj, db_session=db_session)
    await db_session.flush()

    owner_role = role.proj_owner
    if owner_role:
        member_create = IProjectMemberCreate(project_id=new_project.id, identity_id=identity_id, role_id=owner_role.id)
        await ProjectMemberRepo.create(new=member_create, db_session=db_session)

    # Create the project app
    await project_app.create_for_project(
        project_id=new_project.id, project_slug=new_project.slug, db_session=db_session
    )
    # Create the project memory category
    await ProjectMemCategoryRepo.create_default_categories(project_id=new_project.id, db_session=db_session)
    await db_session.flush()

    return new_project


async def get_project_with_details(
    identity_id: str,
    project: Project,
    db_session: AsyncSession,
) -> Project:
    """Get project with its details including apps and members."""
    project = await ProjectRepo.get(model_id=project.id, db_session=db_session)
    if not project:
        raise ValueError(f"Project with id {project_id} not found")

    role_name = await ProjectMemberRepo.get_member_role(
        identity_id=identity_id, project_id=project.id, db_session=db_session
    )
    owner = await ProjectMemberRepo.get_project_owner(project_id=project.id, db_session=db_session)
    if owner:
        owner_email = owner.identity.email if owner.identity else None

    return IProjectDetailsRead(**project.model_dump(), role=role_name, owner=owner_email)
