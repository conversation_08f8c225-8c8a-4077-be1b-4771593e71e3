from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.role.crud import role
from app.modules.role.models import Role
from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import ProjectMember
from .schema import IProjectMemberCreate, IProjectMemberUpdate


class CRUDProjectMember(CRUDBase[ProjectMember, IProjectMemberCreate, IProjectMemberUpdate]):
    async def create(self, *, new: IProjectMemberCreate, db_session: AsyncSession) -> ProjectMember:
        # Create the project member record
        project_member = await super().create(new=new, db_session=db_session)

        return project_member

    async def update(
        self, *, db_obj: ProjectMember, new: IProjectMemberUpdate, db_session: AsyncSession
    ) -> ProjectMember:
        if new.role_id and new.role_id != db_obj.role_id:
            # Get old role name
            old_role_stmt = select(Role).where(Role.id == db_obj.role_id)
            old_role_result = await db_session.execute(old_role_stmt)
            old_role_result.scalar_one_or_none()

            # Get new role name
            new_role_stmt = select(Role).where(Role.id == new.role_id)
            new_role_result = await db_session.execute(new_role_stmt)
            new_role_result.scalar_one_or_none()

        if new.is_active is False and db_obj.is_active is True:
            role_stmt = select(Role).where(Role.id == db_obj.role_id)
            role_result = await db_session.execute(role_stmt)
            role_result.scalar_one_or_none()

        if new.is_active is True and db_obj.is_active is False:
            role_stmt = select(Role).where(Role.id == db_obj.role_id)
            role_result = await db_session.execute(role_stmt)
            role_result.scalar_one_or_none()

        return await super().update(db_obj=db_obj, new=new, db_session=db_session)

    async def remove(self, *, id: str, db_session: AsyncSession) -> ProjectMember:
        # Get the project member before deletion
        project_member = await self.get(model_id=id, db_session=db_session)
        if not project_member:
            raise ValueError(f"ProjectMember with id {id} not found")

        role_stmt = select(Role).where(Role.id == project_member.role_id)
        role_result = await db_session.execute(role_stmt)
        role_result.scalar_one_or_none()

        # Remove the project member record
        return await super().remove(id=id, db_session=db_session)

    async def get_by_project_id(self, *, project_id: str, db_session: AsyncSession) -> list[ProjectMember]:
        stmt = select(ProjectMember).where(ProjectMember.project_id == project_id)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_identity_id(self, *, identity_id: str, db_session: AsyncSession) -> list[ProjectMember]:
        stmt = select(ProjectMember).where(ProjectMember.identity_id == identity_id)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_project_and_identity(
        self, *, project_id: str, identity_id: str, db_session: AsyncSession
    ) -> ProjectMember | None:
        stmt = select(ProjectMember).where(
            ProjectMember.project_id == project_id, ProjectMember.identity_id == identity_id
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_members(self, *, project_id: str, db_session: AsyncSession) -> list[ProjectMember]:
        stmt = select(ProjectMember).where(ProjectMember.project_id == project_id, ProjectMember.is_active is True)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_members_by_role(
        self, *, project_id: str, role_id: str, db_session: AsyncSession
    ) -> list[ProjectMember]:
        stmt = select(ProjectMember).where(
            ProjectMember.project_id == project_id, ProjectMember.role_id == role_id, ProjectMember.is_active is True
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def activate_member(
        self, *, project_id: str, identity_id: str, db_session: AsyncSession
    ) -> ProjectMember | None:
        """Activate a project member and add their role back"""

        project_member = await self.get_by_project_and_identity(
            project_id=project_id, identity_id=identity_id, db_session=db_session
        )
        if not project_member:
            return None

        # Update to active
        update_data = IProjectMemberUpdate(is_active=True)
        return await self.update(db_obj=project_member, new=update_data, db_session=db_session)

    async def deactivate_member(
        self, *, project_id: str, identity_id: str, db_session: AsyncSession
    ) -> ProjectMember | None:
        """Deactivate a project member and remove their role"""

        project_member = await self.get_by_project_and_identity(
            project_id=project_id, identity_id=identity_id, db_session=db_session
        )
        if not project_member:
            return None

        # Update to inactive
        update_data = IProjectMemberUpdate(is_active=False)
        return await self.update(db_obj=project_member, new=update_data, db_session=db_session)

    async def change_member_role(
        self, *, project_id: str, identity_id: str, new_role_id: str, db_session: AsyncSession
    ) -> ProjectMember | None:
        """Change a project member's role"""

        project_member = await self.get_by_project_and_identity(
            project_id=project_id, identity_id=identity_id, db_session=db_session
        )
        if not project_member:
            return None

        # Update role
        update_data = IProjectMemberUpdate(role_id=new_role_id)
        return await self.update(db_obj=project_member, new=update_data, db_session=db_session)

    async def list_roles_by_identity_id(
        self, *, identity_id: str, project_id: str, db_session: AsyncSession
    ) -> list[Role]:
        """Get all roles for an active project member by identity ID and project ID."""
        stmt = (
            select(Role)
            .join(ProjectMember, ProjectMember.role_id == Role.id)
            .where(
                ProjectMember.identity_id == identity_id,
                ProjectMember.project_id == project_id,
                ProjectMember.is_active == True,
            )
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def get_member_role(self, *, identity_id: str, project_id: str, db_session: AsyncSession) -> str | None:
        """Get the role name of a project member by identity ID and project ID."""
        stmt = (
            select(Role.name)
            .join(ProjectMember, ProjectMember.role_id == Role.id)
            .where(
                ProjectMember.identity_id == identity_id,
                ProjectMember.project_id == project_id,
                ProjectMember.is_active == True,
            )
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_project_owner(self, *, project_id: str, db_session: AsyncSession) -> ProjectMember | None:
        """Get the owner of the project."""
        stmt = (
            select(ProjectMember)
            .options(selectinload(ProjectMember.identity))
            .where(
                ProjectMember.project_id == project_id,
                ProjectMember.role_id == role.proj_owner.id,
                ProjectMember.is_active.is_(True),
            )
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()


ProjectMemberRepo = CRUDProjectMember(ProjectMember)
