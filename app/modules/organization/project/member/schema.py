from datetime import datetime

from pydantic import BaseModel


class IProjectMemberRead(BaseModel):
    id: str
    identity_id: str
    role_name: str
    project_id: str
    is_active: bool
    invitation_id: str | None = None
    created_at: datetime
    updated_at: datetime
    identity_name: str | None = None
    identity_email: str | None = None
    role_name: str


class IProjectMemberCreate(BaseModel):
    project_id: str
    identity_id: str
    role_id: str
    invitation_id: str | None = None


class IProjectMemberUpdate(BaseModel):
    role_id: str | None = None
    is_active: bool | None = None
