from typing import TYPE_CHECKING, Optional

from sqlmodel import Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity.model import Identity
    from app.modules.invitation.models import Invitation
    from app.modules.organization.project.model import Project
    from app.modules.role.models import Role


class ProjectMember(BaseModelMixin, table=True):
    __tablename__ = "project_members"
    __table_args__ = (UniqueConstraint("project_id", "identity_id"),)

    project_id: str = Field(foreign_key="projects.id", nullable=False)
    identity_id: str = Field(foreign_key="identities.id", nullable=False)
    role_id: str = Field(foreign_key="roles.id", nullable=False)

    is_active: bool = Field(default=True)
    invitation_id: str | None = Field(foreign_key="invitations.id", nullable=True)

    project: "Project" = Relationship(back_populates="members")
    identity: "Identity" = Relationship(back_populates="projects")
    role: "Role" = Relationship()
    invitation: Optional["Invitation"] = Relationship()
