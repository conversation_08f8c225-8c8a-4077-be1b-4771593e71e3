from typing import Annotated

from fastapi import APIRouter, Depends
from fastapi_pagination import Params
from sqlmodel import select

from app.exceptions import NotFoundException
from app.modules.identity.crud import IdentityRepo
from app.modules.organization.project.crud import ProjectRepo
from app.modules.organization.project.member.crud import ProjectMemberRepo
from app.modules.organization.project.member.model import ProjectMember
from app.modules.organization.project.member.schema import IProjectMemberRead
from app.modules.organization.project.model import Project
from app.modules.permission.deps import need_proj_permission
from app.modules.role.crud import role
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IGetResponsePaginated, IOrderEnum
from app.services.db.deps import DatabaseSessionDep

resource = "project"
CAN_READ = need_proj_permission(Action.READ, resource)

router = APIRouter(prefix="/projects/{project_id}/members", tags=["project-members"])


@router.get("/")
async def list_project_members(
    *,
    identity=CAN_READ,
    project_id: str,
    params: Annotated[Params, Depends()],
    order_by: str | None = "id",
    order: IOrderEnum | None = IOrderEnum.ascendent,
    db_session: DatabaseSessionDep,
):
    """List all members of a project"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Get all members for this project
    query = select(ProjectMember).where(ProjectMember.project_id == project_id)
    members = await ProjectMemberRepo.list_multi_paginated_ordered(
        query=query, params=params, order=order, order_by=order_by, db_session=db_session
    )

    # Build response with member details
    member_responses: list[IProjectMemberRead] = []
    for member in members.items:
        # Get identity details
        identity_obj = await IdentityRepo.get(model_id=member.identity_id, db_session=db_session)

        # Get role details
        role_obj = await role.get(model_id=member.role_id, db_session=db_session)

        member_response = IProjectMemberRead(
            id=member.id,
            identity_id=member.identity_id,
            role_id=member.role_id,
            project_id=member.project_id,
            is_active=member.is_active,
            invitation_id=member.invitation_id,
            created_at=member.created_at,
            updated_at=member.updated_at,
            identity_name=identity_obj.name if identity_obj else None,
            identity_email=identity_obj.email if identity_obj else None,
            role_name=role_obj.name if role_obj else "",
        )
        member_responses.append(member_response)
    resp = IGetResponsePaginated.create(items=member_responses, total=members.total, params=params)

    return resp
