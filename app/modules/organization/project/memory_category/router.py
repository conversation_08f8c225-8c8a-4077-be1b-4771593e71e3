from fastapi import APIRouter, HTTPException
from fastapi_pagination import Params
from sqlmodel import select

from app.exceptions import NotFoundException
from app.modules.organization.project.crud import ProjectRepo
from app.modules.organization.project.memory_category.crud import ProjectMemCategoryRepo
from app.modules.organization.project.memory_category.model import ProjectMemoryCategory
from app.modules.organization.project.memory_category.schema import (
    IMemoryCategoryCreate,
    IMemoryCategoryRead,
    IMemoryCategoryUpdate,
)
from app.modules.organization.project.model import Project
from app.modules.permission.deps import need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import (
    IDeleteResponseBase,
    IGetResponseBase,
    IGetResponsePaginated,
    IOrderEnum,
    IPostResponseBase,
    IPutResponseBase,
    create_response,
)
from app.services.db.deps import DatabaseSessionDep
from config.loader import SettingsFactory

resource = "project"
CAN_READ = need_proj_permission(Action.READ, resource)
CAN_CREATE = need_proj_permission(Action.CREATE, resource)
CAN_UPDATE = need_proj_permission(Action.UPDATE, resource)
CAN_DELETE = need_proj_permission(Action.DELETE, resource)

router = APIRouter(prefix="/projects/{project_id}/memory-categories", tags=["project-memory-categories"])


@router.get("/", response_model=IGetResponsePaginated[IMemoryCategoryRead])
async def list_memory_categories(
    *,
    identity=CAN_READ,
    project_id: str,
    category_type: str | None = None,
    params=Params(),
    order_by: str | None = "id",
    order: IOrderEnum | None = IOrderEnum.ascendent,
    db_session: DatabaseSessionDep,
):
    """List all memory categories for a project"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Build query
    query = select(ProjectMemoryCategory).where(ProjectMemoryCategory.project_id == project_id)

    if category_type:
        query = query.where(ProjectMemoryCategory.type == category_type)
    # Get paginated results
    categories = await ProjectMemCategoryRepo.list_multi_paginated_ordered(
        query=query, params=params, order=order, order_by=order_by, db_session=db_session
    )

    return create_response(data=categories, message="Memory categories retrieved successfully")


@router.get("/{category_id}", response_model=IGetResponseBase[IMemoryCategoryRead])
async def get_memory_category(
    *,
    identity=CAN_READ,
    project_id: str,
    category_id: str,
    db_session: DatabaseSessionDep,
):
    """Get a specific memory category"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Get category
    category = await ProjectMemCategoryRepo.get(model_id=category_id, db_session=db_session)
    if not category or category.project_id != project_id:
        raise NotFoundException(ProjectMemoryCategory)

    return create_response(data=category, message="Memory category retrieved successfully")


@router.post("/", response_model=IPostResponseBase[IMemoryCategoryRead])
async def create_memory_category(
    *,
    identity=CAN_CREATE,
    project_id: str,
    category_in: IMemoryCategoryCreate,
    db_session: DatabaseSessionDep,
):
    """Create a new memory category"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Check if category already exists
    existing = await ProjectMemCategoryRepo.get_by_project_and_category(
        project_id=project_id,
        name=category_in.name,
        # type=category_in.type,
        session=db_session,
    )
    if existing:
        raise HTTPException(status_code=400, detail="Memory category already exists")
    category_in.project_id = project_id
    if category_in.config is None:
        category_in.config = {}
    if "context_length" not in category_in.config:
        settings = SettingsFactory.settings()
        category_in.config["context_length"] = settings.DEFAULT_SUMMARY_LENGTH
    # Create category
    category = await ProjectMemCategoryRepo.create(new=category_in, db_session=db_session)
    return create_response(data=category, message="Memory category created successfully")


@router.patch("/{category_id}", response_model=IPutResponseBase[IMemoryCategoryRead])
async def update_memory_category(
    *,
    identity=CAN_UPDATE,
    project_id: str,
    category_id: str,
    category_in: IMemoryCategoryUpdate,
    db_session: DatabaseSessionDep,
):
    """Update a memory category"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Get category
    category = await ProjectMemCategoryRepo.get(model_id=category_id, db_session=db_session)
    if not category or category.project_id != project_id:
        raise NotFoundException(ProjectMemoryCategory)

    if category.config is not None and category_in.config is not None:
        category_in.config = category.config | category_in.config
    # Update category
    category = await ProjectMemCategoryRepo.update(current=category, new=category_in, db_session=db_session)
    return create_response(data=category, message="Memory category updated successfully")


@router.delete("/{category_id}", response_model=IDeleteResponseBase)
async def delete_memory_category(
    *,
    identity=CAN_DELETE,
    project_id: str,
    category_id: str,
    db_session: DatabaseSessionDep,
):
    """Delete a memory category"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Get category
    category = await ProjectMemCategoryRepo.get(model_id=category_id, db_session=db_session)
    if not category or category.project_id != project_id:
        raise NotFoundException(ProjectMemoryCategory)

    # Delete category
    data = await ProjectMemCategoryRepo.delete(model_id=category_id, db_session=db_session)
    return create_response(data=data, message="Memory category deleted successfully")


@router.put("/{category_id}/deactivate", response_model=IPutResponseBase[IMemoryCategoryRead])
async def deactivate_memory_category(
    *,
    identity=CAN_UPDATE,
    project_id: str,
    category_id: str,
    db_session: DatabaseSessionDep,
):
    """Deactivate a memory category"""
    # Verify project exists
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
    if not project:
        raise NotFoundException(Project)

    # Get category
    category = await ProjectMemCategoryRepo.get(model_id=category_id, db_session=db_session)
    if not category or category.project_id != project_id:
        raise NotFoundException(ProjectMemoryCategory)

    # Deactivate
    update_data = IMemoryCategoryUpdate(is_active=False)
    category = await ProjectMemCategoryRepo.update(model_id=category_id, obj_in=update_data, db_session=db_session)

    return create_response(data=category, message="Memory category deactivated successfully")
