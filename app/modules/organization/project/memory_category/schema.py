from pydantic import BaseModel

from .model import ProjectMemoryCategoryBase


class IMemoryCategoryRead(ProjectMemoryCategoryBase):
    id: str


class IMemoryCategoryCreate(BaseModel):
    project_id: str = None
    name: str
    type: str = "auto"  # 'available' or 'default'
    description: str | None = None
    is_active: bool = True
    config: dict | None = None


class IMemoryCategoryUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    is_active: bool | None = None
    config: dict | None = None
