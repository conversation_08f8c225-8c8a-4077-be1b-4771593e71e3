from enum import Enum

from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import ProjectMemoryCategory
from .schema import IMemoryCategoryCreate, IMemoryCategoryUpdate


class Context(Enum):
    RAG = "rag"
    ALL = "all"


DEFAULT_CONFIG = {
    "context": Context.RAG.value,
    "context_length": -1,
}


class ProjectMemCategoryRepo(CRUDBase[ProjectMemoryCategory, IMemoryCategoryCreate, IMemoryCategoryUpdate]):
    async def get_by_project_id(
        self,
        *,
        project_id: str,
        category_type: str | None = None,
        include_inactive: bool = False,
        session: AsyncSession,
    ) -> list[ProjectMemoryCategory]:
        """Get memory categories for a project, optionally filtered by type"""

        stmt = select(ProjectMemoryCategory).where(ProjectMemoryCategory.project_id == project_id)

        if category_type:
            stmt = stmt.where(ProjectMemoryCategory.type == category_type)

        if not include_inactive:
            stmt = stmt.where(ProjectMemoryCategory.is_active is True)

        result = await session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_project_and_category(
        self,
        *,
        project_id: str,
        name: str,
        session: AsyncSession,
    ) -> ProjectMemoryCategory | None:
        """Get a specific category by project, name and type"""

        stmt = select(ProjectMemoryCategory).where(
            ProjectMemoryCategory.project_id == project_id,
            ProjectMemoryCategory.name == name,
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_categories(
        self, *, project_id: str, include_inactive: bool = False, db_session: AsyncSession
    ) -> list[ProjectMemoryCategory]:
        """Get active categories for a project"""

        stmt = select(ProjectMemoryCategory).where(ProjectMemoryCategory.project_id == project_id)
        if not include_inactive:
            stmt = stmt.where(ProjectMemoryCategory.is_active.is_(True))
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def create_default_categories(
        self,
        *,
        project_id: str,
        db_session: AsyncSession,
    ) -> list[ProjectMemoryCategory]:
        """Create a default memory category"""
        default_profile = ProjectMemoryCategory(
            project_id=project_id,
            name="profile",
            type="auto",
            description="Default profile category",
            config=DEFAULT_CONFIG,
        )
        default_event = ProjectMemoryCategory(
            project_id=project_id,
            name="event",
            type="manual",
            description="Default event category",
            config=DEFAULT_CONFIG,
        )
        profile = await self.create(
            new=default_profile,
            db_session=db_session,
        )
        event = await self.create(
            new=default_event,
            db_session=db_session,
        )
        return [profile, event]


ProjectMemCategoryRepo = ProjectMemCategoryRepo(ProjectMemoryCategory)
