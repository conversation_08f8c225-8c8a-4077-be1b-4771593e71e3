from typing import TYPE_CHECKING

from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlmodel import Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..model import Project


class ProjectMemoryCategoryBase(BaseModelMixin):
    project_id: str = Field(foreign_key="projects.id", nullable=False)
    name: str = Field(index=True)  # 'profile' or 'event'
    type: str = Field(default="auto", index=True)  # 'auto' or 'manual'
    config: dict | None = Field(default=None, sa_type=JSONB)
    description: str | None = Field(default=None)
    is_active: bool = Field(default=True)


class ProjectMemoryCategory(ProjectMemoryCategoryBase, table=True):
    __tablename__ = "project_memory_categories"
    __table_args__ = (UniqueConstraint("project_id", "name"),)

    project: "Project" = Relationship(back_populates="memory_categories")
