from pydantic import BaseModel

from app.modules.organization.project.model import ProjectBase
from app.modules.organization.schema import IOrganizationRead


class IProjectRead(ProjectBase):
    pass


class IProjectDetailsRead(IProjectRead):
    role: str | None = None
    owner: str | None = None


class IProjectReadWithOrg(IProjectRead):
    organization: IOrganizationRead


class IProjectCreate(BaseModel):
    name: str | None = None
    description: str | None = None
    data_location: str | None = None
    environment: str = "production"


class IProjectUpdate(BaseModel):
    slug: str | None = None
    name: str | None = None
    description: str | None = None
    data_location: str | None = None
    status: str | None = None
    environment: str | None = None
