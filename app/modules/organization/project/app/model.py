from typing import TYPE_CHECKING

from sqlmodel import Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.organization.project.app.api_key.model import ProjectAppApiKey
    from app.modules.organization.project.model import Project


class ProjectApp(BaseModelMixin, table=True):
    __tablename__ = "project_apps"

    project_id: str = Field(foreign_key="projects.id", nullable=False, unique=True)
    app_id: str = Field(nullable=False)
    app_secret: str = Field(nullable=False)

    project: "Project" = Relationship(back_populates="app")
    api_keys: list["ProjectAppApiKey"] = Relationship(back_populates="project_app")
