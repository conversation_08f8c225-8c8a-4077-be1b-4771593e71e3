import secrets
import string

from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from ..model import Project
from .model import ProjectApp
from .schema import IProjectAppCreate


def generate_app_id(project_slug: str) -> str:
    """Generate a human-readable app ID"""
    random_suffix = "".join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
    return f"{project_slug}-app-{random_suffix}"


def generate_app_secret() -> str:
    """Generate a secure app secret"""
    return secrets.token_urlsafe(32)


class CRUDProjectApp(CRUDBase[ProjectApp, IProjectAppCreate, IProjectAppCreate]):
    async def create_for_project(self, *, project_id: str, project_slug: str, db_session: AsyncSession) -> ProjectApp:
        """Create a ProjectApp with auto-generated credentials for a project"""
        app_id = generate_app_id(project_slug)
        app_secret = generate_app_secret()

        project_app_create = IProjectAppCreate(project_id=project_id, app_id=app_id, app_secret=app_secret)

        return await self.create(new=project_app_create, db_session=db_session)

    async def get_by_project_id(self, *, project_id: str, db_session: AsyncSession):
        stmt = select(ProjectApp).options(selectinload(ProjectApp.project)).where(ProjectApp.project_id == project_id)
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_project_slug(self, *, project_slug: str, db_session: AsyncSession):
        stmt = select(ProjectApp).join(Project).where(Project.slug == project_slug)
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()


project_app = CRUDProjectApp(ProjectApp)
