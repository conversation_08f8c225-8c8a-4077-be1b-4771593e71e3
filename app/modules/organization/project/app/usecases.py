"""Project app usecase functions."""

import hashlib

import pendulum
from loguru import logger

from app.services.db.session import AsyncSession
from app.utils.api_key import encrypt_api_key_data

from .api_key.crud import ProjectApiKeyRepo
from .api_key.model import Project<PERSON>pp<PERSON><PERSON><PERSON><PERSON>
from .api_key.schema import IProjectAppApiKeyCreate
from .model import Project<PERSON>pp


async def create_api_key_by_app(
    project_app: ProjectApp,
    created_by: str,
    db_session: AsyncSession,
    name: str | None = None,
    description: str | None = None,
    scopes: str | None = None,
    expires_at: str | None = None,
) -> ProjectAppApiKey:
    """Create an API key by app using encrypted token with readable information.

    Args:
        project_app: ProjectApp object (with project relationship loaded)
        created_by: Identity ID of the creator
        db_session: Database db_session
        name: Optional name for the API key
        description: Optional description
        scopes: Optional scopes
        expires_at: Optional expiration datetime

    Returns:
        Created API key object
    """

    logger.info(f"Creating API key for project_app: {project_app.id}, created_by: {created_by}")

    # Generate encrypted API key token
    created_at = pendulum.now().to_iso8601_string()
    logger.info(f"Generated timestamp: {created_at}")

    try:
        api_key_token = encrypt_api_key_data(
            app_id=project_app.app_id,
            project_id=project_app.project_id,
            created_at=created_at,
            expires_at=expires_at,
            secret=project_app.app_secret,  # Use app_secret as encryption secret
        )
        logger.info("API key token encrypted successfully")
    except Exception as e:
        logger.error(f"Error encrypting API key token: {e!s}")
        raise

    # Hash the encrypted token for secure authentication
    hashed_key = hashlib.sha256(api_key_token.encode()).hexdigest()

    # Create API key record
    logger.info("Creating API key database record")
    try:
        api_key_create = IProjectAppApiKeyCreate(
            organization_id=project_app.project.organization_id,
            project_id=project_app.project_id,
            project_app_id=project_app.id,
            created_by=created_by,
            hashed_key=hashed_key,
            encrypted_key=api_key_token,
            name=name,
            description=description,
            scopes=scopes,
            expires_at=expires_at,
        )
        logger.info("API key create object prepared")

        created_api_key = await ProjectApiKeyRepo.create(new=api_key_create, db_session=db_session)
        logger.info(f"API key created in database with ID: {created_api_key.id}")

        return created_api_key
    except Exception as e:
        logger.error(f"Error creating API key in database: {e!s}")
        raise
