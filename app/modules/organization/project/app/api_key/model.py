from datetime import datetime
from typing import TYPE_CHECKING

from sqlmodel import DateTime, Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.conversation.models import Conversation
    from app.modules.organization.project.app.model import ProjectApp


class ProjectAppApiKeyBase(BaseModelMixin):
    encrypted_key: str = Field(nullable=False)
    name: str | None = Field(default=None, max_length=128)
    description: str | None = Field(default=None, max_length=512)
    disabled: bool = Field(default=False)

    expires_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))
    last_used_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))


class ProjectAppApiKey(ProjectAppApiKeyBase, table=True):
    __tablename__ = "project_app_api_keys"

    created_by: str | None = Field(foreign_key="identities.id", default=None)
    organization_id: str = Field(foreign_key="organizations.id", index=True)

    project_id: str | None = Field(foreign_key="projects.id", index=True)

    project_app_id: str = Field(foreign_key="project_apps.id", index=True)

    hashed_key: str = Field(nullable=False, index=True, unique=True)

    scopes: str | None = Field(default=None, max_length=256)

    project_app: "ProjectApp" = Relationship(back_populates="api_keys")
    conversations: list["Conversation"] = Relationship(back_populates="api_key")
