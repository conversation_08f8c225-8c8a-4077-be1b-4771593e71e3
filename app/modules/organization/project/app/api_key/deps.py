from typing import Annotated

from fastapi import Depends
from fastapi.security import HTT<PERSON>uthorizationCredentials, <PERSON>TT<PERSON><PERSON><PERSON><PERSON>
from loguru import logger

from app.exceptions import UnauthenticatedException
from app.modules.organization.project import Project
from app.modules.organization.project.app.api_key.crud import ProjectApiKeyRepo
from app.services.db.deps import DatabaseSessionDep
from app.utils.api_key import decrypt_api_key_data

bearer_scheme = HTTPBearer(auto_error=False)
BearerDep = Annotated[HTTPAuthorizationCredentials | None, Depends(bearer_scheme)]


async def get_current_project(bearer: BearerDep, db_session: DatabaseSessionDep) -> tuple[str, str]:
    if bearer and (encrypted_key := bearer.credentials):
        try:
            api_key = await ProjectApiKeyRepo.get_by_encrypted_key(encrypted_key=encrypted_key, session=db_session)
            decoded_token = decrypt_api_key_data(encrypted_key, api_key.project_app.app_secret)
            project_id = decoded_token.get("project_id")

            return project_id, api_key.id
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise UnauthenticatedException(message="Invalid or expired token")
    else:
        raise UnauthenticatedException(message="Authentication required")


ApiKeyProjectDep = Annotated[Project, Depends(get_current_project)]
