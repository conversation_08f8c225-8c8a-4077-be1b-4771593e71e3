from typing import Annotated

from fastapi import APIRouter, Depends
from fastapi_pagination import Params
from loguru import logger

from app.exceptions import NotFoundException
from app.modules import cruds
from app.modules.organization.project.app import ProjectApp, ProjectAppApiKey
from app.modules.organization.project.app.api_key.schema import (
    IProjectAppApiKeyCreateRequest,
    IProjectAppApiKeyRead,
)
from app.modules.organization.project.app.usecases import create_api_key_by_app
from app.modules.permission.deps import need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import (
    IDeleteResponseBase,
    IGetResponsePaginated,
    IPostResponseBase,
    create_response,
)
from app.services.db.deps import DatabaseSessionDep

resource = "project_app_api_key"
CAN_CREATE = need_proj_permission(Action.CREATE, resource, need_email_verified=True)
CAN_DELETE = need_proj_permission(Action.DELETE, resource, need_email_verified=True)

router = APIRouter(prefix="/projects/{project_id}", tags=["project-api-keys"])


@router.post("/api-keys")
async def create_api_key(
    *,
    identity=CAN_CREATE,
    project_id: str,
    api_key_request: IProjectAppApiKeyCreateRequest,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IProjectAppApiKeyRead]:
    """Create a new encrypted API key for a project using project ID"""

    project_app = await cruds.project_app.get_by_project_id(project_id=project_id, db_session=db_session)

    logger.info(f"Project app found: {project_app is not None}")
    if project_app:
        logger.info(f"Project app details: id={project_app.id}, project_id={project_app.project_id}")

    if not project_app:
        logger.error(f"Project app not found for project_id: {project_id}")
        raise NotFoundException(message="Project app not found.")

    # Create encrypted API key using the usecase function
    logger.info("Calling create_api_key_by_app usecase")
    try:
        created_api_key = await create_api_key_by_app(
            project_app=project_app,
            created_by=identity.id,
            db_session=db_session,
            name=api_key_request.name,
            expires_at=api_key_request.expires_at,
        )
        logger.info(f"API key created successfully: {created_api_key.id}")
    except Exception as e:
        logger.error(f"Error creating API key: {e!s}")
        raise

    # Create response with the required fields
    response = IProjectAppApiKeyRead(
        id=created_api_key.id,
        created_by=identity.email,  # Return email instead of ID
        name=created_api_key.name,
        description=created_api_key.description,
        disabled=created_api_key.disabled,
        expires_at=created_api_key.expires_at,
        last_used_at=created_api_key.last_used_at,
        created_at=created_api_key.created_at,
        encrypted_key=created_api_key.encrypted_key,  # Return the encrypted key to user
    )

    logger.info("API key endpoint completed successfully")
    return create_response(data=response, message="API key created successfully")


ParamDep = Annotated[Params, Depends()]


@router.get("/api-keys")
async def list_api_keys(
    *,
    identity=CAN_CREATE,
    project_id: str,
    db_session: DatabaseSessionDep,
    params: ParamDep,
) -> IGetResponsePaginated[IProjectAppApiKeyRead]:
    """Get all API keys for a project"""
    # Verify project exists
    project_app = await cruds.project_app.get_by_project_id(project_id=project_id, db_session=db_session)
    if not project_app:
        raise NotFoundException(ProjectApp)

    # Get paginated API keys
    from sqlmodel import select

    query = (
        select(cruds.ProjectApiKeyRepo.model)
        .where(ProjectAppApiKey.project_id == project_id)
        .order_by(ProjectAppApiKey.created_at.desc())
    )
    api_keys_data = await cruds.ProjectApiKeyRepo.get_multi_paginated(
        params=params,
        query=query,
        db_session=db_session,
    )

    return create_response(data=api_keys_data)


@router.delete("/api-keys/{api_key_id}")
async def delete_api_key(
    *,
    identity=CAN_DELETE,
    project_id: str,
    api_key_id: str,
    db_session: DatabaseSessionDep,
) -> IDeleteResponseBase[IProjectAppApiKeyRead]:
    """Delete an API key"""

    logger.info(f"Deleting API key {api_key_id} for project_id: {project_id}, user: {identity.id}")

    # Verify project exists
    project_app = await cruds.project_app.get_by_project_id(project_id=project_id, db_session=db_session)
    if not project_app:
        logger.error(f"Project app not found for project_id: {project_id}")
        raise NotFoundException(message="Project app not found.")

    # Get the API key to verify it belongs to this project
    api_key = await cruds.ProjectApiKeyRepo.get(model_id=api_key_id, db_session=db_session)
    if not api_key:
        logger.error(f"API key not found: {api_key_id}")
        raise NotFoundException(message="API key not found.")

    if api_key.project_id != project_id:
        logger.error(f"API key {api_key_id} does not belong to project {project_id}")
        raise NotFoundException(message="API key not found.")

    # Delete the API key
    deleted_api_key = await cruds.ProjectApiKeyRepo.delete(model_id=api_key_id, db_session=db_session)

    logger.info(f"API key {api_key_id} deleted successfully")
    return create_response(data=deleted_api_key, message="API key deleted successfully")
