from datetime import datetime

from pydantic import BaseModel

from .model import ProjectAppApiKeyBase


class IProjectAppApiKeyCreate(BaseModel):
    organization_id: str
    project_id: str | None = None
    project_app_id: str
    created_by: str | None = None
    hashed_key: str
    encrypted_key: str
    name: str | None = None
    description: str | None = None
    scopes: str | None = None
    disabled: bool = False
    expires_at: datetime | None = None


class IProjectAppApiKeyUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    scopes: str | None = None
    disabled: bool | None = None
    expires_at: datetime | None = None


class IProjectAppApiKeyRead(ProjectAppApiKeyBase):
    pass


class IProjectAppApiKeyCreateRequest(BaseModel):
    name: str
    expires_at: datetime | None = None
