from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import ProjectAppApi<PERSON>ey
from .schema import IProjectAppApi<PERSON>ey<PERSON>reate, IProjectAppApiKeyUpdate


class CRUDProjectAppApiKey(CRUDBase[ProjectAppApiKey, IProjectAppApiKeyCreate, IProjectAppApiKeyUpdate]):
    async def get_by_project_id(self, *, project_id: str, session: AsyncSession):
        stmt = select(ProjectAppApiKey).where(ProjectAppApiKey.project_id == project_id)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_hashed_key(self, *, hashed_key: str, session: AsyncSession):
        stmt = select(ProjectAppApiKey).where(ProjectAppApiKey.hashed_key == hashed_key)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_encrypted_key(self, *, encrypted_key: str, session: AsyncSession):
        stmt = (
            select(ProjectAppApiKey)
            .options(selectinload(ProjectAppApiKey.project_app))
            .where(ProjectAppApiKey.encrypted_key == encrypted_key)
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


ProjectApiKeyRepo = CRUDProjectAppApiKey(ProjectAppApiKey)
