from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.organization.project.app.model import ProjectApp
from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .app.crud import project_app
from .member.crud import ProjectMemberRepo
from .model import Project
from .schema import (
    IProjectCreate,
    IProjectUpdate,
)


class CRUDProject(CRUDBase[Project, IProjectCreate, IProjectUpdate]):
    async def remove(self, *, id: str, session: AsyncSession) -> Project:
        """Remove project and automatically remove its ProjectApp"""

        # Get the project first
        project = await self.get(model_id=id, session=session)
        if not project:
            raise ValueError(f"Project with id {id} not found")

        # Remove associated ProjectApp if it exists
        existing_app = await project_app.get_by_project_id(project_id=id, session=session)
        if existing_app:
            await project_app.remove(id=existing_app.id, session=session)

        # Remove the project
        return await super().remove(id=id, session=session)

    async def list_by_organization_id(self, *, organization_id: str, db_session: AsyncSession) -> list[Project]:
        """List all projects for a given organization"""
        stmt = select(Project).where(Project.organization_id == organization_id)
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def list_slugs_by_organization(self, *, organization_id: str, session: AsyncSession) -> set[str]:
        """List all project slugs for a given organization"""
        stmt = select(Project.slug).where(Project.organization_id == organization_id)
        result = await session.execute(stmt)
        return set(result.scalars().all())

    async def get_by_slug(self, *, slug: str, session: AsyncSession):
        stmt = select(Project).options(selectinload(Project.app)).where(Project.slug == slug)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_organization_id(self, *, organization_id: str, session: AsyncSession):
        stmt = select(Project).where(Project.organization_id == organization_id)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_created_by(self, *, created_by: str, session: AsyncSession):
        stmt = select(Project).where(Project.created_by == created_by)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_members(self, *, project_id: str, session: AsyncSession):
        """Get project members directly from the database relationship."""
        return await ProjectMemberRepo.get_by_project_id(project_id=project_id, session=session)

    async def get_with_api_key(self, *, project_id: str, db_session: AsyncSession):
        """Get project with its associated API keys."""
        stmt = (
            select(Project)
            .options(selectinload(Project.app).selectinload(ProjectApp.api_keys))
            .where(Project.id == project_id)
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()


ProjectRepo = CRUDProject(Project)
