from fastapi import APIRouter

from app.exceptions import NotFoundException
from app.modules.organization.crud import OrganizationRepo
from app.modules.organization.project.crud import ProjectRepo
from app.modules.organization.project.model import Project
from app.modules.organization.project.schema import IProjectCreate, IProjectDetailsRead, IProjectUpdate
from app.modules.organization.project.usecases import create_proj_with_defaults, get_project_with_details
from app.modules.permission.deps import need_org_permission, need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import (
    IDeleteResponseBase,
    IGetResponseBase,
    IPatchResponseBase,
    IPostResponseBase,
    create_response,
)
from app.services.db.deps import DatabaseSessionDep

resource = "project"
CAN_CREATE = need_org_permission(Action.CREATE, resource)
CAN_READ = need_proj_permission(Action.READ, resource)
CAN_UPDATE = need_proj_permission(Action.UPDATE, resource)
CAN_DELETE = need_proj_permission(Action.DELETE, resource)

router = APIRouter(prefix="/organizations/{organization_id}/projects", tags=["projects"])


@router.post("/")
async def create_project(
    *,
    identity=CAN_CREATE,
    organization_id: str,
    project_create: IProjectCreate,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IProjectDetailsRead]:
    """Create a new project"""
    org = await OrganizationRepo.get(model_id=organization_id, db_session=db_session)

    new_proj = await create_proj_with_defaults(
        identity_id=identity.id,
        organization_id=org.id,
        organization_slug=org.slug,
        project_params=project_create,
        db_session=db_session,
    )
    proj = await get_project_with_details(identity.id, new_proj, db_session=db_session)

    return create_response(data=proj, message="Project created successfully")


@router.get("/{project_id}")
async def get_project(
    *,
    identity=CAN_READ,
    organization_id: str,
    project_id: str,
    db_session: DatabaseSessionDep,
) -> IGetResponseBase[IProjectDetailsRead]:
    """Get project by id"""
    # Get the project
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)

    if not project:
        raise NotFoundException(Project)

    # Convert to read schema
    proj = await get_project_with_details(identity.id, project, db_session=db_session)

    return create_response(data=proj, message="Project retrieved successfully")


@router.patch("/{project_id}")
async def update_project(
    *,
    identity=CAN_UPDATE,
    project_id: str,
    project_update: IProjectUpdate,
    db_session: DatabaseSessionDep,
) -> IPatchResponseBase[IProjectDetailsRead]:
    """Update project by id"""
    # Get the project first
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)

    if not project:
        raise NotFoundException(message=f"Project with id '{project_id}' not found")

    # Update the project
    updated_project = await ProjectRepo.update(current=project, new=project_update, db_session=db_session)

    # Convert to read schema with organization
    proj = await get_project_with_details(identity.id, updated_project, db_session=db_session)

    return create_response(data=proj, message="Project updated successfully")


@router.delete("/{project_id}")
async def delete_project(
    *,
    identity=CAN_DELETE,
    project_id: str,
    db_session: DatabaseSessionDep,
) -> IDeleteResponseBase[IProjectDetailsRead]:
    """Delete project by slug (soft delete)"""
    # Get the project first
    project = await ProjectRepo.get(model_id=project_id, db_session=db_session)

    if not project:
        raise NotFoundException(message=f"Project with id '{project_id}' not found")

    # Soft delete the project
    deleted_project = await ProjectRepo.soft_delete(model_id=project.id, db_session=db_session)

    # Convert to read schema with organization
    proj = await get_project_with_details(identity.id, deleted_project, db_session=db_session)
    return create_response(data=proj, message="Project deleted successfully")
