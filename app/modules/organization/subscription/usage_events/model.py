from enum import Enum
from typing import TYPE_CHECKING

import sqlalchemy as sa
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship, SQLModel

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity.model import Identity
    from app.modules.organization.model import Organization
    from app.modules.organization.project.app.api_key.model import ProjectAppApiKey
    from app.modules.organization.project.model import Project
    from app.modules.organization.subscription.model import OrganizationSubscription


class UsageEventType(str, Enum):
    """Types of usage events that can be tracked"""

    TOKEN_USAGE = "token_usage"
    API_CALL = "api_call"
    MEMORY_CREATION = "memory_creation"
    MEMORY_RETRIEVAL = "memory_retrieval"
    MEMORIZE_CALL = "memorize_call"
    CUSTOM = "custom"


class OrganizationSubscriptionUsageEventBase(SQLModel):
    """Base model for subscription usage events"""

    event_type: UsageEventType = Field(
        sa_type=sa.Enum(UsageEventType, native_enum=False, length=50), description="Type of usage event"
    )
    feature_name: str = Field(description="Name of the feature associated with this event")
    quantity: int = Field(
        default=0, sa_type=BigInteger, description="Quantity of usage (e.g., number of tokens, API calls)"
    )
    data: dict | None = Field(default=None, sa_type=JSONB, description="Additional event-specific data")


class OrganizationSubscriptionUsageEvent(OrganizationSubscriptionUsageEventBase, BaseModelMixin, table=True):
    """Tracks usage events for organization subscriptions"""

    __tablename__ = "organization_subscription_usage_events"

    # Foreign keys
    organization_id: str = Field(
        foreign_key="organizations.id", nullable=False, index=True, description="Organization this event belongs to"
    )
    subscription_id: str = Field(
        foreign_key="organization_subscriptions.id",
        nullable=False,
        index=True,
        description="Subscription this event is tracked against",
    )
    identity_id: str | None = Field(
        foreign_key="identities.id", nullable=True, index=True, description="Identity who triggered the event"
    )
    project_id: str | None = Field(
        foreign_key="projects.id", nullable=True, index=True, description="Project context for the event"
    )
    api_key_id: str | None = Field(
        foreign_key="project_app_api_keys.id", nullable=True, index=True, description="API key used for the event"
    )

    # Relationships
    organization: "Organization" = Relationship()
    subscription: "OrganizationSubscription" = Relationship()
    identity: "Identity" = Relationship()
    project: "Project" = Relationship()
    api_key: "ProjectAppApiKey" = Relationship()
