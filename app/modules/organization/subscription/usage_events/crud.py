from datetime import UTC, datetime
from typing import Any

from sqlalchemy import and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import new_session

from .model import OrganizationSubscriptionUsageEvent, UsageEventType
from .schema import IUsageEventCreate, IUsageEventFilter, IUsageEventUpdate


class CRUDUsageEvent(CRUDBase[OrganizationSubscriptionUsageEvent, IUsageEventCreate, IUsageEventUpdate]):
    """CRUD operations for usage events"""

    async def create(
        self, *, obj_in: IUsageEventCreate, created_by_id: str | None = None, session: AsyncSession | None = None
    ) -> OrganizationSubscriptionUsageEvent:
        """Create a new usage event with timestamp defaulting to now"""
        session = session or new_session()

        # Set event_timestamp to now if not provided
        create_data = obj_in.model_dump()
        if create_data.get("event_timestamp") is None:
            create_data["event_timestamp"] = datetime.now(UTC)

        # Create the event
        db_obj = OrganizationSubscriptionUsageEvent(**create_data)
        if created_by_id:
            db_obj.created_by_id = created_by_id

        session.add(db_obj)
        await session.commit()
        return db_obj

    async def create_bulk(
        self, *, events: list[IUsageEventCreate], created_by_id: str | None = None, session: AsyncSession | None = None
    ) -> list[OrganizationSubscriptionUsageEvent]:
        """Create multiple usage events in bulk"""
        session = session or new_session()

        db_objs = []
        for event in events:
            create_data = event.model_dump()
            if create_data.get("event_timestamp") is None:
                create_data["event_timestamp"] = datetime.now(UTC)

            db_obj = OrganizationSubscriptionUsageEvent(**create_data)
            if created_by_id:
                db_obj.created_by_id = created_by_id
            db_objs.append(db_obj)

        session.add_all(db_objs)
        await session.commit()

        # Refresh all objects
        for db_obj in db_objs:
            await session.refresh(db_obj)

        return db_objs

    async def get_by_filter(
        self, *, filter_params: IUsageEventFilter, skip: int = 0, limit: int = 100, session: AsyncSession | None = None
    ) -> list[OrganizationSubscriptionUsageEvent]:
        """Get usage events by filter parameters"""
        session = session or new_session()

        stmt = select(OrganizationSubscriptionUsageEvent)

        # Apply filters
        conditions = []
        if filter_params.organization_id:
            conditions.append(OrganizationSubscriptionUsageEvent.organization_id == filter_params.organization_id)
        if filter_params.subscription_id:
            conditions.append(OrganizationSubscriptionUsageEvent.subscription_id == filter_params.subscription_id)
        if filter_params.identity_id:
            conditions.append(OrganizationSubscriptionUsageEvent.identity_id == filter_params.identity_id)
        if filter_params.project_id:
            conditions.append(OrganizationSubscriptionUsageEvent.project_id == filter_params.project_id)
        if filter_params.api_key_id:
            conditions.append(OrganizationSubscriptionUsageEvent.api_key_id == filter_params.api_key_id)
        if filter_params.event_type:
            conditions.append(OrganizationSubscriptionUsageEvent.event_type == filter_params.event_type)
        if filter_params.start_date:
            conditions.append(OrganizationSubscriptionUsageEvent.event_timestamp >= filter_params.start_date)
        if filter_params.end_date:
            conditions.append(OrganizationSubscriptionUsageEvent.event_timestamp <= filter_params.end_date)

        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.order_by(OrganizationSubscriptionUsageEvent.event_timestamp.desc())
        stmt = stmt.offset(skip).limit(limit)

        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_usage_sum(
        self,
        *,
        organization_id: str,
        subscription_id: str,
        event_type: UsageEventType,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        session: AsyncSession | None = None,
    ) -> int:
        """Get sum of usage quantities for a specific event type and period"""
        session = session or new_session()

        stmt = select(func.sum(OrganizationSubscriptionUsageEvent.quantity)).where(
            OrganizationSubscriptionUsageEvent.organization_id == organization_id,
            OrganizationSubscriptionUsageEvent.subscription_id == subscription_id,
            OrganizationSubscriptionUsageEvent.event_type == event_type,
        )

        if start_date:
            stmt = stmt.where(OrganizationSubscriptionUsageEvent.created_at >= start_date)
        if end_date:
            stmt = stmt.where(OrganizationSubscriptionUsageEvent.created_at <= end_date)

        result = await session.execute(stmt)
        return result.scalar() or 0

    async def get_usage_stats(
        self,
        *,
        organization_id: str,
        subscription_id: str,
        event_type: UsageEventType,
        start_date: datetime,
        end_date: datetime,
        session: AsyncSession | None = None,
    ) -> dict[str, Any]:
        """Get usage statistics for a specific period"""
        session = session or new_session()

        stmt = select(
            func.sum(OrganizationSubscriptionUsageEvent.quantity).label("total_quantity"),
            func.count(OrganizationSubscriptionUsageEvent.id).label("event_count"),
            func.avg(OrganizationSubscriptionUsageEvent.quantity).label("average_quantity"),
        ).where(
            OrganizationSubscriptionUsageEvent.organization_id == organization_id,
            OrganizationSubscriptionUsageEvent.subscription_id == subscription_id,
            OrganizationSubscriptionUsageEvent.event_type == event_type,
            OrganizationSubscriptionUsageEvent.event_timestamp >= start_date,
            OrganizationSubscriptionUsageEvent.event_timestamp <= end_date,
        )

        result = await session.execute(stmt)
        row = result.one()

        return {
            "organization_id": organization_id,
            "subscription_id": subscription_id,
            "period_start": start_date,
            "period_end": end_date,
            "event_type": event_type,
            "total_quantity": row.total_quantity or 0,
            "event_count": row.event_count or 0,
            "average_quantity": float(row.average_quantity) if row.average_quantity else 0.0,
        }

    async def check_quota(
        self,
        *,
        organization_id: str,
        subscription_id: str,
        event_type: UsageEventType,
        requested_quantity: int,
        quota_limit: int | None,
        period_start: datetime | None = None,
        session: AsyncSession | None = None,
    ) -> dict[str, Any]:
        """Check if usage is within quota limits"""
        session = session or new_session()

        # Get current usage
        current_usage = await self.get_usage_sum(
            organization_id=organization_id,
            subscription_id=subscription_id,
            event_type=event_type,
            start_date=period_start,
            session=session,
        )

        # Check quota
        is_allowed = True
        remaining_quota = None

        if quota_limit is not None:
            remaining_quota = quota_limit - current_usage
            is_allowed = (current_usage + requested_quantity) <= quota_limit

        return {
            "organization_id": organization_id,
            "subscription_id": subscription_id,
            "event_type": event_type,
            "requested_quantity": requested_quantity,
            "current_usage": current_usage,
            "quota_limit": quota_limit,
            "is_allowed": is_allowed,
            "remaining_quota": remaining_quota,
        }


# Create instance
usage_event = CRUDUsageEvent(OrganizationSubscriptionUsageEvent)
