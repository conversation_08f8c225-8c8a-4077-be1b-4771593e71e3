from datetime import datetime

from pydantic import BaseModel, Field

from .model import OrganizationSubscriptionUsageEventBase, UsageEventType


class IUsageEventRead(OrganizationSubscriptionUsageEventBase):
    """Schema for reading usage events"""

    id: str
    organization_id: str
    subscription_id: str
    identity_id: str | None
    project_id: str | None
    api_key_id: str | None
    created_at: datetime
    updated_at: datetime


class IUsageEventCreate(BaseModel):
    """Schema for creating usage events"""

    event_type: UsageEventType
    feature_name: str = Field(description="Name of the feature associated with this event")
    event_timestamp: datetime | None = Field(
        default=None, description="When the event occurred (defaults to current time)"
    )
    quantity: int = Field(default=1, ge=0, description="Quantity of usage")
    data: dict | None = Field(default=None, description="Additional event-specific data")
    organization_id: str
    subscription_id: str
    identity_id: str | None = None
    project_id: str | None = None
    api_key_id: str | None = None


class IUsageEventUpdate(BaseModel):
    """Schema for updating usage events"""

    event_type: UsageEventType | None = None
    event_timestamp: datetime | None = None
    quantity: int | None = Field(default=None, ge=0)
    metadata: dict | None = None


class IUsageEventBulkCreate(BaseModel):
    """Schema for bulk creating usage events"""

    events: list[IUsageEventCreate] = Field(
        ..., min_length=1, max_length=1000, description="List of usage events to create"
    )


class IUsageEventFilter(BaseModel):
    """Schema for filtering usage events"""

    organization_id: str | None = None
    subscription_id: str | None = None
    identity_id: str | None = None
    project_id: str | None = None
    api_key_id: str | None = None
    event_type: UsageEventType | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None


class IUsageEventStats(BaseModel):
    """Schema for usage event statistics"""

    organization_id: str
    subscription_id: str
    period_start: datetime
    period_end: datetime
    event_type: UsageEventType
    total_quantity: int
    event_count: int
    average_quantity: float
    metadata: dict | None = None


class IUsageQuotaCheck(BaseModel):
    """Schema for checking usage against quotas"""

    organization_id: str
    subscription_id: str
    event_type: UsageEventType
    requested_quantity: int
    current_usage: int
    quota_limit: int | None
    is_allowed: bool
    remaining_quota: int | None


class IUsageEventResponse(BaseModel):
    """Response schema for usage event operations"""

    success: bool
    message: str
    data: IUsageEventRead | None = None
    error: str | None = None
