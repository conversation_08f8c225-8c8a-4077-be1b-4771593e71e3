from datetime import UTC, datetime
from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.organization.subscription.crud import organization_subscription
from app.services.db.session import new_session

from .crud import usage_event
from .model import UsageEventType
from .schema import IUsageEventCreate


class UsageEventUseCases:
    """Use cases for usage event operations"""

    @staticmethod
    async def track_token_usage(
        *,
        organization_id: str,
        subscription_id: str,
        token_count: int,
        identity_id: str | None = None,
        project_id: str | None = None,
        api_key_id: str | None = None,
        metadata: dict | None = None,
        session: AsyncSession | None = None,
    ) -> Any:
        """Track token usage for an organization"""
        session = session or new_session()

        # Create metadata if not provided
        if metadata is None:
            metadata = {}

        # Add token count to metadata
        metadata["token_count"] = token_count

        # Create usage event
        event_data = IUsageEventCreate(
            event_type=UsageEventType.TOKEN_USAGE,
            quantity=token_count,
            metadata=metadata,
            organization_id=organization_id,
            subscription_id=subscription_id,
            identity_id=identity_id,
            project_id=project_id,
            api_key_id=api_key_id,
        )

        return await usage_event.create(obj_in=event_data, session=session)

    @staticmethod
    async def track_api_call(
        *,
        organization_id: str,
        subscription_id: str,
        endpoint: str,
        method: str,
        identity_id: str | None = None,
        project_id: str | None = None,
        api_key_id: str | None = None,
        response_time_ms: int | None = None,
        status_code: int | None = None,
        session: AsyncSession | None = None,
    ) -> Any:
        """Track an API call"""
        session = session or new_session()

        # Create metadata
        metadata = {
            "endpoint": endpoint,
            "method": method,
        }

        if response_time_ms is not None:
            metadata["response_time_ms"] = response_time_ms
        if status_code is not None:
            metadata["status_code"] = status_code

        # Create usage event
        event_data = IUsageEventCreate(
            event_type=UsageEventType.API_CALL,
            quantity=1,
            metadata=metadata,
            organization_id=organization_id,
            subscription_id=subscription_id,
            identity_id=identity_id,
            project_id=project_id,
            api_key_id=api_key_id,
        )

        return await usage_event.create(obj_in=event_data, session=session)

    @staticmethod
    async def track_memory_usage(
        *,
        organization_id: str,
        subscription_id: str,
        event_type: UsageEventType,  # MEMORY_CREATION or MEMORY_RETRIEVAL
        memory_count: int = 1,
        identity_id: str | None = None,
        project_id: str | None = None,
        api_key_id: str | None = None,
        metadata: dict | None = None,
        session: AsyncSession | None = None,
    ) -> Any:
        """Track memory creation or retrieval"""
        session = session or new_session()

        if event_type not in [UsageEventType.MEMORY_CREATION, UsageEventType.MEMORY_RETRIEVAL]:
            raise ValueError("Event type must be MEMORY_CREATION or MEMORY_RETRIEVAL")

        # Create metadata if not provided
        if metadata is None:
            metadata = {}

        metadata["memory_count"] = memory_count

        # Create usage event
        event_data = IUsageEventCreate(
            event_type=event_type,
            quantity=memory_count,
            metadata=metadata,
            organization_id=organization_id,
            subscription_id=subscription_id,
            identity_id=identity_id,
            project_id=project_id,
            api_key_id=api_key_id,
        )

        return await usage_event.create(obj_in=event_data, session=session)

    @staticmethod
    async def check_and_track_usage(
        *,
        organization_id: str,
        subscription_id: str,
        event_type: UsageEventType,
        requested_quantity: int,
        identity_id: str | None = None,
        project_id: str | None = None,
        api_key_id: str | None = None,
        metadata: dict | None = None,
        session: AsyncSession | None = None,
    ) -> dict[str, Any]:
        """Check quota and track usage if allowed"""
        session = session or new_session()

        # Get subscription to check quota
        subscription = await organization_subscription.get(id=subscription_id, session=session)

        if not subscription:
            return {"success": False, "message": "Subscription not found", "is_allowed": False}

        # For token usage, check against token_quota
        quota_limit = None
        if event_type == UsageEventType.TOKEN_USAGE and subscription.token_quota:
            quota_limit = subscription.token_quota
            # Get period start (subscription start date)
            period_start = subscription.started_at
        else:
            # No quota limit for other event types
            period_start = None

        # Check quota
        quota_check = await usage_event.check_quota(
            organization_id=organization_id,
            subscription_id=subscription_id,
            event_type=event_type,
            requested_quantity=requested_quantity,
            quota_limit=quota_limit,
            period_start=period_start,
            session=session,
        )

        if not quota_check["is_allowed"]:
            return {"success": False, "message": "Quota exceeded", "is_allowed": False, "quota_check": quota_check}

        # Track the usage
        event_data = IUsageEventCreate(
            event_type=event_type,
            quantity=requested_quantity,
            metadata=metadata,
            organization_id=organization_id,
            subscription_id=subscription_id,
            identity_id=identity_id,
            project_id=project_id,
            api_key_id=api_key_id,
        )

        created_event = await usage_event.create(obj_in=event_data, session=session)

        return {
            "success": True,
            "message": "Usage tracked successfully",
            "is_allowed": True,
            "event": created_event,
            "quota_check": quota_check,
        }

    @staticmethod
    async def get_organization_usage_summary(
        *,
        organization_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        session: AsyncSession | None = None,
    ) -> dict[str, Any]:
        """Get usage summary for an organization"""
        session = session or new_session()

        # Default to current month if dates not provided
        if not end_date:
            end_date = datetime.now(UTC)
        if not start_date:
            start_date = datetime(end_date.year, end_date.month, 1, tzinfo=UTC)

        # Get active subscription
        subscriptions = await organization_subscription.get_by_organization_id(
            organization_id=organization_id, session=session
        )

        active_subscription = None
        for sub in subscriptions:
            if sub.status == "ACTIVE":
                active_subscription = sub
                break

        if not active_subscription:
            return {
                "organization_id": organization_id,
                "subscription_id": None,
                "period_start": start_date,
                "period_end": end_date,
                "usage_by_type": {},
                "has_active_subscription": False,
            }

        # Get usage for each event type
        usage_by_type = {}
        for event_type in UsageEventType:
            stats = await usage_event.get_usage_stats(
                organization_id=organization_id,
                subscription_id=active_subscription.id,
                event_type=event_type,
                start_date=start_date,
                end_date=end_date,
                session=session,
            )
            usage_by_type[event_type.value] = stats

        return {
            "organization_id": organization_id,
            "subscription_id": active_subscription.id,
            "period_start": start_date,
            "period_end": end_date,
            "usage_by_type": usage_by_type,
            "has_active_subscription": True,
            "token_quota": active_subscription.token_quota,
        }


# Create instance
usage_event_usecases = UsageEventUseCases()
