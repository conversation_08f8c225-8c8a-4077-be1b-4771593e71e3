from datetime import datetime

from pydantic import BaseModel

from .model import OrganizationSubscriptionBase, SubscriptionStatus


class IOrganizationSubscriptionRead(OrganizationSubscriptionBase):
    plan_id: str
    started_at: datetime
    expires_at: datetime | None
    is_trial: bool
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime


class IOrganizationSubscriptionCreate(BaseModel):
    organization_id: str
    plan_id: str
    started_at: datetime
    expires_at: datetime | None = None
    is_trial: bool = False
    status: SubscriptionStatus = SubscriptionStatus.ACTIVE


class IOrganizationSubscriptionUpdate(BaseModel):
    plan_id: str | None = None
    expires_at: datetime | None = None
    status: SubscriptionStatus | None = None


class IPlanDetails(BaseModel):
    id: str  # Plan identifier like "free", "monthly", "yearly"
    name: str
    memorize_calls: int | None  # Plan's memorize call limit (None means unlimited)
    status: SubscriptionStatus
    created_at: datetime
    updated_at: datetime
    expires_at: datetime | None
    started_at: datetime


class IUsageDetails(BaseModel):
    memorize_calls: int  # Actual usage count
    billing_period_start: datetime
    billing_period_end: datetime


class ICurrentSubscriptionRead(BaseModel):
    plan: IPlanDetails
    usage: IUsageDetails
