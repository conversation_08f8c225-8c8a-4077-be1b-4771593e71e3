from datetime import datetime

from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import OrganizationSubscription, SubscriptionStatus
from .schema import IOrganizationSubscriptionCreate, IOrganizationSubscriptionUpdate


class CRUDOrganizationSubscription(
    CRUDBase[OrganizationSubscription, IOrganizationSubscriptionCreate, IOrganizationSubscriptionUpdate]
):
    async def get_active_by_org(self, *, org_id: str, db_session: AsyncSession) -> OrganizationSubscription | None:
        """Get the active subscription for an organization"""
        stmt = select(OrganizationSubscription).where(
            OrganizationSubscription.organization_id == org_id,
            OrganizationSubscription.status == SubscriptionStatus.ACTIVE,
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_organization(
        self, *, organization_id: str, session: AsyncSession
    ) -> list[OrganizationSubscription]:
        """Get all subscriptions for an organization"""
        stmt = select(OrganizationSubscription).where(OrganizationSubscription.organization_id == organization_id)
        result = await session.execute(stmt)
        return list(result.scalars().all())

    async def expire_subscription(
        self, *, subscription_id: str, session: AsyncSession
    ) -> OrganizationSubscription | None:
        """Expire a subscription"""
        subscription = await self.get(model_id=subscription_id, session=session)
        if not subscription:
            return None

        update_data = IOrganizationSubscriptionUpdate(status=SubscriptionStatus.EXPIRED, expires_at=datetime.utcnow())
        return await self.update(db_obj=subscription, new=update_data, session=session)

    async def cancel_subscription(
        self, *, subscription_id: str, session: AsyncSession
    ) -> OrganizationSubscription | None:
        """Cancel a subscription"""
        subscription = await self.get(model_id=subscription_id, db_session=session)
        if not subscription:
            return None

        update_data = IOrganizationSubscriptionUpdate(status=SubscriptionStatus.CANCELLED)
        return await self.update(current=subscription, new=update_data, db_session=session)


organization_subscription = CRUDOrganizationSubscription(OrganizationSubscription)
