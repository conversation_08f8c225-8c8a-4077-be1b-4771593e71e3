from datetime import datetime

import pendulum
from fastapi import APIRouter
from sqlmodel import select

from app.exceptions import NotFoundException
from app.modules.organization.subscription.crud import organization_subscription
from app.modules.organization.subscription.schema import ICurrentSubscriptionRead, IPlanDetails, IUsageDetails
from app.modules.organization.subscription.usage_events.crud import usage_event
from app.modules.organization.subscription.usage_events.model import UsageEventType
from app.modules.permission.deps import need_org_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.modules.stripe_subscription.models import StripeSubscription
from app.services.db.deps import DatabaseSessionDep

resource = "subscription"
CAN_READ = need_org_permission(Action.READ, resource)

router = APIRouter(prefix="/organizations/{organization_id}/subscription", tags=["subscriptions"])


def get_current_billing_period(subscription_started_at: datetime) -> tuple[datetime, datetime]:
    """
    Calculate the current billing period based on subscription start date.
    Returns (start_date, end_date) for the current billing month.
    """
    # Convert to pendulum for easier date math
    if subscription_started_at.tzinfo is None:
        start_dt = pendulum.instance(subscription_started_at, tz="UTC")
    else:
        start_dt = pendulum.instance(subscription_started_at)

    now = pendulum.now("UTC")

    # Calculate months between start and now
    months_diff = now.diff(start_dt).in_months()

    # Current billing period starts at subscription start date plus months
    current_period_start = start_dt.add(months=months_diff)

    # End date is one month after the start date
    current_period_end = current_period_start.add(months=1)

    # Convert back to datetime objects
    return current_period_start, current_period_end


@router.get("/", response_model=IGetResponseBase[ICurrentSubscriptionRead])
async def get_organization_subscription(
    *,
    organization_id: str,
    identity=CAN_READ,
    db_session: DatabaseSessionDep,
) -> IGetResponseBase[ICurrentSubscriptionRead]:
    """Get the active subscription for an organization"""
    subscription = await organization_subscription.get_active_by_org(org_id=organization_id, db_session=db_session)

    if not subscription:
        raise NotFoundException("No active subscription found for this organization")

    await db_session.refresh(subscription, ["plan"])

    # Calculate current billing period
    billing_start, billing_end = get_current_billing_period(subscription.started_at)

    # Get memorize count for current billing period
    memorize_count = await usage_event.get_usage_sum(
        organization_id=organization_id,
        subscription_id=subscription.id,
        event_type=UsageEventType.MEMORIZE_CALL,
        start_date=billing_start,
        end_date=billing_end,
        session=db_session,
    )

    # Get plan ID from Stripe price nickname
    plan_id = "free"  # Default value
    try:
        # Query StripeSubscription using organization_subscription_id
        stripe_sub_stmt = select(StripeSubscription).where(
            StripeSubscription.organization_subscription_id == subscription.id
        )
        result = await db_session.execute(stripe_sub_stmt)
        stripe_subscription = result.scalar_one_or_none()

        if stripe_subscription:
            # TODO: Need to implement getting price_id from Stripe subscription
            # and then querying StripeProductPrice for nickname
            # For now, use a mapping based on plan code or other available data
            if subscription.plan.code:
                plan_id = subscription.plan.code
    except Exception:
        # If any error occurs, fall back to default "free"
        plan_id = "free"

    # Create plan details
    plan_details = IPlanDetails(
        id=plan_id,
        name=subscription.plan.name,
        memorize_calls=subscription.memorize_calls or subscription.plan.memorize_calls,
        status=subscription.status,
        created_at=subscription.created_at,
        updated_at=subscription.updated_at,
        expires_at=subscription.expires_at,
        started_at=subscription.started_at,
    )

    # Create usage details
    usage_details = IUsageDetails(
        memorize_calls=memorize_count, billing_period_start=billing_start, billing_period_end=billing_end
    )

    subscription_read = ICurrentSubscriptionRead(plan=plan_details, usage=usage_details)

    return create_response(data=subscription_read)
