from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession
from app.usecases.subscription import get_current_subscription

from .member.crud import OrganizationMemberRepo
from .model import Organization
from .schema import IOrganizationCreate, IOrganizationDetailsRead, IOrganizationUpdate


class CRUDOrganization(CRUDBase[Organization, IOrganizationCreate, IOrganizationUpdate]):
    async def list_slugs(self, *, session: AsyncSession) -> set[str]:
        result = await session.execute(select(Organization.slug))
        return set(result.scalars().all())

    async def get_by_slug(self, *, slug: str, session: AsyncSession):
        stmt = select(Organization).where(Organization.slug == slug)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_created_by(self, *, created_by: str, session: AsyncSession):
        stmt = select(Organization).where(Organization.created_by == created_by)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_members(self, *, organization_id: str, session: AsyncSession):
        """Get organization members directly from the database relationship."""
        return await OrganizationMemberRepo.list_by_organization_id(organization_id=organization_id, session=session)

    async def get_with_subscription(self, *, org_id: str, session: AsyncSession) -> IOrganizationDetailsRead | None:
        """Get organization with current subscription details."""
        org = await self.get(model_id=org_id, session=session)
        if not org:
            return None

        current_subscription = await get_current_subscription(org.id, session)

        return IOrganizationDetailsRead(**org.model_dump(), current_subscription=current_subscription)

    async def get_by_slug_with_subscription(
        self, *, slug: str, session: AsyncSession
    ) -> IOrganizationDetailsRead | None:
        """Get organization by slug with current subscription details."""
        org = await self.get_by_slug(slug=slug, session=session)
        if not org:
            return None

        current_subscription = await get_current_subscription(org.id, session)

        return IOrganizationDetailsRead(**org.model_dump(), current_subscription=current_subscription)

    async def soft_delete(self, *, org_id, db_session):
        return await super().soft_delete(model_id=org_id, db_session=db_session)


OrganizationRepo = CRUDOrganization(Organization)
