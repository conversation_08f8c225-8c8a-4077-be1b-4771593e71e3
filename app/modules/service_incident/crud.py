from __future__ import annotations

from sqlalchemy import update
from sqlmodel import delete, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.modules.shared.base_crud import CRUDBase

from .model import ServiceIncident
from .schema import ICreateServiceIncident, IUpdateServiceIncident


class CRUDServiceIncident(CRUDBase[ServiceIncident, ICreateServiceIncident, IUpdateServiceIncident]):
    async def get_by_service_name(self, *, service_name: str, session: AsyncSession):
        stmt = (
            select(ServiceIncident)
            .where(ServiceIncident.service_name == service_name)
            .order_by(ServiceIncident.created_at.desc())
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def insert(self, *, incident: ServiceIncident, session: AsyncSession):
        session.add(incident)
        await session.flush()
        return incident.id

    async def update(
        self, *, update_id: str, update_data: IUpdateServiceIncident, session: AsyncSession
    ) -> ServiceIncident | None:
        session = session or self.get_db().session
        try:
            # 提取更新字段(仅显式设置的字段)
            update_fields = update_data.model_dump(exclude_unset=True)
            if not update_fields:
                return None  # 没有字段需要更新

            stmt = (
                update(ServiceIncident)
                .where(ServiceIncident.id == update_id)
                .values(**update_fields)
                .returning(ServiceIncident)  # 返回更新后的记录
            )

            # 执行语句
            result = await session.execute(stmt)
            updated_incident = result.scalars().first()  # 获取更新后的 ORM 对象
        except Exception:
            return None
        else:
            return updated_incident  # 返回更新后的记录

    async def batch_delete(self, *, ids: list[str], session: AsyncSession):
        if not ids:
            return 0

        session = session or self.get_db().session
        try:
            result = await session.execute(delete(ServiceIncident).where(ServiceIncident.id.in_(ids)))
            await session.commit()

            deleted_count = result.rowcount
        except Exception:
            await session.rollback()
            return 0
        else:
            return deleted_count


service_incident = CRUDServiceIncident(ServiceIncident)
