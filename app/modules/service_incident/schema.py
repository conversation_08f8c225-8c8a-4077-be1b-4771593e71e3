from datetime import datetime

from app.modules.service_incident.model import IncidentLevel, IncidentStatus, ServiceIncidentBase


class ICreateServiceIncident(ServiceIncidentBase):
    created_by: str | None = None
    updated_by: str | None = None


class IUpdateServiceIncident(ServiceIncidentBase):
    id: str
    service_name: str | None = None
    title: str | None = None
    level: IncidentLevel | None = None
    status: IncidentStatus | None = None
    start_time: datetime | None = None
    content: str | None = None
    extra: str | None = None
    end_time: datetime | None = None


class IServiceIncidentResponse(ServiceIncidentBase):
    id: str
    created_by: str | None = None
    updated_by: str | None = None
