from datetime import datetime
from enum import Enum

import pendulum
import sqlalchemy as sa
from sqlmodel import DateTime, Field

from app.modules.shared.base_model import BaseModelMixin


class ServiceStatus(int, Enum):
    online = 1
    offline = 0


class Services(str, Enum):
    SERVICE_1 = "test_1"
    SERVICE_2 = "test_2"
    SERVICE_3 = "test_3"


class IncidentStatus(Enum):
    ACTIVE = "active"
    RESOLVED = "resolved"


class IncidentLevel(Enum):
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"


class ServiceIncidentBase(BaseModelMixin):
    """Base model for service incidents."""

    service_name: str = Field(index=True, nullable=False)
    title: str = Field(nullable=False)
    content: str | None = None
    level: IncidentLevel = Field(sa_type=sa.Enum(IncidentLevel, native_enum=False), nullable=False)
    status: IncidentStatus = Field(sa_type=sa.Enum(IncidentStatus, native_enum=False), nullable=False)
    extra: str | None = None
    start_time: datetime = Field(
        default_factory=lambda: pendulum.now("UTC"), sa_type=DateTime(timezone=True), nullable=False
    )
    end_time: datetime | None = Field(default=None, sa_type=DateTime(timezone=True), nullable=True)


class ServiceIncident(ServiceIncidentBase, table=True):
    """Model for service incidents."""

    __tablename__ = "service_incidents"

    created_by: str | None = Field(default=None, nullable=True)
    updated_by: str | None = Field(default=None, nullable=True)
