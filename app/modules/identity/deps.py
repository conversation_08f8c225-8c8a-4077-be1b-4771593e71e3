from typing import Annotated

from fastapi import Depends
from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.exceptions import NotFoundException
from app.services.authentication.deps import FirebaseUserDep
from app.services.db.deps import DatabaseSessionDep

from .auth_provider.model import IdentityAuthProvider
from .model import Identity


async def _get_current_identity(firebase_user: FirebaseUserDep, session: DatabaseSessionDep) -> Identity:
    stmt = (
        select(Identity)
        .join(Identity.auth_provider)
        .options(selectinload(Identity.auth_provider))
        .where(IdentityAuthProvider.provider_uid == firebase_user.uid)
        .where(Identity.deleted_at.is_(None))
    )
    res = await session.execute(stmt)
    identity = res.scalar_one_or_none()

    if not identity:
        raise NotFoundException(Identity)

    return identity


_IdentityDep = Annotated[Identity, Depends(_get_current_identity)]
