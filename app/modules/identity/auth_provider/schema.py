from pydantic import BaseModel

from .model import IdentityAuthProviderBase


class IIdentityAuthProviderCreate(IdentityAuthProviderBase):
    identity_id: str
    hashed_password: str | None = None


class IIdentityAuthProviderUpdate(BaseModel):
    platform: str | None = None
    provider: str | None = None
    provider_uid: str | None = None
    email: str | None = None
    email_verified: bool | None = None
    hashed_password: str | None = None
    extra_data: dict | None = None


class IIdentityAuthProviderRead(IdentityAuthProviderBase):
    id: str
    identity_id: str
    created_at: str
    updated_at: str
