from typing import TYPE_CHECKING

from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlmodel import Field, Relationship, SQLModel

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity import Identity


class IdentityAuthProviderBase(SQLModel):
    platform: str = Field(default="firebase")  # e.g., 'firebase'
    provider: str  # e.g., 'password', 'google'
    provider_uid: str  # unique user id from provider
    email: str | None = None
    email_verified: bool | None = None


class IdentityAuthProvider(BaseModelMixin, IdentityAuthProviderBase, table=True):
    __tablename__ = "identity_auth_providers"
    identity_id: str = Field(foreign_key="identities.id", index=True)

    hashed_password: str | None = None

    extra_data: dict | None = Field(default_factory=dict, sa_type=JSONB)

    identity: "Identity" = Relationship(back_populates="auth_provider")
