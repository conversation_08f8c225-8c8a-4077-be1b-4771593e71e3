from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.authentication.firebase import FirebaseUser
from app.services.db.session import AsyncSession

from ..model import Identity
from .model import IdentityAuthProvider
from .schema import IIdentityAuthProviderCreate, IIdentityAuthProviderUpdate


class CRUDIdentityAuthProvider(
    CRUDBase[IdentityAuthProvider, IIdentityAuthProviderCreate, IIdentityAuthProviderUpdate]
):
    async def get_by_identity_id(self, *, identity_id: str, session: AsyncSession) -> IdentityAuthProvider | None:
        stmt = select(IdentityAuthProvider).where(IdentityAuthProvider.identity_id == identity_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_provider_uid(self, *, provider_uid: str, session: AsyncSession) -> IdentityAuthProvider | None:
        stmt = select(IdentityAuthProvider).where(IdentityAuthProvider.provider_uid == provider_uid)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_provider_and_uid(
        self, *, provider: str, provider_uid: str, session: AsyncSession
    ) -> IdentityAuthProvider | None:
        stmt = select(IdentityAuthProvider).where(
            IdentityAuthProvider.provider == provider, IdentityAuthProvider.provider_uid == provider_uid
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_email(self, *, email: str, session: AsyncSession) -> IdentityAuthProvider:
        stmt = select(IdentityAuthProvider).where(IdentityAuthProvider.email == email)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_platform_and_provider(
        self, *, platform: str, provider: str, session: AsyncSession
    ) -> list[IdentityAuthProvider]:
        stmt = select(IdentityAuthProvider).where(
            IdentityAuthProvider.platform == platform, IdentityAuthProvider.provider == provider
        )
        result = await session.execute(stmt)
        return list(result.scalars().all())

    async def update_email_verification(
        self, *, identity_id: str, email_verified: bool, session: AsyncSession
    ) -> IdentityAuthProvider | None:
        auth_provider = await self.get_by_identity_id(identity_id=identity_id, session=session)
        if not auth_provider:
            return None

        update_data = {"email_verified": email_verified}
        return await self.update(obj_current=auth_provider, obj_new=update_data, session=session)

    async def update_extra_data(
        self, *, identity_id: str, extra_data: dict, session: AsyncSession
    ) -> IdentityAuthProvider | None:
        auth_provider = await self.get_by_identity_id(identity_id=identity_id, session=session)
        if not auth_provider:
            return None

        current_extra_data = auth_provider.extra_data or {}
        current_extra_data.update(extra_data)

        update_data = {"extra_data": current_extra_data}
        return await self.update(obj_current=auth_provider, obj_new=update_data, session=session)

    async def create_by_firebase_user(
        self, firebase_user: FirebaseUser, identity: Identity, session: AsyncSession
    ) -> IdentityAuthProvider:
        """Create authentication provider record for the identity from Firebase user data."""
        auth_provider = IdentityAuthProvider(
            identity_id=identity.id,
            platform="firebase",
            provider=firebase_user.firebase.sign_in_provider,
            provider_uid=firebase_user.uid,
            email=firebase_user.email,
            email_verified=firebase_user.email_verified,
            extra_data=firebase_user.model_dump(),
        )
        session.add(auth_provider)
        return auth_provider


AuthProviderRepo = CRUDIdentityAuthProvider(IdentityAuthProvider)
