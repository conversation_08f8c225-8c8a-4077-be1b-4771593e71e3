from fastapi import APIRouter

from app.modules.identity.crud import IdentityRepo
from app.modules.identity.model import Identity
from app.modules.identity.schema import IIdentityUpdate
from app.modules.permission.deps import need_any_identity
from app.modules.shared.base_schema import IPatchResponseBase, create_response
from app.services.db.deps import DatabaseSessionDep

router = APIRouter(prefix="/identity", tags=["Identity"])
SELF = need_any_identity()


@router.patch("/")
async def update_identity(
    *, identity=SELF, identity_update: IIdentityUpdate, session: DatabaseSessionDep
) -> IPatchResponseBase[Identity]:
    """Update the current user's identity fields"""
    updated_identity = await IdentityRepo.update(obj_current=identity, obj_new=identity_update, session=session)

    return create_response(data=updated_identity, message="Identity updated successfully")
