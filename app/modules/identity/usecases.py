from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.identity.auth_provider.crud import AuthProviderRepo as auth_provider
from app.modules.identity.auth_provider.model import IdentityAuthProvider
from app.modules.identity.crud import IdentityRepo
from app.modules.identity.model import Identity
from app.services.authentication.firebase import FirebaseUser
from app.services.db.session import AsyncSession


async def create_identity_resources(firebase_user: FirebaseUser, db_session: AsyncSession) -> Identity:
    """Create a new identity and auth provider from Firebase user data."""
    # Create identity
    new_identity = await IdentityRepo.create_by_firebase_user(firebase_user, db_session)

    # Create auth provider
    await auth_provider.create_by_firebase_user(firebase_user, new_identity, db_session)

    # Flush to ensure both identity and auth provider are persisted
    await db_session.flush()

    return new_identity


async def get_identity_by_provider_uid(provider_uid: str, db_session: AsyncSession) -> Identity | None:
    stmt = (
        select(Identity)
        .join(IdentityAuthProvider)
        .options(selectinload(Identity.auth_provider))
        .where(IdentityAuthProvider.provider_uid == provider_uid)
        .where(Identity.deleted_at.is_(None))
    )
    result = await db_session.execute(stmt)
    identity = result.scalar_one_or_none()

    return identity
