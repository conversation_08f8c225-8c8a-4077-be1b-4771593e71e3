from typing import Any

import pendulum
from sqlalchemy import exc
from sqlmodel import select

from app.exceptions.integrity_error_handler import handle_integrity_error
from app.modules.shared.base_crud import CRUDBase
from app.services.authentication.firebase import FirebaseUser
from app.services.db.session import AsyncSession
from app.utils import slug

from .model import Identity
from .schema import IIdentityCreate, IIdentityUpdate


class CRUDIdentity(CRUDBase[Identity, IIdentityCreate, IIdentityUpdate]):
    async def list_slugs(self, *, db_session: AsyncSession) -> set[str]:
        result = await db_session.execute(select(Identity.slug))
        return set(result.scalars().all())

    async def update_last_login_at(self, *, identity: Identity, db_session: AsyncSession) -> Identity:
        identity.last_login_at = pendulum.now("UTC")
        db_session.add(identity)
        return identity

    async def update(
        self,
        *,
        obj_current: Identity,
        obj_new: IIdentityUpdate | dict[str, Any] | Identity,
        session: AsyncSession,
    ) -> Identity:
        # Extract update data for potential error handling
        update_data = obj_new if isinstance(obj_new, dict) else obj_new.model_dump(exclude_unset=True)

        try:
            # Call parent update method
            return await super().update(obj_current=obj_current, obj_new=obj_new, session=session)
        except exc.IntegrityError as e:
            handle_integrity_error(e, update_data)

    async def create_by_firebase_user(self, firebase_user: FirebaseUser, db_session: AsyncSession) -> Identity:
        """Create a new identity from Firebase user data."""
        base_slug = slug.create_base_slug_from_email(firebase_user.email)
        existing_slugs = await self.list_slugs(db_session=db_session)
        uniq_slug = slug.generate_uniq_slug(base_slug, existing_slugs)

        identity = Identity(
            slug=uniq_slug,
            name=base_slug,
            email=firebase_user.email,
        )
        db_session.add(identity)
        await db_session.flush()
        return identity


IdentityRepo = CRUDIdentity(Identity)
