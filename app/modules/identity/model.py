from datetime import datetime
from typing import TYPE_CHECKING

import pendulum
from sqlmodel import Column, DateTime, Field, ForeignKey, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity.auth_provider import IdentityAuthProvider
    from app.modules.organization.member import OrganizationMember
    from app.modules.organization.project import Project
    from app.modules.organization.project.member import ProjectMember


class IdentityBase(BaseModelMixin):
    slug: str = Field(unique=True)
    name: str | None = None
    language: str = Field(default="en")
    timezone: str = Field(default="UTC")
    email: str | None = None
    last_login_at: datetime = Field(default_factory=lambda: pendulum.now("UTC"), sa_type=DateTime(timezone=True))
    terms_accepted: bool | None = None


class Identity(IdentityBase, table=True):
    __tablename__ = "identities"

    default_project_id: str | None = Field(
        default=None, sa_column=Column(ForeignKey("projects.id", use_alter=True), nullable=True)
    )

    deleted_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))

    auth_provider: "IdentityAuthProvider" = Relationship(back_populates="identity")
    default_project: "Project" = Relationship(sa_relationship_kwargs={"foreign_keys": "Identity.default_project_id"})
    projects: list["ProjectMember"] = Relationship(back_populates="identity")
    organizations: list["OrganizationMember"] = Relationship(back_populates="identity")
