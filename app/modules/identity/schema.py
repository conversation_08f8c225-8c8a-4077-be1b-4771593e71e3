from __future__ import annotations

from pydantic import BaseModel, Field

from app.modules.organization.project.schema import IProjectReadWithOrg

from ..organization.schema import IOrganizationWithProjects, IProjectInOrganization
from .model import Identity, IdentityBase


class IIdentityCreate:
    pass


class IIdentityUpdate(BaseModel):
    slug: str | None = None
    name: str | None = None
    language: str | None = None
    timezone: str | None = None
    email: str | None = None
    terms_accepted: bool | None = None
    default_project_id: str | None = None


class IIdentityReadWithProject(IdentityBase):
    default_project: IProjectReadWithOrg | None = None
    email_verified: bool = Field(default=False)

    @classmethod
    def from_identity(cls, identity: Identity) -> IIdentityReadWithProject:
        return cls(
            **identity.model_dump(),
            email_verified=identity.auth_provider.email_verified,
            default_project=identity.default_project,
        )


class IIdentityOrganizationsResponse(BaseModel):
    organizations: list[IOrganizationWithProjects]

    @classmethod
    def format_from_data(cls, organizations_with_roles, projects_with_roles):
        """Format raw organization and project data into the response structure"""
        org_dict = {}

        # Add organizations user is a member of with their roles
        for org, role_name in organizations_with_roles:
            org_dict[org.id] = IOrganizationWithProjects(
                id=org.id,
                slug=org.slug,
                name=org.name,
                description=org.description,
                status=org.status,
                data_location=org.data_location,
                currency=org.currency,
                role=role_name,
                projects=[],
            )

        # Add projects and their organizations with roles
        for project, role_name in projects_with_roles:
            org = project.organization
            if org.id not in org_dict:
                org_dict[org.id] = IOrganizationWithProjects(
                    id=org.id,
                    slug=org.slug,
                    name=org.name,
                    description=org.description,
                    status=org.status,
                    data_location=org.data_location,
                    currency=org.currency,
                    role="guest",  # User is not a direct member of this org
                    projects=[],
                )

            org_dict[org.id].projects.append(
                IProjectInOrganization(
                    id=project.id,
                    slug=project.slug,
                    name=project.name,
                    description=project.description,
                    status=project.status,
                    environment=project.environment,
                    data_location=project.data_location,
                    role=role_name,
                )
            )

        return cls(organizations=list(org_dict.values()))
