from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field


# Base schemas
class StripeProductBase(BaseModel):
    """Base schema for StripeProduct"""

    stripe_product_id: str = Field(..., description="Stripe product ID")
    name: str = Field(..., description="Product name")
    description: str | None = Field(None, description="Product description")
    stripe_metadata: dict[str, Any] | None = Field(None, description="Stripe metadata")
    active: bool = Field(True, description="Whether the product is active")


class StripeProductPriceBase(BaseModel):
    """Base schema for StripeProductPrice"""

    stripe_price_id: str = Field(..., description="Stripe price ID")
    product_id: str = Field(..., description="Product ID")
    nickname: str | None = Field(None, description="Price nickname")
    amount: int = Field(..., description="Amount in cents")
    currency: str = Field("usd", description="Currency code")
    active: bool = Field(True, description="Whether the price is active")


# Response schemas
class StripeProductResponse(StripeProductBase):
    """Schema for StripeProduct response"""

    id: str = Field(..., description="Product ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class StripeProductWithPricesResponse(StripeProductResponse):
    """Schema for StripeProduct with associated prices"""

    prices: list[StripeProductPriceBase] = Field(default_factory=list, description="Associated prices")
