from typing import TYPE_CHECKING

from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlmodel import Column, Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    pass


class StripeProduct(BaseModelMixin, table=True):
    __tablename__ = "stripe_products"

    stripe_product_id: str = Field(unique=True, nullable=False, max_length=255)
    name: str = Field(nullable=False, max_length=255)
    description: str | None = None
    stripe_metadata: dict | None = Field(default=None, sa_column=Column(JSONB))
    active: bool = Field(default=True)

    # Relationships
    prices: list["StripeProductPrice"] = Relationship(back_populates="product")


class StripeProductPrice(BaseModelMixin, table=True):
    __tablename__ = "stripe_product_prices"

    stripe_price_id: str = Field(unique=True, nullable=False, max_length=255)
    product_id: str = Field(foreign_key="stripe_products.id", nullable=False, index=True)
    nickname: str | None = Field(default=None, max_length=100)
    amount: int = Field(nullable=False)  # Amount in cents (e.g., 999 = $9.99)
    currency: str = Field(default="usd", max_length=10)
    active: bool = Field(default=True)

    # Relationships
    product: "StripeProduct" = Relationship(back_populates="prices")
