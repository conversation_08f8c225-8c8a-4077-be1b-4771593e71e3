from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import new_session

from .models import StripeProduct, StripeProductPrice


class CRUDProduct(CRUDBase[StripeProduct, dict, dict]):
    async def get_by_stripe_id(
        self, *, stripe_product_id: str, session: AsyncSession | None = None
    ) -> StripeProduct | None:
        """Get product by Stripe product ID."""
        session = session or new_session()
        stmt = select(StripeProduct).where(StripeProduct.stripe_product_id == stripe_product_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def upsert_from_stripe(self, *, stripe_product: dict, session: AsyncSession | None = None) -> StripeProduct:
        """Create or update product from Stripe data."""
        session = session or new_session()

        # Check if product already exists
        existing = await self.get_by_stripe_id(stripe_product_id=stripe_product["id"], session=session)

        if existing:
            # Update existing product
            existing.name = stripe_product["name"]
            existing.description = stripe_product.get("description")
            existing.stripe_metadata = stripe_product.get("metadata", {})
            existing.active = stripe_product.get("active", True)
            session.add(existing)
            await session.commit()
            await session.refresh(existing)
            return existing
        else:
            # Create new product
            new_product = StripeProduct(
                stripe_product_id=stripe_product["id"],
                name=stripe_product["name"],
                description=stripe_product.get("description"),
                stripe_metadata=stripe_product.get("metadata", {}),
                active=stripe_product.get("active", True),
            )
            session.add(new_product)
            await session.commit()
            await session.refresh(new_product)
            return new_product


class CRUDPrice(CRUDBase[StripeProductPrice, dict, dict]):
    async def get_by_stripe_id(
        self, *, stripe_price_id: str, session: AsyncSession | None = None
    ) -> StripeProductPrice | None:
        """Get price by Stripe price ID."""
        session = session or new_session()
        stmt = select(StripeProductPrice).where(StripeProductPrice.stripe_price_id == stripe_price_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_stripe_price_id(
        self, *, stripe_price_id: str, session: AsyncSession | None = None
    ) -> StripeProductPrice | None:
        """Alias for get_by_stripe_id for consistency."""
        return await self.get_by_stripe_id(stripe_price_id=stripe_price_id, session=session)

    async def get_by_product_id(
        self, *, product_id: str, session: AsyncSession | None = None
    ) -> list[StripeProductPrice]:
        """Get all prices for a product."""
        session = session or new_session()
        stmt = select(StripeProductPrice).where(StripeProductPrice.product_id == product_id)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def upsert_from_stripe(
        self, *, stripe_price: dict, product_id: str, session: AsyncSession | None = None
    ) -> StripeProductPrice:
        """Create or update price from Stripe data."""
        session = session or new_session()

        # Check if price already exists
        existing = await self.get_by_stripe_id(stripe_price_id=stripe_price["id"], session=session)

        if existing:
            # Update existing price
            existing.product_id = product_id
            existing.nickname = stripe_price.get("nickname")
            existing.amount = stripe_price.get("unit_amount", 0)
            existing.currency = stripe_price.get("currency", "usd")
            existing.active = stripe_price.get("active", True)
            session.add(existing)
            await session.commit()
            await session.refresh(existing)
            return existing
        else:
            # Create new price
            new_price = StripeProductPrice(
                stripe_price_id=stripe_price["id"],
                product_id=product_id,
                nickname=stripe_price.get("nickname"),
                amount=stripe_price.get("unit_amount", 0),
                currency=stripe_price.get("currency", "usd"),
                active=stripe_price.get("active", True),
            )
            session.add(new_price)
            await session.commit()
            await session.refresh(new_price)
            return new_price


# Singleton instances
product_crud = CRUDProduct(StripeProduct)
price_crud = CRUDPrice(StripeProductPrice)
