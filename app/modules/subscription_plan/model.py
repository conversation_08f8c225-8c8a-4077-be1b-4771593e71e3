from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>
from sqlmodel import Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..organization.subscription.model import OrganizationSubscription


class SubscriptionPlanBase(BaseModelMixin):
    name: str = Field(index=True)
    code: str = Field(index=True, unique=True)
    description: str | None = None
    token_quota: int = Field(default=0, sa_type=BigInteger)
    memorize_calls: int | None = Field(default=None, sa_type=BigInteger)
    extra_features: dict | None = Field(default_factory=dict, sa_type=JSONB)
    is_active: bool = Field(default=True)


class SubscriptionPlan(SubscriptionPlanBase, table=True):
    __tablename__ = "subscription_plans"

    # Relationships
    organization_subscriptions: list["OrganizationSubscription"] = Relationship(back_populates="plan")
