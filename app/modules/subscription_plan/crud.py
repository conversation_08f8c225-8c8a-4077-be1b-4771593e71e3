from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import SubscriptionPlan
from .schema import ISubscriptionPlanCreate, ISubscriptionPlanUpdate


class CRUDSubscriptionPlan(CRUDBase[SubscriptionPlan, ISubscriptionPlanCreate, ISubscriptionPlanUpdate]):
    def __init__(self, model: type[SubscriptionPlan]):
        super().__init__(model)
        self._plan_cache: dict[str, SubscriptionPlan | None] = {}

    async def get_by_code(self, *, code: str, session: AsyncSession):
        cache_key = f"code:{code}"
        if cache_key in self._plan_cache:
            return self._plan_cache[cache_key]

        stmt = select(SubscriptionPlan).where(SubscriptionPlan.code == code)
        result = await session.execute(stmt)
        plan = result.scalar_one_or_none()

        self._plan_cache[cache_key] = plan
        return plan

    async def build_cache(self, session: AsyncSession):
        """Initialize subscription plans cache from database"""
        self._plan_cache = {
            "default": await self.get_by_code(code="MEMU-FREE-v20250730", session=session),
        }

    def clear_cache(self):
        """Clear the subscription plan cache if needed"""
        self._plan_cache.clear()

    @property
    def free(self) -> SubscriptionPlan | None:
        """Get the free subscription plan"""
        return self._plan_cache.get("default")


subscription_plan = CRUDSubscriptionPlan(SubscriptionPlan)
