from pydantic import BaseModel


class ISubscriptionPlanCreate(BaseModel):
    name: str
    code: str
    description: str | None = None
    token_quota: int = 0
    extra_features: dict | None = None
    is_active: bool = True


class ISubscriptionPlanUpdate(BaseModel):
    name: str | None = None
    code: str | None = None
    description: str | None = None
    token_quota: int | None = None
    extra_features: dict | None = None
    is_active: bool | None = None


class ISubscriptionPlanRead(BaseModel):
    id: str
    name: str
    code: str
    description: str | None = None
    token_quota: int
    extra_features: dict | None = None
    is_active: bool
