from datetime import datetime

import pendulum
from ksuid import KsuidMs
from sqlmodel import DateTime, Field, SQLModel


class BaseModelMixin(SQLModel):
    id: str = Field(default_factory=lambda: str(KsuidMs()), primary_key=True, index=True)
    created_at: datetime = Field(default_factory=lambda: pendulum.now("UTC"), sa_type=DateTime(timezone=True))
    updated_at: datetime = Field(default_factory=lambda: pendulum.now("UTC"), sa_type=DateTime(timezone=True))
