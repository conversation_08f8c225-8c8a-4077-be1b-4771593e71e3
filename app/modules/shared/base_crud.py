from typing import Any, Generic, <PERSON>Var

import pendulum
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlmodel import apaginate
from pydantic import BaseModel
from sqlalchemy import ColumnElement
from sqlmodel import SQLModel, func, select
from sqlmodel.sql.expression import Select

from app.services.db.session import AsyncSession

from .base_schema import IOrderEnum

ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
SchemaType = TypeVar("SchemaType", bound=BaseModel)
T = TypeVar("T", bound=SQLModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        **Parameters**
        * `model`: A SQLModel model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    async def get(self, *, model_id: str, db_session: AsyncSession) -> ModelType | None:
        query = select(self.model).where(self.model.id == model_id)
        response = await db_session.execute(query)
        return response.scalar_one_or_none()

    async def list_by(
        self,
        *,
        condition: ColumnElement,
        db_session: AsyncSession,
    ) -> list[ModelType] | None:
        response = await db_session.execute(select(self.model).where(condition))
        return response.scalars().all()

    async def get_count(self, db_session: AsyncSession) -> int | None:
        response = await db_session.execute(select(func.count()).select_from(select(self.model).subquery()))
        return response.scalar_one()

    async def get_multi_paginated(
        self,
        *,
        params: Params | None = Params(),
        query: T | Select[T] | None = None,
        db_session: AsyncSession,
    ) -> Page[ModelType]:
        if query is None:
            query = select(self.model)

        output = await apaginate(db_session, query, params)
        return output

    async def list_multi_paginated_ordered(
        self,
        *,
        params: Params | None = Params(),
        order_by: str | None = None,
        order: IOrderEnum | None = IOrderEnum.ascendent,
        query: T | Select[T] | None = None,
        db_session: AsyncSession,
    ) -> Page[ModelType]:
        columns = self.model.__table__.columns

        if order_by is None or order_by not in columns:
            order_by = "id"
        if order is None:
            order = IOrderEnum.ascendent

        if query is None:
            query = select(self.model)

        if order == IOrderEnum.ascendent:
            query = query.order_by(columns[order_by].asc())
        else:
            query = query.order_by(columns[order_by].desc())

        return await apaginate(db_session, query, params)

    async def create(
        self,
        *,
        new: CreateSchemaType | ModelType,
        created_by: str | None = None,
        db_session: AsyncSession,
    ) -> ModelType:
        db_obj = self.model.model_validate(new.model_dump())
        if created_by:
            db_obj.created_by = created_by
        db_session.add(db_obj)
        await db_session.flush()
        await db_session.refresh(db_obj)
        return db_obj

    async def update(
        self,
        *,
        current: ModelType,
        new: UpdateSchemaType | dict[str, Any] | ModelType,
        db_session: AsyncSession,
    ) -> ModelType:
        if isinstance(new, dict):
            update_data = new
        else:
            update_data = new.model_dump(exclude_unset=True)
        for field in update_data:
            setattr(current, field, update_data[field])

        db_session.add(current)
        await db_session.flush()
        await db_session.refresh(current)
        return current

    async def delete(self, *, model_id: str, db_session: AsyncSession) -> ModelType:
        response = await db_session.execute(select(self.model).where(self.model.id == model_id))
        obj = response.scalar_one()
        await db_session.delete(obj)
        await db_session.flush()
        return obj

    async def soft_delete(self, *, model_id: str, db_session: AsyncSession) -> ModelType:
        """
        Perform a soft delete by setting deleted_at timestamp.
        Raises AttributeError if the model doesn't have deleted_at field.
        """
        response = await db_session.execute(select(self.model).where(self.model.id == model_id))
        obj = response.scalar_one()

        if not hasattr(obj, "deleted_at"):
            msg = f"Model {self.model.__name__} does not have 'deleted_at' field for soft delete"
            raise AttributeError(msg)

        obj.deleted_at = pendulum.now("UTC")
        db_session.add(obj)
        await db_session.flush()
        await db_session.refresh(obj)
        return obj
