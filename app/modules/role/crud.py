from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .models import DomainRole, PlatformRole, Role, RoleDomain
from .schema import IRoleCreate, IRoleUpdate


class CRUDRole(CRUDBase[Role, IRoleCreate, IRoleUpdate]):
    def __init__(self, model: type[Role]):
        super().__init__(model)
        self._role_cache: dict[str, Role | None] = {}

    async def get_by_name_and_domain(self, *, name: str, domain: RoleDomain, session: AsyncSession):
        cache_key = f"{name}:{domain.value}"
        if cache_key in self._role_cache:
            return self._role_cache[cache_key]

        stmt = select(Role).where(Role.name == name, Role.domain == domain)
        result = await session.execute(stmt)
        role = result.scalar_one_or_none()

        self._role_cache[cache_key] = role
        return role

    async def build_cache(self, session: AsyncSession):
        """Initialize roles in the database if they don't exist"""
        self._role_cache = {
            "super_admin:platform": await self.get_by_name_and_domain(
                name=PlatformRole.SUPER_ADMIN.value, domain=RoleDomain.PLATFORM, session=session
            ),
            "owner:organization": await self.get_by_name_and_domain(
                name=DomainRole.OWNER.value, domain=RoleDomain.ORGANIZATION, session=session
            ),
            "owner:project": await self.get_by_name_and_domain(
                name=DomainRole.OWNER.value, domain=RoleDomain.PROJECT, session=session
            ),
            "admin:organization": await self.get_by_name_and_domain(
                name=DomainRole.ADMIN.value, domain=RoleDomain.ORGANIZATION, session=session
            ),
            "admin:project": (
                await self.get_by_name_and_domain(
                    name=DomainRole.ADMIN.value, domain=RoleDomain.PROJECT, session=session
                )
            ),
            "viewer:organization": await self.get_by_name_and_domain(
                name=DomainRole.VIEWER.value, domain=RoleDomain.ORGANIZATION, session=session
            ),
            "viewer:project": await self.get_by_name_and_domain(
                name=DomainRole.VIEWER.value, domain=RoleDomain.PROJECT, session=session
            ),
        }

    def clear_cache(self):
        """Clear the role cache if needed"""
        self._role_cache.clear()

    @property
    def platform_super_admin(self) -> Role | None:
        """Get the platform super admin role"""
        return self._role_cache.get("super_admin:platform")

    @property
    def org_owner(self) -> Role | None:
        """Get the organization owner role"""
        return self._role_cache.get("owner:organization")

    @property
    def proj_owner(self) -> Role | None:
        """Get the project owner role"""
        return self._role_cache.get("owner:project")

    @property
    def org_admin(self) -> Role | None:
        """Get the organization admin role"""
        return self._role_cache.get("admin:organization")

    @property
    def proj_admin(self) -> Role | None:
        """Get the project admin role"""
        return self._role_cache.get("admin:project")

    @property
    def org_viewer(self) -> Role | None:
        """Get the organization viewer role"""
        return self._role_cache.get("viewer:organization")

    @property
    def proj_viewer(self) -> Role | None:
        """Get the project viewer role"""
        return self._role_cache.get("viewer:project")


role = CRUDRole(Role)
