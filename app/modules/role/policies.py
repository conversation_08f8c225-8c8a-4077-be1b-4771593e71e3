from enum import Enum


class RoleDomain(str, Enum):
    PLATFORM = "platform"
    ORGANIZATION = "organization"
    PROJECT = "project"


class PlatformRole(str, Enum):
    SUPER_ADMIN = "super_admin"


class DomainRole(str, Enum):
    OWNER = "owner"
    ADMIN = "admin"
    VIEWER = "viewer"


RoleTypesByDomain: dict[RoleDomain, type[Enum]] = {
    RoleDomain.PLATFORM: PlatformRole,
    RoleDomain.ORGANIZATION: DomainRole,
    RoleDomain.PROJECT: DomainRole,
}


def get_valid_roles_for_domain(domain: RoleDomain) -> list[str]:
    """Get all valid role names for a given domain."""
    role_enum = RoleTypesByDomain.get(domain)
    if not role_enum:
        return []
    return [role.value for role in role_enum]
