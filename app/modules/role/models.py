from enum import Enum
from typing import TYPE_CHECKING

import sqlalchemy as sa
from sqlmodel import Field, Relationship, UniqueConstraint

from app.exceptions import ValidationException
from app.modules.shared.base_model import BaseModelMixin

from .policies import (
    DomainRole,
    PlatformRole,
    RoleDomain,
    RoleTypesByDomain,
    get_valid_roles_for_domain,
)

if TYPE_CHECKING:
    from ..permission.models import Permission


class Role(BaseModelMixin, table=True):
    __tablename__ = "roles"
    __table_args__ = (UniqueConstraint("name", "domain"),)

    name: str = Field(max_length=50, description="Name of the role")
    domain: RoleDomain = Field(
        sa_type=sa.Enum(RoleDomain, values_callable=lambda obj: [e.value for e in obj], native_enum=False),
        description="Domain of the role (platform, organization, or project)",
    )
    description: str | None = Field(default=None, max_length=255, description="Optional description of the role")

    # Relationship to permissions
    permissions: list["Permission"] = Relationship(back_populates="role")

    def validate(self) -> None:
        """Validate that the role name is valid for its domain."""
        valid_roles = get_valid_roles_for_domain(self.domain)
        if self.name not in valid_roles:
            raise ValueError(
                f"'{self.name}' is not a valid role for domain '{self.domain.value}'. "
                f"Valid roles: {', '.join(valid_roles)}"
            )

    @classmethod
    def get_role_enum(cls, domain: RoleDomain) -> type[Enum]:
        """Get the appropriate role enum class for a given domain."""
        role_enum = RoleTypesByDomain.get(domain)
        if not role_enum:
            raise ValidationException(message=f"No role enum found for domain: {domain.value}")
        return role_enum

    @property
    def role_enum_value(self) -> PlatformRole | DomainRole:
        """Get the enum value for this role."""
        role_enum = self.get_role_enum(self.domain)
        return role_enum(self.name)
