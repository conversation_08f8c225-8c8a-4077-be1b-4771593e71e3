from pydantic import BaseModel

from .models import RoleDomain


class IRoleCreate(BaseModel):
    name: str
    scope: RoleDomain
    description: str | None = None


class IRoleUpdate(BaseModel):
    name: str | None = None
    scope: RoleDomain | None = None
    description: str | None = None


class IRoleRead(BaseModel):
    id: str
    name: str
    scope: RoleDomain
    description: str | None = None


class IRoleAssignmentRequest(BaseModel):
    """Request schema for role assignment"""

    identity_id: str
    role: str


class IRoleAssignmentResponse(BaseModel):
    """Response schema for role assignment"""

    identity_id: str
    role: str
    success: bool
    message: str


class IOrganizationMemberResponse(BaseModel):
    identity_id: str
    identity_name: str
    identity_email: str
    roles: list[str]


class IProjectMemberResponse(BaseModel):
    identity_id: str
    identity_name: str
    identity_email: str
    roles: list[str]


class IPermissionCheckRequest(BaseModel):
    """Request schema for permission checking"""

    identity_id: str
    action: str


class IPermissionCheckResponse(BaseModel):
    """Response schema for permission checking"""

    identity_id: str
    action: str
    has_permission: bool
