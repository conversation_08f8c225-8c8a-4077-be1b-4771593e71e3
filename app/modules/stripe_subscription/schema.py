from datetime import datetime

from pydantic import BaseModel, Field


# Base schemas
class StripeSubscriptionBase(BaseModel):
    """Base schema for StripeSubscription"""

    stripe_id: str = Field(..., description="Stripe subscription ID")
    identity_id: str = Field(..., description="Identity ID associated with the subscription")
    organization_id: str = Field(..., description="Organization ID associated with the subscription")
    latest_invoice: str | None = Field(None, description="Latest invoice ID from Stripe")
    cancel_at: datetime | None = Field(None, description="Cancel time of the subscription")
    status: str = Field(..., description="Subscription status (active, canceled, past_due, incomplete, etc.)")


# Request schemas
class StripeSubscriptionCreate(StripeSubscriptionBase):
    """Schema for creating a new StripeSubscription"""

    pass


class StripeSubscriptionUpdate(BaseModel):
    """Schema for updating a StripeSubscription"""

    latest_invoice: str | None = Field(None, description="Latest invoice ID from Stripe")
    cancel_at: datetime | None = Field(None, description="Cancel time of the subscription")
    status: str | None = Field(None, description="Subscription status")


# Response schemas
class StripeSubscriptionResponse(StripeSubscriptionBase):
    """Schema for StripeSubscription response"""

    id: str = Field(..., description="Internal subscription ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class StripeSubscriptionWithRelationsResponse(StripeSubscriptionResponse):
    """Schema for StripeSubscription with related data"""

    # Can be extended later to include identity/organization details if needed
    pass
