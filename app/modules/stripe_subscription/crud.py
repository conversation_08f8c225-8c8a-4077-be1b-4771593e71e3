from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.services.db.session import new_session

from ..shared.base_crud import CRUDBase
from .models import StripeSubscription
from .schema import StripeSubscriptionCreate, StripeSubscriptionUpdate


class CRUDStripeSubscription(CRUDBase[StripeSubscription, StripeSubscriptionCreate, StripeSubscriptionUpdate]):
    async def get_by_stripe_id(
        self, *, stripe_id: str, session: AsyncSession | None = None
    ) -> StripeSubscription | None:
        """Get subscription by Stripe subscription ID."""
        session = session or new_session()
        stmt = select(StripeSubscription).where(StripeSubscription.stripe_id == stripe_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_identity_id(
        self, *, identity_id: str, session: AsyncSession | None = None
    ) -> list[StripeSubscription]:
        """Get all subscriptions for a specific identity."""
        session = session or new_session()
        stmt = select(StripeSubscription).where(StripeSubscription.identity_id == identity_id)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_organization_id(
        self, *, organization_id: str, session: AsyncSession | None = None
    ) -> list[StripeSubscription]:
        """Get all subscriptions for a specific organization."""
        session = session or new_session()
        stmt = select(StripeSubscription).where(StripeSubscription.organization_id == organization_id)
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_active_by_organization_id(
        self, *, organization_id: str, session: AsyncSession | None = None
    ) -> list[StripeSubscription]:
        """Get all active subscriptions for a specific organization."""
        session = session or new_session()
        stmt = select(StripeSubscription).where(
            StripeSubscription.organization_id == organization_id, StripeSubscription.status == "active"
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def upsert_from_stripe(
        self, *, stripe_subscription: dict, session: AsyncSession | None = None
    ) -> StripeSubscription:
        """Create or update subscription from Stripe data."""
        session = session or new_session()

        # Check if subscription already exists
        existing = await self.get_by_stripe_id(stripe_id=stripe_subscription["id"], session=session)

        if existing:
            # Update existing subscription
            existing.latest_invoice = stripe_subscription.get("latest_invoice")
            existing.status = stripe_subscription.get("status", "unknown")
            existing.cancel_at = stripe_subscription.get("cancel_at")
            session.add(existing)
            await session.commit()
            await session.refresh(existing)
            return existing
        else:
            # Create new subscription
            # Note: identity_id and organization_id should be provided in the stripe_subscription dict
            # or resolved from customer data
            new_subscription = StripeSubscription(
                stripe_id=stripe_subscription["id"],
                identity_id=stripe_subscription["identity_id"],  # This needs to be resolved from customer
                organization_subscription_id=stripe_subscription["organization_subscription_id"],
                organization_id=stripe_subscription["organization_id"],  # This needs to be resolved from customer
                latest_invoice=stripe_subscription.get("latest_invoice"),
                status=stripe_subscription.get("status", "unknown"),
            )
            session.add(new_subscription)
            await session.commit()
            await session.refresh(new_subscription)
            return new_subscription


# Create singleton instance
stripe_subscription_crud = CRUDStripeSubscription(StripeSubscription)
