from datetime import datetime

from typing import TYPE_CHECKING

from sqlmodel import Field, Relationship

from ..shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..identity.model import Identity
    from ..organization.model import Organization


class StripeSubscription(BaseModelMixin, table=True):
    __tablename__ = "stripe_subscriptions"

    stripe_id: str = Field(unique=True, nullable=False, max_length=255, index=True)
    organization_subscription_id: str = Field(
        foreign_key="organization_subscriptions.id", nullable=False, max_length=255
    )
    identity_id: str = Field(foreign_key="identities.id", nullable=False, max_length=255, index=True)
    organization_id: str = Field(foreign_key="organizations.id", nullable=False, max_length=255, index=True)
    latest_invoice: str | None = Field(default=None, max_length=255)
    cancel_at: datetime = Field(description="cancel time of the subscription")
    status: str = Field(nullable=False, max_length=50, index=True)

    # Relationships
    identity: "Identity" = Relationship()
    organization: "Organization" = Relationship()
