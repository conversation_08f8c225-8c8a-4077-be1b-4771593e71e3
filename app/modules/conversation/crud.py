from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import desc, select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import new_session

from .models import Conversation
from .schema import IConversationCreate, IConversationUpdate


class CRUDConversation(CRUDBase[Conversation, IConversationCreate, IConversationUpdate]):
    async def get_by_user_id(self, *, user_id: str, session: AsyncSession | None = None) -> list[Conversation]:
        """Get all conversations by user ID"""
        session = session or new_session()
        stmt = select(Conversation).where(Conversation.user_id == user_id).order_by(desc(Conversation.last_message_at))
        result = await session.execute(stmt)
        return result.scalars().all()

    async def get_by_conversation_id(
        self, *, conversation_id: str, session: AsyncSession | None = None
    ) -> Conversation | None:
        """Get conversation by conversation ID"""
        session = session or new_session()
        stmt = select(Conversation).where(Conversation.id == conversation_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_user_and_agent(
        self, *, user_id: str, agent_id: str, session: AsyncSession | None = None
    ) -> list[Conversation]:
        """Get conversations by user ID and agent ID"""
        session = session or new_session()
        stmt = select(Conversation).where(Conversation.user_id == user_id, Conversation.agent_id == agent_id)
        result = await session.execute(stmt)
        return result.scalars().all()


ConversationRepo = CRUDConversation(Conversation)
