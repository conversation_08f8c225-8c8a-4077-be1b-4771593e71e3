from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Column, DateTime, Field, Relationship, SQLModel

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.memory.models import Memory
    from app.modules.organization.project import Project
    from app.modules.organization.project.app.api_key import ProjectAppApiKey


class ConversationBase(SQLModel):
    user_id: str = Field(index=True, description="User ID")
    user_name: str | None = Field(default=None, description="User name")
    agent_id: str = Field(index=True, description="Agent ID")
    agent_name: str | None = Field(default=None, description="Agent name")
    # conversation_id: str = Field(unique=True, index=True, description="Unique conversation ID")

    title: str | None = Field(default=None, description="Conversation title")
    session_date: datetime = Field(sa_column=Column(DateTime(timezone=True)), description="Session start date")
    last_message_at: datetime | None = Field(
        default=None, sa_column=Column(DateTime(timezone=True)), description="Last message timestamp"
    )
    message_count: int = Field(default=0, description="Total number of messages in conversation")
    path: str | None = Field(default=None, description="Path to conversation data")
    content: list[dict] | None = Field(default=None, sa_type=JSONB, description="Conversation content")


class Conversation(BaseModelMixin, ConversationBase, table=True):
    __tablename__ = "conversations"
    api_key_id: str | None = Field(
        foreign_key="project_app_api_keys.id",
        nullable=True,
        default=None,
        index=True,
        description="API key used for this conversation",
    )
    project_id: str | None = Field(
        foreign_key="projects.id", nullable=True, default=None, index=True, description="Project ID"
    )

    api_key: "ProjectAppApiKey" = Relationship()
    project: "Project" = Relationship(back_populates="conversations")
    memories: list["Memory"] = Relationship(back_populates="conversation")
