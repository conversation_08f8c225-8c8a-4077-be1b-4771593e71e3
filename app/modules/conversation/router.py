from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from fastapi_pagination import Params
from sqlmodel import select, text

from app.modules.conversation.crud import ConversationRepo
from app.modules.conversation.models import Conversation
from app.modules.conversation.schema import (
    IConversationContentResponse,
    IConversationListRequest,
    IConversationListResponse,
)
from app.modules.memory.schema import IAgentSearchRequest, IAgentUsersSearchRequest
from app.modules.permission.deps import need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IPostResponseBase, IPostResponsePaginated, create_response
from app.services.db.deps import DatabaseSessionDep

resource = "memory"
CAN_CREATE = need_proj_permission(Action.CREATE, resource)
CAN_READ = need_proj_permission(Action.READ, resource)
CAN_UPDATE = need_proj_permission(Action.UPDATE, resource)
CAN_DELETE = need_proj_permission(Action.DELETE, resource)

router = APIRouter(prefix="/projects/{project_id}/conversations", tags=["conversations"])


# ========================================
# New Search APIs - POST endpoints
# ========================================


@router.post("/agents/search")
async def search_agents(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: IAgentSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Search agents under a project with optional filtering.
    """
    condition = ""
    if request.agent__icontains:
        condition = (
            f"AND (agent_id ILIKE '%{request.agent__icontains}%' OR agent_name ILIKE '%{request.agent__icontains}%')"
        )

    base_cte = f"""
    WITH filtered AS (
        SELECT *
        FROM conversations
        WHERE project_id = :project_id {condition}
    )
    """

    rank_cte = (
        base_cte
        + """
    , ranked AS (
        SELECT DISTINCT ON (agent_id, agent_name)
            agent_id,
            agent_name,
            created_at
        FROM filtered
        ORDER BY agent_id, agent_name, created_at DESC
    )
    """
    )

    # 分页查询：聚合 + 稳定排序（按最后活跃时间+agent_id）
    data_sql = (
        rank_cte
        + """
    , deduplicated AS (
        SELECT
            agent_id,
            MAX(created_at) AS updated_at,
            string_agg(agent_name, ', ') AS agent_names
        FROM ranked
        GROUP BY agent_id
    )
    SELECT
        agent_id,
        agent_names,
        updated_at
    FROM deduplicated
    ORDER BY updated_at DESC, agent_id
    LIMIT :limit OFFSET :offset
    """
    )

    count_sql = (
        base_cte
        + """
    SELECT COUNT(DISTINCT agent_id) AS total
    FROM filtered
    """
    )

    # 查询参数
    query_params = {
        "project_id": project_id,
        "agent_icontains": request.agent__icontains,
        "limit": params.size,
        "offset": (params.page - 1) * params.size if params.page > 0 else 0,
    }
    result = await db_session.execute(text(data_sql), query_params)
    data = [dict(row._mapping) for row in result.fetchall()]

    total = (await db_session.execute(text(count_sql), query_params)).scalar()

    return IPostResponsePaginated.create(items=data, total=total, params=params)


@router.post("/agents/users/search")
async def search_agent_users(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: IAgentUsersSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Search users under a specific agent with optional filtering.
    """
    condition = ""
    if request.user__icontains:
        condition = (
            f"AND (user_id ILIKE '%{request.user__icontains}%' OR user_name ILIKE '%{request.user__icontains}%')"
        )

    base_cte = f"""
    WITH filtered AS (
        SELECT *
        FROM conversations
        WHERE project_id = :project_id {condition} AND agent_id = :agent_id {condition}
    )
    """

    rank_cte = (
        base_cte
        + """
    , ranked AS (
        SELECT DISTINCT ON (user_id, user_name)
            user_id,
            user_name,
            created_at
        FROM filtered
        ORDER BY user_id, user_name, created_at DESC
    )
    """
    )

    # 分页查询：聚合 + 稳定排序（按最后活跃时间+user_id）
    data_sql = (
        rank_cte
        + """
    , deduplicated AS (
        SELECT
            user_id,
            MAX(created_at) AS updated_at,
            string_agg(user_name, ', ') AS user_names
        FROM ranked
        GROUP BY user_id
    )
    SELECT
        user_id,
        user_names,
        updated_at
    FROM deduplicated
    ORDER BY updated_at DESC, user_id
    LIMIT :limit OFFSET :offset
    """
    )

    count_sql = (
        base_cte
        + """
    SELECT COUNT(DISTINCT user_id) AS total
    FROM filtered
    """
    )

    # 查询参数
    query_params = {
        "project_id": project_id,
        "agent_id": request.agent_id,
        "user_icontains": request.user__icontains,
        "limit": params.size,
        "offset": (params.page - 1) * params.size if params.page > 0 else 0,
    }
    result = await db_session.execute(text(data_sql), query_params)
    data = [dict(row._mapping) for row in result.fetchall()]

    total = (await db_session.execute(text(count_sql), query_params)).scalar()

    return IPostResponsePaginated.create(items=data, total=total, params=params)


@router.post("/agents/users/conversations")
async def get_agent_user_conversations(
    *,
    project_id: str,
    request: IConversationListRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IConversationListResponse]:
    """
    Get full list of conversations for a user and agent.
    """

    try:
        query = select(Conversation.id).where(
            Conversation.project_id == project_id,
            Conversation.user_id == request.user_id,
            Conversation.agent_id == request.agent_id,
        )

        result = await db_session.execute(query)
        conversation_ids = result.scalars().all()

        return create_response(data=IConversationListResponse(conversations=conversation_ids))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting conversations: {e!s}")


@router.get("/{conversation_id}/content")
async def get_agent_user_conversation_content(
    *,
    project_id: str,
    conversation_id: str,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IConversationContentResponse]:
    """
    Get the content of a conversation.
    """
    try:
        conversation = await ConversationRepo.get(model_id=conversation_id, db_session=db_session)

        if not conversation:
            raise HTTPException(status_code=400, detail="Conversation with this ID does not exist")

        return create_response(
            data=IConversationContentResponse(conversation_id=conversation.id, content=conversation.content)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting conversation content: {e!s}")
