from __future__ import annotations

from datetime import datetime

from pydantic import BaseModel, Field

from .models import ConversationBase


# Conversation schemas
class IConversationRead(ConversationBase):
    id: str


class IConversationCreate(BaseModel):
    user_id: str
    user_name: str | None = None
    agent_id: str
    agent_name: str | None = None
    title: str | None = None
    session_date: datetime
    message_count: int | None = None
    path: str | None = None
    content: list[dict] | None = None
    api_key_id: str | None = None
    project_id: str | None = None


class IConversationUpdate(BaseModel):
    title: str | None = None
    last_message_at: datetime | None = None
    message_count: int | None = None


class IConversationListRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str = Field(..., description="Agent ID")


class IConversationListResponse(BaseModel):
    conversations: list[str] = Field(..., description="Conversation IDs")


class IConversationContentResponse(BaseModel):
    conversation_id: str | None = None
    content: list[dict] | None = None


# Search request/response schemas
class IConversationAgentSearchRequest(BaseModel):
    agent__icontains: str | None = Field(
        default=None, description="Filter agents by partial agent_id or agent_name match"
    )


class IConversationAgentSearchResponse(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    agent_name: str | None = Field(default=None, description="Agent name")


class IConversationAgentUsersSearchRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to search users for")
    user__icontains: str | None = Field(default=None, description="Filter users by partial user_id or user_name match")


class IConversationAgentUsersSearchResponse(BaseModel):
    user_id: str = Field(..., description="User ID")
    user_name: str | None = Field(default=None, description="User name")


class IConversationSearchRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str = Field(..., description="Agent ID")
    title__icontains: str | None = Field(default=None, description="Filter conversations by partial title match")


class IConversationSearchResponse(BaseModel):
    id: str = Field(..., description="Conversation ID")
    title: str | None = Field(default=None, description="Conversation title")
    session_date: datetime = Field(..., description="Session start date")
    last_message_at: datetime | None = Field(default=None, description="Last message timestamp")
    message_count: int = Field(..., description="Total number of messages")
