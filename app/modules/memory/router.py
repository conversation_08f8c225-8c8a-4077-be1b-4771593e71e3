from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from fastapi_pagination import Params
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select, text

# from app.modules import cruds
from app.modules.memory.crud import MemoryCategoryRepo, MemoryRepo
from app.modules.memory.models import MemoryCategory
from app.modules.memory.schema import (
    IAgentSearchRequest,
    IAgentUsersSearchRequest,
    IMemoryCategoriesSearchRequest,
    IMemoryCategoriesSearchResponse,
    IMemoryCategorySummaryRequest,
    IMemoryCategorySummaryResponse,
)
from app.modules.permission.deps import need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IPostResponseBase, IPostResponsePaginated, create_response
from app.services.db.deps import DatabaseSessionDep

resource = "memory"
CAN_CREATE = need_proj_permission(Action.CREATE, resource)
CAN_READ = need_proj_permission(Action.READ, resource)
CAN_UPDATE = need_proj_permission(Action.UPDATE, resource)
CAN_DELETE = need_proj_permission(Action.DELETE, resource)

router = APIRouter(prefix="/projects/{project_id}/memories")


# ========================================
# New Search APIs - POST endpoints
# ========================================


@router.post("/agents/search")
async def search_agents(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: IAgentSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Search agents under a project with optional filtering.
    """
    condition = ""
    if request.agent__icontains:
        condition = (
            f"AND (agent_id ILIKE '%{request.agent__icontains}%' OR agent_name ILIKE '%{request.agent__icontains}%')"
        )

    base_cte = f"""
    WITH filtered AS (
        SELECT *
        FROM conversations
        WHERE project_id = :project_id {condition}
    )
    """

    rank_cte = (
        base_cte
        + """
    , ranked AS (
        SELECT DISTINCT ON (agent_id, agent_name)
            agent_id,
            agent_name,
            created_at
        FROM filtered
        ORDER BY agent_id, agent_name, created_at DESC
    )
    """
    )

    # 分页查询：聚合 + 稳定排序（按最后活跃时间+agent_id）
    data_sql = (
        rank_cte
        + """
    , deduplicated AS (
        SELECT
            agent_id,
            MAX(created_at) AS updated_at,
            string_agg(agent_name, ', ') AS agent_names
        FROM ranked
        GROUP BY agent_id
    )
    SELECT
        agent_id,
        agent_names,
        updated_at
    FROM deduplicated
    ORDER BY updated_at DESC, agent_id
    LIMIT :limit OFFSET :offset
    """
    )

    count_sql = (
        base_cte
        + """
    SELECT COUNT(DISTINCT agent_id) AS total
    FROM filtered
    """
    )

    # 查询参数
    query_params = {
        "project_id": project_id,
        "agent_icontains": request.agent__icontains,
        "limit": params.size,
        "offset": (params.page - 1) * params.size if params.page > 0 else 0,
    }
    result = await db_session.execute(text(data_sql), query_params)
    data = [dict(row._mapping) for row in result.fetchall()]

    total = (await db_session.execute(text(count_sql), query_params)).scalar()

    return IPostResponsePaginated.create(items=data, total=total, params=params)


@router.post("/agents/users/search")
async def search_agent_users(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: IAgentUsersSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Search users under a specific agent with optional filtering.
    """
    condition = ""
    if request.user__icontains:
        condition = (
            f"AND (user_id ILIKE '%{request.user__icontains}%' OR user_name ILIKE '%{request.user__icontains}%')"
        )

    base_cte = f"""
    WITH filtered AS (
        SELECT *
        FROM conversations
        WHERE project_id = :project_id {condition} AND agent_id = :agent_id {condition}
    )
    """

    rank_cte = (
        base_cte
        + """
    , ranked AS (
        SELECT DISTINCT ON (user_id, user_name)
            user_id,
            user_name,
            created_at
        FROM filtered
        ORDER BY user_id, user_name, created_at DESC
    )
    """
    )

    # 分页查询：聚合 + 稳定排序（按最后活跃时间+user_id）
    data_sql = (
        rank_cte
        + """
    , deduplicated AS (
        SELECT
            user_id,
            MAX(created_at) AS updated_at,
            string_agg(user_name, ', ') AS user_names
        FROM ranked
        GROUP BY user_id
    )
    SELECT
        user_id,
        user_names,
        updated_at
    FROM deduplicated
    ORDER BY updated_at DESC, user_id
    LIMIT :limit OFFSET :offset
    """
    )

    count_sql = (
        base_cte
        + """
    SELECT COUNT(DISTINCT user_id) AS total
    FROM filtered
    """
    )

    # 查询参数
    query_params = {
        "project_id": project_id,
        "agent_id": request.agent_id,
        "user_icontains": request.user__icontains,
        "limit": params.size,
        "offset": (params.page - 1) * params.size if params.page > 0 else 0,
    }
    result = await db_session.execute(text(data_sql), query_params)
    data = [dict(row._mapping) for row in result.fetchall()]

    total = (await db_session.execute(text(count_sql), query_params)).scalar()

    return IPostResponsePaginated.create(items=data, total=total, params=params)


@router.post("/agents/users/memory-categories/search")
async def search_memory_categories(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: IMemoryCategoriesSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Search memory categories for a specific user and agent.
    """
    try:
        # previous version: get from memory table
        # query = (
        #     select(
        #         Memory.category,
        #         func.min(Memory.created_at).label("min_1"),
        #         func.max(Memory.updated_at),
        #         func.count(Memory.id).label("count"),
        #     )
        #     .where(
        #         Memory.project_id == project_id,
        #         Memory.agent_id == request.agent_id,
        #         Memory.user_id == request.user_id,
        #     )
        #     .group_by(Memory.category)
        #     .order_by(asc("min_1"))
        # )

        # if request.category__icontains:
        #     query = query.where(Memory.category.ilike(f"%{request.category__icontains}%"))

        # updated version: get from memory_category table
        # Need apply git branch [memo-actions] to let memory_category table pull basic categories from project and record counts
        query = (
            select(
                MemoryCategory.name,
                MemoryCategory.created_at,
                MemoryCategory.updated_at,
                MemoryCategory.count,
            )
            .where(
                MemoryCategory.project_id == project_id,
                MemoryCategory.agent_id == request.agent_id,
                MemoryCategory.user_id == request.user_id,
            )
            # .group_by(MemoryCategory.name)
            .order_by((MemoryCategory.group == "basic").desc(), MemoryCategory.created_at)
        )

        if request.category__icontains:
            query = query.where(MemoryCategory.name.ilike(f"%{request.category__icontains}%"))

        page_result = await MemoryRepo.get_multi_paginated(
            query=query,
            params=params,
            db_session=db_session,
        )
        categories = page_result.items

        response_items = [
            IMemoryCategoriesSearchResponse(
                name=name,
                count=count,
                created_at=created_at,
                updated_at=updated_at,
            )
            for name, created_at, updated_at, count in categories
        ]

        return IPostResponsePaginated.create(items=response_items, total=page_result.total, params=params)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to search memory categories: {e!s}")


@router.post("/agents/users/memory-categories/summary")
async def get_memory_category_summary(
    *,
    project_id: str,
    request: IMemoryCategorySummaryRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
) -> IPostResponseBase[IMemoryCategorySummaryResponse]:
    """
    Get detailed summary of a specific memory category including all memories.
    """
    try:
        result = await get_memory_or_summary(
            user_id=request.user_id,
            agent_id=request.agent_id,
            category_name=request.name,
            project_id=project_id,
            db_session=db_session,
        )

        summary = IMemoryCategorySummaryResponse(
            name=request.name,
            content=result or "No valid memory data",
        )
        return create_response(data=summary)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get memory category summary: {e!s}")


async def get_memory_or_summary(
    *,
    user_id: str,
    agent_id: str | None = None,
    category_name: str,
    project_id: str,
    db_session: AsyncSession,
) -> str | None:
    category_data = await MemoryCategoryRepo.get_by_user_agent_name(
        user_id=user_id,
        agent_id=agent_id,
        name=category_name,
        project_id=project_id,
        db_session=db_session,
    )
    if category_data is None:
        return None

    summaries = category_data.summary
    if summaries is not None:
        # length = -1 return latest, consider rewrite this later
        summary = get_best_summary(summaries, want_summary_length=-1)
        if summary is not None:
            return summary

    # fallback to raw memory items
    category_memories = await MemoryRepo.get_by_category_top(
        user_id=user_id,
        agent_id=agent_id,
        project_id=project_id,
        category=category_name,
        db_session=db_session,
        top_n=100,
        order="asc",
        order_by="created_at",
    )

    plain_memories = "\n\n".join([mem.content for mem in category_memories if mem.content])
    if category_data.count > 100:
        plain_memories += "\n\n..."

    return plain_memories


def get_best_summary(summaries: dict, want_summary_length: int) -> str:
    if len(summaries) == 0:
        return None

    if want_summary_length > 0:
        best_summary = max(
            summaries.values(),
            key=lambda item: (
                -abs(item["metadata"]["target_length"] - want_summary_length),
                item["timestamp"],
            ),
        )
    else:
        best_summary = max(summaries.values(), key=lambda item: item["timestamp"])

    return best_summary["summary"]
