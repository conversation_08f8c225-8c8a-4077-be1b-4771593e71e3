from loguru import logger
from sqlmodel import select

from app.modules.memory.task.model import MemoryTask
from app.modules.memory.task.schema import ITaskCreate, ITaskUpdate
from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession, SessionFactory


class CRUDTask(CRUDBase[MemoryTask, ITaskCreate, ITaskUpdate]):
    async def get_by_project_id(self, *, project_id: str, db_session: AsyncSession) -> list[MemoryTask]:
        """Get all tasks for a project"""
        query = select(MemoryTask).where(MemoryTask.project_id == project_id)
        result = await db_session.execute(query)
        return result.scalars().all()

    async def get_by_task_id(self, *, task_id: str, db_session: AsyncSession) -> MemoryTask | None:
        """Get a task by task ID"""
        query = select(MemoryTask).where(MemoryTask.id == task_id)
        result = await db_session.execute(query)
        return result.scalar_one_or_none()

    async def new_task_detached(
        self,
        *,
        conversation_id: str | None = None,
        status: str = "pending",
        detail_info: str | None = None,
        db_session: AsyncSession,
    ) -> bool:
        task_create = ITaskCreate(conversation_id=conversation_id, status=status, detail_info=detail_info)
        return await self.create(new=task_create, db_session=db_session)

    async def update_task_state_detached(
        # self, *, task_id: str, status: str | None = None, detail_info: str | None = None
        self,
        *,
        task_id: str,
        **kwargs,
    ) -> bool:
        try:
            async with SessionFactory.make_local_session() as db_session:
                task = await self.get_by_task_id(task_id=task_id, db_session=db_session)
                if not task:
                    logger.warning(f"Failed to update task state: Task id {task_id} not found")
                    return False

                task_state_update = ITaskUpdate(**kwargs)

                await self.update(current=task, new=task_state_update, db_session=db_session)

                await db_session.commit()

            return True
        except Exception as e:
            logger.error(f"Failed to update task state: {e!r}")
            return False


MemoryTaskRepo = CRUDTask(MemoryTask)
