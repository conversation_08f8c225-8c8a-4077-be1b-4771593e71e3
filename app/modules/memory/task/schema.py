from datetime import datetime
from typing import Any

from pydantic import BaseModel
from sqlmodel import Field

from .model import MemoryTaskBase


class ITaskRead(MemoryTaskBase):
    id: str


class ITaskSearchRequest(BaseModel):
    content__icontains: str | None = None


class ITaskCreate(BaseModel):
    status: str
    detail_info: str | None = None
    finished_at: datetime | None = None
    token_used: int | None = None
    conversation_id: str | None = None


class ITaskUpdate(BaseModel):
    status: str | None = None
    detail_info: str | None = None
    finished_at: datetime | None = None
    token_used: int | None = None
    conversation_id: str | None = None
    debug_info: dict[str, Any] | None = None


class ITaskRequest(BaseModel):
    pass


class ITaskResponse(BaseModel):
    task_id: str
    user_id: str | None = None
    user_name: str | None = None
    agent_id: str | None = None
    agent_name: str | None = None
    status: str
    detail_info: str | None = None
    token_used: int | None = None
    conversation_id: str | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None
    finished_at: datetime | None = None


class ITasksResponse(BaseModel):
    tasks: list[ITaskResponse] = Field(..., description="List of tasks")


class ITaskDiff(BaseModel):
    pass


class IMemoryDiff(BaseModel):
    memory_id: str
    action: str
    category: str
    content_before: str | None = None
    content_after: str | None = None


class ITaskDiffResponse(BaseModel):
    diff: list[IMemoryDiff] = Field(..., description="List of diffs")


class IMemoryDiffEnhanced(IMemoryDiff):
    links_before: list | None = None
    links_after: list | None = None


class ITaskStatusResponseFull(ITaskResponse):
    diff: list[IMemoryDiffEnhanced] | None = None


class ITaskStatusResponseSDK(BaseModel):
    task_id: str
    status: str
    detail_info: str = ""
