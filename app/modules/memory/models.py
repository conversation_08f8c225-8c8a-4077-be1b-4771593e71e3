from datetime import datetime
from typing import TYPE_CHECKING

from pgvector.sqlalchemy import Vector
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Column, Field, Relationship, SQLModel, Text

from app.modules.organization.project.model import Project
from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..conversation.models import Conversation


class MemoryBase(SQLModel):
    agent_id: str | None = Field(default=None, index=True, description="Agent ID")
    user_id: str = Field(index=True, description="User ID")
    conversation_id: str | None = Field(foreign_key="conversations.id", index=True, description="Conversation ID")
    memory_id: str = Field(index=True, description="Memory ID")
    category: str | None = Field(default=None, index=True, description="Memory category")
    content: str = Field(sa_column=Column(Text), description="Memory content")
    embedding: list[float] | None = Field(
        default=None, sa_column=Column(Vector(1536)), description="Memory embedding vector"
    )
    links: list | None = Field(default=None, sa_column=Column(JSONB), description="Related links or references")
    happened_at: datetime | None = Field(default=None, description="When the memory event happened")


class Memory(BaseModelMixin, MemoryBase, table=True):
    __tablename__ = "memories"
    api_key_id: str | None = Field(index=True, description="API key used for this conversation")
    project_id: str | None = Field(
        foreign_key="projects.id", index=True, description="Project ID this memory belongs to"
    )

    project: "Project" = Relationship()
    conversation: "Conversation" = Relationship(back_populates="memories")


class MemoryHistoryBase(SQLModel):
    memory_id: str = Field(index=True, description="Related memory ID")
    agent_id: str | None = Field(default=None, index=True, description="Agent ID")
    user_id: str = Field(index=True, description="User ID")
    conversation_id: str | None = Field(default=None, index=True, description="Conversation ID")
    action: str = Field(description="Action performed (create, update, delete)")
    timestamp: datetime = Field(description="When this memory history occurred")
    category: str | None = Field(default=None, description="Memory category at time of action")
    content_before: str | None = Field(sa_column=Column(Text), description="Memory content before action")
    content_after: str | None = Field(sa_column=Column(Text), description="Memory content after action")
    links_before: list | None = Field(
        default=None, sa_column=Column(JSONB), description="Related links or references before action"
    )
    links_after: list | None = Field(
        default=None, sa_column=Column(JSONB), description="Related links or references after action"
    )


class MemoryHistory(BaseModelMixin, MemoryHistoryBase, table=True):
    __tablename__ = "memory_history"


class MemoryCategoryBase(SQLModel):
    agent_id: str | None = Field(default=None, index=True, description="Agent ID")
    user_id: str = Field(index=True, description="User ID")
    category_id: str | None = Field(default=None, description="Memory category ID")
    group: str = Field(index=True, description="Class of memory category: 'basic' or 'cluster'")
    name: str = Field(description="Name of memory category")
    description: str | None = Field(sa_column=Column(Text), description="Description of memory category")
    summary: dict | None = Field(default=None, sa_column=Column(JSONB), description="Summary of memory category")
    count: int = Field(default=0, description="Number of memories in this category")


class MemoryCategory(BaseModelMixin, MemoryCategoryBase, table=True):
    __tablename__ = "memory_categories"

    project_id: str | None = Field(
        foreign_key="projects.id", index=True, description="Project ID this memory category belongs to"
    )
