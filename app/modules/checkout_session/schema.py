from datetime import datetime

from pydantic import BaseModel, ConfigDict, Field


class CheckoutSessionBase(BaseModel):
    identity_id: str = Field(..., description="Identity ID associated with the checkout session")
    organization_id: str = Field(..., description="Organization ID associated with the checkout session")
    payment_status: str | None = Field(None, description="Payment status (unpaid, paid, no_payment_required)")
    status: str | None = Field(None, description="Checkout session status (open, complete, expired)")
    stripe_id: str | None = Field(None, description="Stripe checkout session ID")


class CheckoutSessionCreate(CheckoutSessionBase):
    pass


class CheckoutSessionUpdate(BaseModel):
    identity_id: str | None = Field(None, description="Identity ID associated with the checkout session")
    organization_id: str | None = Field(None, description="Organization ID associated with the checkout session")
    payment_status: str | None = Field(None, description="Payment status")
    status: str | None = Field(None, description="Checkout session status")
    stripe_id: str | None = Field(None, description="Stripe checkout session ID")


class CheckoutSessionResponse(CheckoutSessionBase):
    model_config = ConfigDict(from_attributes=True)
    id: str = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class CheckoutSessionListResponse(BaseModel):
    items: list[CheckoutSessionResponse]
    total: int


# New schemas for the Stripe checkout API
class StripeCheckoutRequest(BaseModel):
    price_id: str = Field(..., description="Stripe price ID for the product to purchase")


class StripeCheckoutResponse(BaseModel):
    """
    Response schema for checkout session creation.

    Note: The main /billing endpoint redirects to checkout_url automatically.
    Use /billing/session endpoint to get this JSON response without redirect.
    """

    stripe_session_id: str = Field(..., description="Stripe checkout session ID")
    checkout_url: str = Field(..., description="URL to redirect user to Stripe checkout")


class CheckoutSessionProcessResponse(BaseModel):
    """Response schema for checkout session processing."""

    subscriptions: list[dict] | None = Field(None, description="Subscription data if created")
