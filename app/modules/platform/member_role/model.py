from typing import TYPE_CHECKING

from sqlmodel import Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.role.models import Role

    from ..member import PlatformMember


class PlatformMemberRole(BaseModelMixin, table=True):
    __tablename__ = "platform_member_roles"

    member_id: str = Field(foreign_key="platform_members.id", nullable=False)
    role_id: str = Field(foreign_key="roles.id", nullable=False)

    member: "PlatformMember" = Relationship(back_populates="roles")
    role: "Role" = Relationship()
