from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from .model import PlatformMemberRole
from .schema import IPlatformMemberRoleCreate, IPlatformMemberRoleUpdate


class CRUDPlatformMemberRole(CRUDBase[PlatformMemberRole, IPlatformMemberRoleCreate, IPlatformMemberRoleUpdate]):
    async def get_by_platform_member_id(
        self, *, platform_member_id: str, session: AsyncSession
    ) -> list[PlatformMemberRole]:
        stmt = select(PlatformMemberRole).where(PlatformMemberRole.platform_member_id == platform_member_id)
        result = await session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_role_id(self, *, role_id: str, session: AsyncSession) -> list[PlatformMemberRole]:
        stmt = select(PlatformMemberRole).where(PlatformMemberRole.role_id == role_id)
        result = await session.execute(stmt)
        return list(result.scalars().all())

    async def get_by_platform_member_and_role(
        self, *, platform_member_id: str, role_id: str, session: AsyncSession
    ) -> PlatformMemberRole | None:
        stmt = select(PlatformMemberRole).where(
            PlatformMemberRole.platform_member_id == platform_member_id, PlatformMemberRole.role_id == role_id
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    async def remove_member_role(
        self, *, platform_member_id: str, role_id: str, session: AsyncSession
    ) -> PlatformMemberRole | None:
        platform_member_role = await self.get_by_platform_member_and_role(
            platform_member_id=platform_member_id, role_id=role_id, session=session
        )
        if not platform_member_role:
            return None

        return await self.remove(id=platform_member_role.id, session=session)

    async def change_member_role(
        self, *, platform_member_id: str, old_role_id: str, new_role_id: str, session: AsyncSession
    ) -> PlatformMemberRole | None:
        platform_member_role = await self.get_by_platform_member_and_role(
            platform_member_id=platform_member_id, role_id=old_role_id, session=session
        )
        if not platform_member_role:
            return None

        update_data = IPlatformMemberRoleUpdate(role_id=new_role_id)
        return await self.update(db_obj=platform_member_role, new=update_data, session=session)

    async def remove_all_member_roles(self, *, platform_member_id: str, session: AsyncSession) -> int:
        """Remove all roles for a platform member. Returns count of removed roles."""
        member_roles = await self.get_by_platform_member_id(platform_member_id=platform_member_id, session=session)

        count = 0
        for member_role in member_roles:
            await self.remove(id=member_role.id, session=session)
            count += 1

        return count


platform_member_role = CRUDPlatformMemberRole(PlatformMemberRole)
