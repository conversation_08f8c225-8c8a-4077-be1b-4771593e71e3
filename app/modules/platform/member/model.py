from datetime import datetime
from typing import TYPE_CHECKING

from sqlmodel import DateTime, Field, Relationship, UniqueConstraint

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity import Identity

    from ..member_role import PlatformMemberRole


class PlatformMember(BaseModelMixin, table=True):
    __tablename__ = "platform_members"
    __table_args__ = (UniqueConstraint("identity_id"),)

    identity_id: str = Field(foreign_key="identities.id", nullable=False, unique=True, index=True)

    is_active: bool = Field(default=True)
    joined_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))

    identity: "Identity" = Relationship()
    roles: list["PlatformMemberRole"] = Relationship(back_populates="member")
