from datetime import datetime

from pydantic import BaseModel, ConfigDict


class IPlatformMemberBase(BaseModel):
    identity_id: str
    is_active: bool = True
    joined_at: datetime | None = None


class IPlatformMemberCreate(IPlatformMemberBase):
    pass


class IPlatformMemberUpdate(BaseModel):
    is_active: bool | None = None
    joined_at: datetime | None = None


class IPlatformMember(IPlatformMemberBase):
    id: str
    model_config = ConfigDict(from_attributes=True)
