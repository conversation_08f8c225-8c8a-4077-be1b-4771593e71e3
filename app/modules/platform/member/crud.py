from sqlmodel import select

from app.modules.role.models import Role
from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession

from ..member_role import PlatformMemberRole
from .model import PlatformMember
from .schema import IPlatformMemberCreate, IPlatformMemberUpdate


class CRUDPlatformMember(CRUDBase[PlatformMember, IPlatformMemberCreate, IPlatformMemberUpdate]):
    async def get_by_identity_id(self, *, identity_id: str, db_session: AsyncSession) -> PlatformMember | None:
        stmt = select(PlatformMember).where(PlatformMember.identity_id == identity_id)
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_members(self, *, db_session: AsyncSession) -> list[PlatformMember]:
        stmt = select(PlatformMember).where(PlatformMember.is_active == True)
        result = await db_session.execute(stmt)
        return list(result.scalars().all())

    async def activate_member(self, *, identity_id: str, db_session: AsyncSession) -> PlatformMember | None:
        platform_member = await self.get_by_identity_id(identity_id=identity_id, db_session=db_session)
        if not platform_member:
            return None

        update_data = IPlatformMemberUpdate(is_active=True)
        return await self.update(db_obj=platform_member, new=update_data, db_session=db_session)

    async def deactivate_member(self, *, identity_id: str, db_session: AsyncSession) -> PlatformMember | None:
        platform_member = await self.get_by_identity_id(identity_id=identity_id, db_session=db_session)
        if not platform_member:
            return None

        update_data = IPlatformMemberUpdate(is_active=False)
        return await self.update(db_obj=platform_member, new=update_data, db_session=db_session)

    async def list_roles_by_identity_id(self, *, identity_id: str, db_session: AsyncSession) -> list["Role"]:
        """Get all roles for an active platform member by identity ID."""
        stmt = (
            select(Role)
            .join(PlatformMemberRole, PlatformMemberRole.role_id == Role.id)
            .join(PlatformMember, PlatformMember.id == PlatformMemberRole.member_id)
            .where(PlatformMember.identity_id == identity_id, PlatformMember.is_active == True)
        )
        result = await db_session.execute(stmt)
        return list(result.scalars().all())


platform_member = CRUDPlatformMember(PlatformMember)
