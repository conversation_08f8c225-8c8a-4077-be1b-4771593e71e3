from app.services.db.session import AsyncSession

from .member.crud import platform_member
from .member.schema import IPlatformMemberCreate
from .member_role.crud import platform_member_role
from .member_role.schema import IPlatformMemberRoleCreate


async def create_platform_member_with_role(
    *,
    identity_id: str,
    role_id: str,
    is_active: bool = True,
    session: AsyncSession,
):
    """Create a platform member and assign them a role in a single transaction."""

    # Create platform member
    member_data = IPlatformMemberCreate(identity_id=identity_id, is_active=is_active)
    created_member = await platform_member.create(new=member_data, session=session)

    # Create platform member role
    member_role_data = IPlatformMemberRoleCreate(platform_member_id=created_member.id, role_id=role_id)
    created_member_role = await platform_member_role.create(new=member_role_data, session=session)

    return created_member, created_member_role
