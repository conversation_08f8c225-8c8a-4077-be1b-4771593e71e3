import logging
from typing import Annotated

from fastapi import Depends, Query

from app.modules import cruds
from app.services.authentication.deps import FirebaseUserDep

from .models import Invitation
from .schema import IInvitationCheck, IUseInvitationQuery

logger = logging.getLogger(__name__)

UseInvitationQueryDep = Annotated[IUseInvitationQuery, Query()]


async def resolve_invitation_from_token(
    firebase_user: FirebaseUserDep, invitation_query: UseInvitationQueryDep
) -> tuple[Invitation | None, IInvitationCheck | None]:
    """Validate invitation token and return invitation object with optional warning."""
    print(invitation_query)
    if not invitation_query or not invitation_query.invitation_token:
        return None, None

    token = invitation_query.invitation_token
    invitation = await cruds.invitation.get_by_token(token=token)

    if not invitation or not invitation.is_usable():
        return None, IInvitationCheck(is_valid=False, message="Invalid or expired invitation token")

    if invitation and invitation.email != firebase_user.email:
        return (
            firebase_user,
            None,
            IInvitationCheck(is_valid=False, message="Invitation email does not match your account"),
        )

    return invitation, IInvitationCheck(is_valid=True)


ResolvedInvitationDep = Annotated[
    tuple[Invitation | None, IInvitationCheck | None], Depends(resolve_invitation_from_token)
]
