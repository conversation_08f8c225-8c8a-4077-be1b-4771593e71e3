from datetime import datetime
from typing import TYPE_CHECKING, Optional

import pendulum
from sqlmodel import DateTime, Field, Relationship, SQLModel

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from ..identity.model import Identity
    from ..organization.model import Organization
    from ..project.model import Project
    from ..role.models import Role


class InvitationBase(SQLModel):
    email: str = Field(index=True)
    token: str = Field(unique=True, index=True)
    expires_at: datetime = Field(sa_type=DateTime(timezone=True))
    used_at: datetime | None = Field(default=None, sa_type=DateTime(timezone=True))
    is_active: bool = Field(default=True)

    # Context for the invitation
    target_organization_id: str | None = Field(
        default=None, foreign_key="organizations.id", description="Organization to invite user to"
    )
    target_project_id: str | None = Field(
        default=None, foreign_key="projects.id", description="Specific project to invite user to"
    )


class Invitation(BaseModelMixin, InvitationBase, table=True):
    __tablename__ = "invitations"

    sender_id: str = Field(foreign_key="identities.id", index=True)
    receiver_id: str | None = Field(default=None, foreign_key="identities.id", index=True)
    target_role_id: str = Field(foreign_key="roles.id", description="Role assigned to the invited user")

    sender: "Identity" = Relationship(sa_relationship_kwargs={"foreign_keys": "Invitation.sender_id"})
    receiver: Optional["Identity"] = Relationship(sa_relationship_kwargs={"foreign_keys": "Invitation.receiver_id"})
    target_role: "Role" = Relationship()
    target_organization: Optional["Organization"] = Relationship()
    target_project: Optional["Project"] = Relationship()

    def is_expired(self) -> bool:
        return pendulum.now("UTC") > self.expires_at

    def is_usable(self) -> bool:
        return self.is_active and not self.is_expired() and self.used_at is None
