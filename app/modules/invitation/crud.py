import secrets

import pendulum
from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.shared.base_crud import CRUDBase
from app.services.db.session import AsyncSession, new_session

from .models import Invitation, InvitationBase


class CRUDInvitation(CRUDBase[Invitation, InvitationBase, InvitationBase]):
    async def get_by_token(self, *, token: str, session: AsyncSession | None = None) -> Invitation | None:
        session = session or new_session()
        query = (
            select(Invitation)
            .options(
                selectinload(Invitation.role), selectinload(Invitation.organization), selectinload(Invitation.project)
            )
            .where(Invitation.token == token)
        )
        response = await session.execute(query)
        return response.scalar_one_or_none()

    async def list_by_email(self, *, email: str, session: AsyncSession | None = None) -> list[Invitation]:
        session = session or new_session()
        query = select(Invitation).where(Invitation.email == email)
        response = await session.execute(query)
        return response.scalars().all()

    async def list_active_by_fields(self, *, email: str, session: AsyncSession | None = None) -> list[Invitation]:
        session = session or new_session()
        query = select(Invitation).where(
            Invitation.email == email,
            Invitation.is_active is True,
            Invitation.used_at.is_(None),
            Invitation.expires_at > pendulum.now("UTC"),
        )
        response = await session.execute(query)
        return response.scalars().all()

    async def create_invitation(
        self,
        *,
        email: str,
        invited_by_id: str,
        expires_hours: int = 72,
        session: AsyncSession | None = None,
    ) -> Invitation:
        session = session or new_session()

        # Generate secure token
        token = secrets.token_urlsafe(32)

        # Set expiration time
        expires_at = pendulum.now("UTC").add(hours=expires_hours)

        invitation_data = InvitationBase(email=email, token=token, expires_at=expires_at, is_active=True)

        invitation = Invitation.model_validate(invitation_data)
        invitation.invited_by_id = invited_by_id

        session.add(invitation)
        await session.commit()
        await session.refresh(invitation)

        return invitation

    async def use_invitation(
        self,
        *,
        invitation: Invitation,
        invited_identity_id: str,
        session: AsyncSession | None = None,
    ) -> Invitation:
        session = session or new_session()

        invitation.used_at = pendulum.now("UTC")
        invitation.invited_identity_id = invited_identity_id
        invitation.is_active = False  # Deactivate to prevent any future use

        session.add(invitation)
        await session.refresh(invitation)

        return invitation

    async def deactivate_invitation(
        self,
        *,
        invitation: Invitation,
        session: AsyncSession | None = None,
    ) -> Invitation:
        session = session or new_session()

        invitation.is_active = False

        session.add(invitation)
        await session.commit()
        await session.refresh(invitation)

        return invitation

    async def get_invitations_by_inviter(
        self,
        *,
        invited_by_id: str,
        session: AsyncSession | None = None,
    ) -> list[Invitation]:
        session = session or new_session()
        query = select(Invitation).where(Invitation.invited_by_id == invited_by_id)
        response = await session.execute(query)
        return response.scalars().all()


invitation = CRUDInvitation(Invitation)
