from datetime import datetime

from pydantic import BaseModel, EmailStr, Field, field_validator

from .models import InvitationBase


class IInvitationCreate(BaseModel):
    email: EmailStr
    expires_hours: int = 72


class IInvitationRead(InvitationBase):
    id: str
    invited_by_id: str
    invited_identity_id: str | None = None


class IInvitationUpdate(BaseModel):
    is_active: bool | None = None


class IUseInvitationQuery(BaseModel):
    invitation_token: str | None = Field(default=None, description="Invitation token for joining organization/project")

    @field_validator("invitation_token")
    @classmethod
    def validate_invitation_token(cls, v):
        return None if v == "" else v


class IInvitationCheck(BaseModel):
    is_valid: bool | None = None
    used_at: datetime | None = None
    message: str | None = None
    invited_by_name: str | None = None


class IInvitationResponse(BaseModel):
    id: str
    email: str
    token: str
    expires_at: datetime
    used_at: datetime | None = None
    is_active: bool
    invited_by_id: str
    invited_identity_id: str | None = None
    is_expired: bool
    is_usable: bool


class IInvitationListResponse(BaseModel):
    invitations: list[IInvitationResponse]
