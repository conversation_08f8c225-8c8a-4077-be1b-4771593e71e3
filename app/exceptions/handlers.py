from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exception_handlers import http_exception_handler as fastapi_http_exception_handler
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import NoResultFound
from starlette.exceptions import HTTPException as StarletteHTTPException

from .app_exceptions import (
    AppException,
    BadRequestException,
    ConflictException,
    InternalErrorException,
    NotFoundException,
    UnauthenticatedException,
    UnauthorizedException,
    ValidationException,
)

# Exception Mapping

STATUS_EXCEPTION_MAP = {
    status.HTTP_400_BAD_REQUEST: BadRequestException,
    status.HTTP_401_UNAUTHORIZED: UnauthenticatedException,
    status.HTTP_403_FORBIDDEN: UnauthorizedException,
    status.HTTP_404_NOT_FOUND: NotFoundException,
    status.HTTP_409_CONFLICT: ConflictException,
    status.HTTP_422_UNPROCESSABLE_ENTITY: ValidationException,
    status.HTTP_500_INTERNAL_SERVER_ERROR: InternalErrorException,
}


# Exception Handlers


async def app_exception_handler(_: Request, exc: AppException) -> JSONResponse:
    """Handle AppException by returning structured JSON response."""
    return exc.to_response()


async def request_validation_exception_handler(_: Request, exc: RequestValidationError) -> JSONResponse:
    app_exception = ValidationException(message="Validation error", details=exc.errors())
    return app_exception.to_response()


async def not_found_exception_handler(_: Request, exc: NoResultFound) -> JSONResponse:
    """Handle NoResultFound exception by returning NotFoundException."""
    app_exception = NotFoundException(message="Resource not found", details=[{"error": str(exc)}])
    return app_exception.to_response()


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """Handle HTTPException by converting to structured AppException or using FastAPI's default."""
    if exception_class := STATUS_EXCEPTION_MAP.get(exc.status_code):
        return exception_class(message=exc.detail or "An unexpected error occurred").to_response()
    return await fastapi_http_exception_handler(request, exc)


async def general_exception_handler(_: Request, _exc: Exception) -> JSONResponse:
    """Handle general exceptions by returning a 500 Internal Server Error."""
    app_exception = InternalErrorException(message="Internal server error")
    return app_exception.to_response()


def register_exception_handlers(app: FastAPI) -> None:
    """Register all exception handlers with the FastAPI app."""
    app.add_exception_handler(AppException, app_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, request_validation_exception_handler)
    app.add_exception_handler(NoResultFound, not_found_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
