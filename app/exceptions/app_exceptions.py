from collections.abc import Callable
from typing import Any

from fastapi import status
from fastapi.responses import JSONResponse

from .models import (
    IBadRequestResponse,
    IConflictResponse,
    IEmailNotVerifiedResponse,
    IErrorResponse,
    IInternalErrorResponse,
    INotFoundResponse,
    IUnauthenticatedResponse,
    IUnauthorizedResponse,
    IValidationErrorResponse,
)


def _ensure_list(details: Any | None, message: str) -> list:
    """Ensure the input is a list, converting single items to a list."""
    if details is None:
        return [{"error": message}]
    return details if isinstance(details, list) else [details]


def _generate_message(template: str | Callable, message_args: str | tuple, message: str | None) -> str:
    """Generate a message from a template and arguments."""
    if message is not None:
        return message
    elif callable(template) and isinstance(message_args, tuple):
        return template(*message_args)
    elif isinstance(template, str):
        return template
    elif isinstance(message_args, str):
        return message_args
    else:
        return "An error occurred"


class ExceptionMeta(type):
    """Metaclass for auto-generating exception __init__ methods with response models."""

    def __new__(mcs, name: str, bases: tuple, attrs: dict):
        if name in ("AppException", "Exception"):
            return super().__new__(mcs, name, bases, attrs)

        response_class = attrs.get("_response_class")
        status_code = attrs.get("_status_code")
        template = attrs.get("_template")

        if response_class and status_code:

            def __init__(
                self,
                message_args: tuple | str = (),
                details: Any | None = None,
                message: str | None = None,
                meta: dict[str, Any] | None = None,
            ) -> None:
                """Initialize the exception with a structured response."""
                new_message = _generate_message(template, message_args, message)
                response = response_class(
                    message=new_message,
                    details=_ensure_list(details, message),
                    meta=meta,
                )
                super(self.__class__, self).__init__(response, status_code=status_code)

            attrs["__init__"] = __init__

        return super().__new__(mcs, name, bases, attrs)


class AppException(Exception, metaclass=ExceptionMeta):
    """Base exception for API errors with structured response models."""

    def __init__(
        self,
        response: IErrorResponse,
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ) -> None:
        self.response = response
        self.status_code = status_code
        super().__init__(response.message)

    def to_response(self) -> JSONResponse:
        """Convert exception to FastAPI JSONResponse."""
        return JSONResponse(
            content=self.response.model_dump(),
            status_code=self.status_code,
        )


# HTTP Exception Classes


class BadRequestException(AppException):
    """400 Bad Request error."""

    _response_class = IBadRequestResponse
    _status_code = status.HTTP_400_BAD_REQUEST


class UnauthenticatedException(AppException):
    """401 Unauthorized error."""

    _response_class = IUnauthenticatedResponse
    _status_code = status.HTTP_401_UNAUTHORIZED


class UnauthorizedException(AppException):
    """403 Forbidden error."""

    _response_class = IUnauthorizedResponse
    _status_code = status.HTTP_403_FORBIDDEN


class EmailNotVerifiedException(AppException):
    """403 Forbidden - Email not verified."""

    _response_class = IEmailNotVerifiedResponse
    _status_code = status.HTTP_403_FORBIDDEN


class NotFoundException(AppException):
    """404 Not Found error."""

    _response_class = INotFoundResponse
    _status_code = status.HTTP_404_NOT_FOUND
    _template = lambda cls: f"{cls.__name__} not found"


class ConflictException(AppException):
    """409 Conflict error."""

    _response_class = IConflictResponse
    _status_code = status.HTTP_409_CONFLICT


class ValidationException(AppException):
    """422 Unprocessable Entity - Validation error."""

    _response_class = IValidationErrorResponse
    _status_code = status.HTTP_422_UNPROCESSABLE_ENTITY


class InternalErrorException(AppException):
    """500 Internal Server Error."""

    _response_class = IInternalErrorResponse
    _status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
