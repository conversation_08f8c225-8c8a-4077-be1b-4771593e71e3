from enum import Enum
from typing import Any

from sqlmodel import Field, SQLModel


class ErrorCode(str, Enum):
    BAD_REQUEST = "BAD_REQUEST"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"

    UNAUTHENTICATED = "UNAUTHENTICATED"
    UNAUTHORIZED = "UNAUTHORIZED"
    EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED"

    NOT_FOUND = "NOT_FOUND"
    CONFLICT = "CONFLICT"
    ALREADY_EXISTS = "ALREADY_EXISTS"


class IErrorResponse(SQLModel):
    """Base response model for all error responses"""

    status: str = Field(default="error", description="Response status")
    message: str = Field(description="Human-readable error message")
    error_code: ErrorCode = Field(description="Human-readable error code")
    details: list | None = Field(default=None, description="Additional error details")
    meta: Any | None = Field(default=None, description="Metadata for debugging")


class IBadRequestResponse(IErrorResponse):
    """Response schema for bad request errors"""

    error_code: ErrorCode = Field(default=ErrorCode.BAD_REQUEST)


class IUnauthenticatedResponse(IErrorResponse):
    """Response schema for unauthorized errors"""

    error_code: ErrorCode = Field(default=ErrorCode.UNAUTHENTICATED)


class IUnauthorizedResponse(IErrorResponse):
    """Response schema for forbidden errors"""

    error_code: ErrorCode = Field(default=ErrorCode.UNAUTHORIZED)


class INotFoundResponse(IErrorResponse):
    """Response schema for not found errors"""

    error_code: ErrorCode = Field(default=ErrorCode.NOT_FOUND)


class IConflictResponse(IErrorResponse):
    """Response schema for conflict errors"""

    error_code: ErrorCode = Field(default=ErrorCode.CONFLICT)


class IValidationErrorResponse(IErrorResponse):
    """Response schema for validation errors"""

    error_code: ErrorCode = Field(default=ErrorCode.VALIDATION_ERROR)


class IInternalErrorResponse(IErrorResponse):
    """Response schema for internal server errors"""

    error_code: ErrorCode = Field(default=ErrorCode.INTERNAL_ERROR)


class IEmailNotVerifiedResponse(IErrorResponse):
    """Response schema for email not verified errors"""

    error_code: ErrorCode = Field(default=ErrorCode.EMAIL_NOT_VERIFIED)


error_responses = {
    400: {"model": IBadRequestResponse, "description": "Bad Request"},
    401: {"model": IUnauthenticatedResponse, "description": "User not authenticated"},
    403: {"model": IUnauthorizedResponse | IEmailNotVerifiedResponse, "description": "Unauthorized"},
    404: {"model": INotFoundResponse, "description": "Resource not found"},
    409: {"model": IConflictResponse, "description": "Conflict error"},
    422: {"model": IValidationErrorResponse, "description": "Validation error"},
    500: {"model": IInternalErrorResponse, "description": "Internal Server Error"},
}
