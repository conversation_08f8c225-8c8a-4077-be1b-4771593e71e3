import re
from typing import Any

from sqlalchemy import exc

from .app_exceptions import ConflictException


class IntegrityErrorHandler:
    """Generic handler for database integrity constraint violations"""

    @classmethod
    def handle_integrity_error(
        cls, error: exc.IntegrityError, update_data: dict[str, Any], custom_messages: dict[str, str] | None = None
    ) -> None:
        """
        Handle integrity errors and raise appropriate HTTPException

        Args:
            error: The SQLAlchemy IntegrityError
            update_data: The data being updated/inserted
            custom_messages: Optional custom error messages for specific fields
        """
        error_str = str(error.orig)
        custom_messages = custom_messages or {}

        # Extract constraint name and field information from database error
        constraint_info = cls._extract_constraint_info(error_str)

        if constraint_info:
            field_name = constraint_info.get("field")
            constraint_info.get("constraint")

            # Get the value that caused the constraint violation
            field_value = update_data.get(field_name, "unknown") if field_name else "unknown"

            # Use custom message if provided, otherwise generate generic message
            if field_name and field_name in custom_messages:
                message = custom_messages[field_name].format(value=field_value)
            else:
                message = cls._generate_message_from_constraint(constraint_info, field_value)

            raise ConflictException(message=message, details=[{"field": field_name, "value": field_value}])
        else:
            # Generic integrity error when we can't parse the constraint
            raise ConflictException(message="Data constraint violation occurred", details=[{"error": error_str}])

    @classmethod
    def _extract_constraint_info(cls, error_str: str) -> dict[str, str] | None:
        """
        Extract constraint information from database error message

        Returns:
            dict with constraint info or None if not parseable
        """
        # Common patterns for different databases
        patterns = [
            # PostgreSQL: Key (field_name)=(value) already exists
            r"Key \((\w+)\)=\((.*?)\) already exists",
            # PostgreSQL: duplicate key value violates unique constraint "constraint_name"
            r'duplicate key value violates unique constraint "([^"]+)"',
            # MySQL: Duplicate entry 'value' for key 'constraint_name'
            r"Duplicate entry '([^']+)' for key '([^']+)'",
            # SQLite: UNIQUE constraint failed: table.field
            r"UNIQUE constraint failed: \w+\.(\w+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, error_str, re.IGNORECASE)
            if match:
                groups = match.groups()

                # PostgreSQL "Key (field)=(value)" format
                if "Key" in error_str and len(groups) >= 2:
                    return {"field": groups[0], "value": groups[1], "constraint": "unique_constraint"}

                # PostgreSQL constraint name format
                elif "duplicate key value" in error_str.lower():
                    constraint_name = groups[0]
                    # Try to extract field name from constraint name
                    field_match = re.search(r"(\w+)_.*(?:key|unique)", constraint_name)
                    field_name = field_match.group(1) if field_match else None
                    return {"field": field_name, "constraint": constraint_name}

                # MySQL format
                elif "Duplicate entry" in error_str and len(groups) >= 2:
                    return {
                        "value": groups[0],
                        "constraint": groups[1],
                        "field": cls._extract_field_from_constraint(groups[1]),
                    }

                # SQLite format
                elif "UNIQUE constraint failed" in error_str:
                    return {"field": groups[0], "constraint": "unique_constraint"}

        return None

    @classmethod
    def _extract_field_from_constraint(cls, constraint_name: str) -> str | None:
        """Try to extract field name from constraint name"""
        # Common patterns: table_field_key, table_field_unique, etc.
        patterns = [r".*_(\w+)_(?:key|unique|idx)$", r".*\.(\w+)$", r"(\w+)_.*"]

        for pattern in patterns:
            match = re.search(pattern, constraint_name, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    @classmethod
    def _generate_message_from_constraint(cls, constraint_info: dict[str, str], field_value: Any) -> str:
        """Generate error message based on constraint information"""
        field_name = constraint_info.get("field")

        if field_name:
            return f"The {field_name} '{field_value}' is already taken"
        else:
            return f"The value '{field_value}' violates a uniqueness constraint"


# Convenience function for common use cases
def handle_integrity_error(
    error: exc.IntegrityError, update_data: dict[str, Any], custom_messages: dict[str, str] | None = None
) -> None:
    """Convenience function to handle integrity errors"""
    IntegrityErrorHandler.handle_integrity_error(error, update_data, custom_messages)
