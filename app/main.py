from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>
from fastapi_pagination import add_pagination
from loguru import logger
from starlette.middleware.cors import CORSMiddleware

from app.api.v1.api import api_router as api_router_v1
from app.exceptions.handlers import register_exception_handlers
from app.services.billing.sync import stripe_sync_service
from app.services.db.middleware import SQLAlchemyMiddleware
from app.services.db.session import get_engine_args
from app.startup import startup
from config.loader import SettingsFactory

settings = SettingsFactory.settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting application...")
    await startup()
    # Perform initial Stripe sync if enabled
    if settings.STRIPE_SYNC_ON_STARTUP:
        try:
            await stripe_sync_service.sync_on_startup()
        except Exception as e:
            logger.error(f"Failed to perform initial Stripe sync: {e}")
            # Don't prevent startup, just log the error

    yield

    # Shutdown
    logger.info("Shutting down application...")


app = FastAPI(title=settings.PROJECT_NAME, version=settings.API_VERSION, lifespan=lifespan, redirect_slashes=False)

register_exception_handlers(app)
add_pagination(app)
app.add_middleware(
    SQLAlchemyMiddleware,
    db_url=str(settings.DATABASE_URI),
    engine_args=get_engine_args(settings),
)


if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


@app.get("/health/web")
async def read_root() -> dict:
    return {"status": "ok", "message": "Welcome to Hippocampus Cloud!"}


app.include_router(api_router_v1, prefix=settings.API_V1_STR)
