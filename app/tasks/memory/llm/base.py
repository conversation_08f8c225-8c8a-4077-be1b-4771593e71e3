"""
Base LLM Client Interface
"""

from abc import ABC, abstractmethod
from typing import Any

from pydantic import BaseModel

from ..utils import get_logger

logger = get_logger(__name__)


class LLMResponse(BaseModel):
    """LLM Response Model"""

    content: str
    usage: dict[str, Any]
    model: str
    success: bool
    error: str | None = None
    error_context: dict[str, Any] | None = None
    tool_calls: list[Any] | None = None  # Support for function calling

    def __bool__(self) -> bool:
        """Enable response object to be used as boolean value"""
        return self.success

    def __str__(self) -> str:
        """String representation returns content"""
        return self.content


class EmbeddingResponse(BaseModel):
    """Embedding Response Model"""

    embedding: list[float]
    usage: dict[str, Any]
    model: str
    success: bool


class BaseEmbeddingClient(ABC):
    """Embedding Client Base Class"""

    def __init__(self, model: str = None, **kwargs):
        """
        Initialize Embedding Client
        """

        self.default_model = model
        self.config = kwargs

    @abstractmethod
    def generate_embedding(self, text: str, **kwargs) -> EmbeddingResponse:
        """Generate embedding for text"""
        pass

    # def generate_embedding_with_usage(self, text: str, **kwargs) -> list[float] | None:
    #     """Generate embedding for text with usage"""
    #     response = self.generate_embedding(text, **kwargs)
    #     self.update_usage(response.usage)
    #     return response

    # def update_usage(self, usage: dict[str, Any]) -> None:
    #     """Update the usage of the LLM client"""
    #     try:
    #         self.usage.prompt_tokens += usage.get("prompt_tokens", 0)
    #         self.usage.completion_tokens += usage.get("completion_tokens", 0)
    #         self.usage.total_tokens += usage.get("total_tokens", 0)
    #     except Exception as e:
    #         logger.exception(f"Failed to update usage with {usage}: {e!r}")

    # def clear_usage(self) -> None:
    #     """Clear the usage of the LLM client"""
    #     self.usage = TokenUsage()


class BaseLLMClient(ABC):
    """LLM Client Base Class"""

    def __init__(self, model: str = None, **kwargs):
        """
        Initialize LLM Client

        Args:
            model: Default model name
            **kwargs: Other configuration parameters
        """
        self.default_model = model
        self.config = kwargs

    @abstractmethod
    def chat_completion(
        self,
        messages: list[dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 8000,
        **kwargs,
    ) -> LLMResponse:
        """
        Chat Completion Interface

        Args:
            messages: List of conversation messages
            model: Model name, uses default_model if None
            temperature: Generation temperature
            max_tokens: Maximum number of tokens
            **kwargs: Other parameters

        Returns:
            LLMResponse: Response result
        """
        pass

    # def chat_completion(self, messages: list[dict[str, str]], **kwargs) -> LLMResponse:
    #     """
    #     Wrapper of chat completion interface with record
    #     """
    #     response = self.chat_completion(messages, **kwargs)
    #     self.update_usage(response.usage)
    #     logger.info(f"token usage this call: {response.usage!r}")
    #     return response

    def simple_chat(self, prompt: str, **kwargs) -> str:
        """
        Simple chat interface, returns string content

        Args:
            prompt: User input
            **kwargs: Other parameters

        Returns:
            str: AI response content
        """
        messages = [{"role": "user", "content": prompt}]
        response = self.chat_completion(messages, **kwargs)
        # self.update_usage(response.usage)
        # logger.info(f"token usage this call: {response.usage!r}")
        return response.content if response.success else f"Error: {response.error}"

    # def update_usage(self, usage: dict[str, Any]) -> None:
    #     """Update the usage of the LLM client"""
    #     try:
    #         self.usage.prompt_tokens += usage.get("prompt_tokens", 0)
    #         self.usage.completion_tokens += usage.get("completion_tokens", 0)
    #         self.usage.total_tokens += usage.get("total_tokens", 0)
    #     except Exception as e:
    #         logger.exception(f"Failed to update usage with {usage}: {e!r}")

    # def clear_usage(self) -> None:
    #     """Clear the usage of the LLM client"""
    #     self.usage = TokenUsage()

    def get_model(self, model: str = None) -> str:
        """Get the model name to use"""
        return model or self.default_model or self._get_default_model()

    @abstractmethod
    def _get_default_model(self) -> str:
        """Get provider's default model"""
        pass

    def _prepare_messages(self, messages: list[dict[str, str]]) -> list[dict[str, str]]:
        """Preprocess message format, can be overridden by subclasses"""
        return messages

    def _handle_error(self, error: Exception, model: str, error_context: dict[str, Any] = {}) -> LLMResponse:
        """Unified error handling"""
        # self.error_record.append({
        #     "error": repr(error),
        #     "context": error_context,
        # })
        return LLMResponse(
            success=False,
            content="",
            usage={},
            model=model,
            error=str(error),
            error_context=error_context,
        )


class ClientWrapper(BaseLLMClient):
    pass


class MetaClientWithMiddleware(type):
    def __new__(cls, name, bases, attrs):
        # This allow disable the middleware in production environment
        if True:
            bases = (ClientWrapper, *bases)
        return super().__new__(cls, name, bases, attrs)
