"""
AWS Bedrock DeepSeek LLM Client Implementation
"""

import logging
import os
from typing import Any

import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from .base import BaseLLMClient, LLMResponse


class AWSDeepSeekClient(BaseLLMClient):
    """AWS Bedrock DeepSeek Client Implementation"""

    def __init__(
        self,
        region_name: str = None,
        aws_access_key_id: str = None,
        aws_secret_access_key: str = None,
        aws_session_token: str = None,
        model_id: str = "deepseek.r1-v1:0",
        **kwargs,
    ):
        """
        Initialize AWS Bedrock DeepSeek Client

        Args:
            region_name: AWS region name (default: us-east-1)
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
            aws_session_token: AWS session token (for temporary credentials)
            model_id: DeepSeek model ID on AWS Bedrock
            **kwargs: Other configuration parameters
        """
        super().__init__(model=model_id, **kwargs)

        self.region_name = region_name or os.getenv("AWS_DEFAULT_REGION", "us-east-1")
        self.model_id = model_id

        # AWS credentials (can be None to use default credential chain)
        self.aws_access_key_id = aws_access_key_id or os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = aws_secret_access_key or os.getenv("AWS_SECRET_ACCESS_KEY")
        self.aws_session_token = aws_session_token or os.getenv("AWS_SESSION_TOKEN")

        # Lazy load Bedrock client
        self._client = None

    @property
    def client(self):
        """Lazy load AWS Bedrock client"""
        if self._client is None:
            try:
                # Prepare credentials
                client_kwargs = {
                    "service_name": "bedrock-runtime",
                    "region_name": self.region_name,
                }

                # Add credentials if explicitly provided
                if self.aws_access_key_id and self.aws_secret_access_key:
                    client_kwargs.update({
                        "aws_access_key_id": self.aws_access_key_id,
                        "aws_secret_access_key": self.aws_secret_access_key,
                    })
                    if self.aws_session_token:
                        client_kwargs["aws_session_token"] = self.aws_session_token

                self._client = boto3.client(**client_kwargs)

            except Exception as e:
                logging.exception(f"Failed to initialize AWS Bedrock client: {e}")
                raise

        return self._client

    def chat_completion(
        self,
        messages: list[dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 16000,
        tools: list[dict] = None,
        tool_choice: str = None,
        **kwargs,
    ) -> LLMResponse:
        """AWS Bedrock chat completion with function calling support"""
        model_id = model or self.model_id

        try:
            # Prepare messages for AWS Bedrock format
            bedrock_messages = self._prepare_messages(messages)

            # Prepare inference configuration
            inference_config = {
                "maxTokens": max_tokens,
                "temperature": temperature,
                "topP": kwargs.get("top_p", 1.0),
            }

            # Prepare API call parameters
            api_params = {
                "modelId": model_id,
                "messages": bedrock_messages,
                "inferenceConfig": inference_config,
            }

            # Add function calling parameters if provided
            if tools:
                # Convert tools to AWS Bedrock format
                bedrock_tools = self._convert_tools_to_bedrock_format(tools)
                api_params["toolConfig"] = {
                    "tools": bedrock_tools,
                }
                if tool_choice:
                    api_params["toolConfig"]["toolChoice"] = self._convert_tool_choice(tool_choice)

            # Call AWS Bedrock API
            response = self.client.converse(**api_params)

            # Extract response content
            output_message = response["output"]["message"]
            content = ""
            tool_calls = None

            # Process content blocks
            if "content" in output_message:
                for content_block in output_message["content"]:
                    if "text" in content_block:
                        content += content_block["text"]
                    elif "toolUse" in content_block:
                        # Handle tool calls
                        if tool_calls is None:
                            tool_calls = []
                        tool_use = content_block["toolUse"]
                        tool_calls.append({
                            "id": tool_use.get("toolUseId", ""),
                            "type": "function",
                            "function": {"name": tool_use.get("name", ""), "arguments": tool_use.get("input", {})},
                        })

            # Extract usage information
            usage = {}
            if "usage" in response:
                bedrock_usage = response["usage"]
                usage = {
                    "prompt_tokens": bedrock_usage.get("inputTokens", 0),
                    "completion_tokens": bedrock_usage.get("outputTokens", 0),
                    "total_tokens": bedrock_usage.get("totalTokens", 0),
                }

            # Build response
            return LLMResponse(content=content, usage=usage, model=model_id, success=True, tool_calls=tool_calls)

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            logging.exception(f"AWS Bedrock API error [{error_code}]: {error_message}")
            return self._handle_error(e, model_id, api_params)

        except NoCredentialsError as e:
            logging.exception("AWS credentials not found. Please configure your AWS credentials.")
            return self._handle_error(e, model_id, api_params)

        except Exception as e:
            logging.exception(f"AWS Bedrock API call failed: {e}")
            return self._handle_error(e, model_id, api_params)

    def _get_default_model(self) -> str:
        """Get AWS Bedrock DeepSeek default model"""
        return self.model_id

    def _prepare_messages(self, messages: list[dict[str, str]]) -> list[dict[str, Any]]:
        """Preprocess messages to AWS Bedrock format"""
        bedrock_messages = []

        for msg in messages:
            if isinstance(msg, dict) and "role" in msg:
                role = msg["role"]
                content = msg.get("content", "")

                # AWS Bedrock uses "user" and "assistant" roles
                if role in ["user", "assistant"]:
                    bedrock_message = {"role": role, "content": [{"text": content}]}

                    # Handle tool calls in assistant messages
                    if role == "assistant" and "tool_calls" in msg and msg["tool_calls"]:
                        tool_content = []
                        for tool_call in msg["tool_calls"]:
                            if isinstance(tool_call, dict) and "function" in tool_call:
                                tool_content.append({
                                    "toolUse": {
                                        "toolUseId": tool_call.get("id", ""),
                                        "name": tool_call["function"].get("name", ""),
                                        "input": tool_call["function"].get("arguments", {}),
                                    }
                                })
                        if tool_content:
                            bedrock_message["content"].extend(tool_content)

                    bedrock_messages.append(bedrock_message)

                elif role == "system":
                    # AWS Bedrock doesn't support system messages in converse API
                    # Convert to user message with system context
                    bedrock_messages.append({"role": "user", "content": [{"text": f"System: {content}"}]})

                elif role == "tool":
                    # Handle tool response messages
                    tool_call_id = msg.get("tool_call_id", "")
                    bedrock_messages.append({
                        "role": "user",
                        "content": [{"toolResult": {"toolUseId": tool_call_id, "content": [{"text": content}]}}],
                    })

                else:
                    logging.warning(f"Unknown message role: {role}, treating as user message")
                    bedrock_messages.append({"role": "user", "content": [{"text": content}]})
            else:
                logging.warning(f"Invalid message format: {msg}")

        return bedrock_messages

    def _convert_tools_to_bedrock_format(self, tools: list[dict]) -> list[dict[str, Any]]:
        """Convert OpenAI-style tools to AWS Bedrock format"""
        bedrock_tools = []

        for tool in tools:
            if tool.get("type") == "function" and "function" in tool:
                function_def = tool["function"]
                bedrock_tool = {
                    "toolSpec": {
                        "name": function_def.get("name", ""),
                        "description": function_def.get("description", ""),
                        "inputSchema": {"json": function_def.get("parameters", {})},
                    }
                }
                bedrock_tools.append(bedrock_tool)

        return bedrock_tools

    def _convert_tool_choice(self, tool_choice: str) -> dict[str, Any]:
        """Convert tool choice to AWS Bedrock format"""
        if tool_choice == "auto":
            return {"auto": {}}
        elif tool_choice == "none":
            return {"any": {}}
        elif isinstance(tool_choice, dict) and "function" in tool_choice:
            return {"tool": {"name": tool_choice["function"]["name"]}}
        else:
            return {"auto": {}}

    @classmethod
    def from_env(cls) -> "AWSDeepSeekClient":
        """Create AWS Bedrock DeepSeek client from environment variables"""
        return cls()

    def __str__(self) -> str:
        return f"AWSDeepSeekClient(model={self.model_id}, region={self.region_name})"
