from ..utils import get_logger

logger = get_logger(__name__)

def _get_longest_word_length(text: str) -> int:
    """
    Return the length of the longest whitespace-delimited segment in the text.
    """
    if not text:
        return 0

    segments = text.split()
    if not segments:
        return len(text)

    return max(len(segment) for segment in segments)


def _should_use_character_count(text: str, threshold: int = 100) -> bool:
    """
    Decide whether to use character-based counting.

    If the longest whitespace-delimited segment exceeds the threshold, fall back
    to character counting. Otherwise, use word counting.
    """
    try:
        longest_segment_length = _get_longest_word_length(text)
        return longest_segment_length > threshold
    except Exception as exc:
        logger.warning(f"Fallback to character counting due to error: {exc}")
        return True


def get_token_count(text: str, model_name: str = "gpt-4.1") -> int:
    """
    Lightweight token approximation for pricing and batching.

    Heuristic:
    - If the longest whitespace-delimited segment > 100 characters, count characters
      (suitable for CJK or unspaced text).
    - Otherwise, count space-delimited words (suitable for English-like text).

    The parameter `model_name` is kept for API compatibility but not used here.
    """
    if not text:
        return 0

    if _should_use_character_count(text):
        return len(text)

    return len(text.split())
