"""
MemU Memory Agent - Action-Based Architecture

Modern memory management system with function calling interface.
Each operation is implemented as a separate action module for modularity and maintainability.
All configuration is now database-driven, no file-based config needed.
"""

import inspect
import json
from collections.abc import Callable
from datetime import datetime
from typing import Any

# Database imports
from ..core import MemoryCore
from ..llm import BaseLL<PERSON>lient

# from ..repository import MemoryRepository
from ..utils import Timer, get_logger
from .actions import ACTION_REGISTRY
from .actions.base_action import ActionContext

logger = get_logger(__name__)


class MemoryAgent:
    """
    Modern Memory Agent with Action-Based Architecture

    Uses independent action modules for each memory operation:
    - add_activity_memory: Add new activity memory content with strict formatting
    - get_default_categories: Get default categories for the project
    - link_related_memories: Find and link related memories using database embedding search
    - generate_memory_suggestions: Generate suggestions for memory categories
    - update_memory_with_suggestions: Update memory categories based on suggestions

    Each action is implemented as a separate module in the actions/ directory.
    All configuration is stored in the database.
    """

    def __init__(
        self,
        *,
        llm_client: BaseLLMClient,
        embedding_client: BaseLLMClient | None = None,
        action_context: ActionContext,
    ):
        """
        Initialize Memory Agent

        Args:
            llm_client: LLM client for processing conversations
            embedding_client: Embedding client for generating embeddings
            action_context: Action context for the memory agent
        """
        # Initialize memory core
        self.memory_core = MemoryCore(llm_client, embedding_client, action_context)
        self.memory_repo = self.memory_core.memory_repo

        # Initialize actions
        self.actions = {}
        self._load_actions()

        # Build function registry for compatibility
        self.function_registry = self._build_function_registry()

        logger.info(f"Memory Agent initialized: {len(self.actions)} actions available")

    def _load_actions(self):
        """Load all available actions from the registry"""
        for action_name, action_class in ACTION_REGISTRY.items():
            try:
                action_instance = action_class(self.memory_core)
                self.actions[action_name] = action_instance
                logger.debug(f"Loaded action: {action_name}")
            except Exception as e:
                logger.exception(f"Failed to load action {action_name}: {e}")

    def _build_function_registry(self) -> dict[str, Callable]:
        """Build registry of callable functions from actions"""
        registry = {}
        for action_name, action in self.actions.items():
            registry[action_name] = action.execute
        return registry

    # ================================
    # Smart Conversation Processing
    # ================================

    async def run(
        self,
        conversation: list[dict[str, str]],
        user_id: str,
        agent_id: str | None = None,
        conversation_id: str | None = None,
        user_name: str = "user",
        agent_name: str = "assistant",
        session_date: str | None = None,
        max_iterations: int = 20,
    ) -> dict[str, Any]:
        """
        Intelligent conversation processing using iterative function calling

        This function allows the LLM to autonomously decide which memory operations to perform
        through function calling, iterating until the LLM decides it's complete or max iterations reached.

        Args:
            conversation: List of conversation messages
            user_id: User ID for database operations
            agent_id: Agent ID for database operations (optional)
            conversation_id: Conversation ID for database operations (optional)
            user_name: Name of the user (default: "user")
            agent_name: Name of the agent (default: "assistant")
            session_date: Session date
            max_iterations: Maximum number of function calling iterations (default: 20)

        Returns:
            Dict containing processing results and database operations
        """
        try:
            if not conversation or not isinstance(conversation, list):
                return {
                    "success": False,
                    "error": "Invalid conversation format. Expected list of message dictionaries.",
                }

            if not user_name:
                return {"success": False, "error": "User name is required."}

            if not user_id:
                return {"success": False, "error": "User ID is required for database operations."}

            session_date = session_date or datetime.now().isoformat()

            logger.info(f"🚀 Starting iterative conversation processing for {user_name} (agent: {agent_name})")
            Timer.clear()
            Timer.tick("init")

            self.memory_core.llm_client.clear_usage()
            self.memory_core.embedding_client.clear_usage()

            # Convert conversation to text for processing
            conversation_text = self._convert_conversation_to_text(conversation)
            self.memory_core.action_context.conversation_text = conversation_text

            # Initialize results tracking
            result = {
                # "success": True,
                "conversation_length": len(conversation),
                "iterations": 0,
                "function_calls": [],
                # "memories_created": [],
                # "memory_histories_created": [],
                # "database_errors": [],
                # "processing_log": [],
            }

            # Get function schemas for LLM
            function_schemas = self.get_functions_schema()

            # Build initial system message
            system_message = f"""You are a memory processing agent. Follow this structured process to analyze and store conversation information for "{user_name}" and "{agent_name}":

CONVERSATION TO PROCESS:
{conversation_text}

USER: {user_name}
AGENT: {agent_name}
SESSION DATE: {session_date}

PROCESSING WORKFLOW:
1. STORE TO ACTIVITY: Call add_activity_memory with the COMPLETE RAW CONVERSATION TEXT as the 'content' parameter. This will automatically append to existing activity memories. DO NOT extract, modify, or summarize the conversation - pass the entire original conversation text exactly as shown above.

2. THEORY OF MIND: Call run_theory_of_mind to analyze the subtle information behind the conversation and extract the theory of mind of the characters.

3. GENERATE SUGGESTIONS: Call generate_memory_suggestions with the available memory items to get suggestions for what should be added to each category.

4. UPDATE CATEGORIES: For each category that should be updated (based on suggestions), call update_memory_with_suggestions to update that category with the new memory items and suggestions. This will return structured modifications.

5. LINK MEMORIES: For each category that was modified, call link_related_memories with link_all_items=true to add relevant links between ALL memories in that category.

6. CLUSTER MEMORIES: Call cluster_memories to cluster the memories into different categories.

IMPORTANT GUIDELINES:
- Step 1: CRITICAL: For add_activity_memory, the 'content' parameter MUST be the complete original conversation text exactly as shown above. Do NOT modify, extract, or summarize it.
- Step 2: Use both the original conversation and the extracted activity memoryitems from step 1 for the theory of mind analysis
- Step 3: Use BOTH the extracted memory items from step 1 and theory-of-mind items from step 2 for generating suggestions. You can simply concatenate the two lists of memory items and pass them to the subsequent function.
- Step 4: Use the memory suggestions from step 3 to update EVERY memory categories in suggestions.
- Step 5-6: Use the new memory items returned from step 4 for linking and clustering memories. DO NOT include the memory items returned from step 1 and 2.
- Each memory item should have its own memory_id and focused content
- Follow the suggestions when updating categories
- The update_memory_with_suggestions function will return structured format with memory_id and content
- Always link related memories after updating categories by setting link_all_items=true

Start with step 1 and work through the process systematically. When you complete all steps, respond with "PROCESSING_COMPLETE"."""

            # Start iterative function calling
            messages = [{"role": "system", "content": system_message}]

            for iteration in range(max_iterations):
                result["iterations"] = iteration + 1
                logger.info(f"🔄 Iteration {iteration + 1}/{max_iterations}")
                self.memory_core.llm_client.set_action_meta({"iteration": iteration + 1})

                try:
                    # Call LLM with function calling enabled
                    Timer.tick(f"{iteration + 1}:agent")
                    response = self.memory_core.llm_client.chat_completion(
                        messages=messages,
                        tools=[{"type": "function", "function": schema} for schema in function_schemas],
                        tool_choice="auto",
                        temperature=0.3,
                    )
                    Timer.tick(f"{iteration + 1}:parse")

                    if not response.success:
                        logger.error(f"LLM call failed: {response.error}")
                        break

                    # Add assistant response to conversation
                    assistant_message = {"role": "assistant", "content": response.content or ""}

                    # Check if processing is complete
                    if response.content and "PROCESSING_COMPLETE" in response.content:
                        logger.info("✅ LLM indicated processing is complete")
                        # result["processing_log"].append(f"Iteration {iteration + 1}: Processing completed")
                        break

                    # Handle tool calls if present
                    if response.tool_calls:
                        assistant_message["tool_calls"] = response.tool_calls
                        messages.append(assistant_message)

                        # Execute each tool call
                        for idx_tool, tool_call in enumerate(response.tool_calls):
                            function_name = tool_call.function.name

                            try:
                                arguments = json.loads(tool_call.function.arguments)
                            except json.JSONDecodeError as e:
                                logger.exception(f"Failed to parse function arguments: {e}")
                                continue

                            self.memory_core.llm_client.set_action_meta({
                                "iteration": iteration + 1,
                                "function_name": function_name,
                            })
                            logger.info(f"🔧 Calling function: {function_name}")

                            Timer.tick(f"{iteration + 1}:action:{idx_tool}:{function_name}")
                            function_result = await self.call_function(function_name, arguments)
                            Timer.tick(f"{iteration + 1}:action:{idx_tool}:{function_name}:end")

                            logger.info(
                                f"Success: {function_result.get('success')}, time use {Timer.history[-1][1]:.2f}s, {function_result.get('message', function_result.get('error'))}"
                            )

                            # Track function call
                            call_record = {
                                "iteration": iteration + 1,
                                "function_idx": idx_tool,
                                "function": function_name,
                                "arguments": arguments,
                                "result": function_result,
                            }
                            result["function_calls"].append(call_record)

                            # Add tool result to conversation
                            tool_message = {
                                "role": "tool",
                                "tool_call_id": getattr(tool_call, "id", f"call_{iteration}_{function_name}"),
                                "content": json.dumps(function_result, ensure_ascii=False),
                            }
                            messages.append(tool_message)

                            # result["processing_log"].append(
                            #     f"Iteration {iteration + 1}: Called {function_name} - "
                            #     + (
                            #         "Success"
                            #         if function_result.get("success")
                            #         else f"Failed: {function_result.get('error', 'Unknown error')}"
                            #     )
                            # )
                    else:
                        # No tool calls, add response and continue
                        messages.append(assistant_message)
                        # if response.content:
                        #     result["processing_log"].append(f"Iteration {iteration + 1}: {response.content[:100]}...")

                except Exception as e:
                    logger.exception(f"Error in iteration {iteration + 1}: {e}")
                    # result["processing_log"].append(f"Iteration {iteration + 1}: Error - {e!s}")
                    break

            # if result["success"]:
            #     post_jobs_result = await self.memory_core.memory_repo.do_post_jobs()

            # Finalize result
            Timer.tick("finalize")
            # if result["iterations"] >= max_iterations:
            #     logger.warning(f"⚠️ Reached maximum iterations ({max_iterations})")
            #     result["processing_log"].append(f"Reached maximum iterations ({max_iterations})")

            # result["operation_record"] = {
            #     operation: list(record) for operation, record in self.memory_repo.operation_record.items()
            # }
            # result["memory_histories_created"] = self.memory_repo.memory_history_created

            result["success"] = True
            result["token_usage"] = self.summarize_token_usage()

            logger.info(f"🎉 Conversation processing completed after {result['iterations']} iterations")
            logger.info(f"🔧 Made {len(result['function_calls'])} function calls")
            logger.info(f"📚 {len(self.memory_repo.operation_record['ADD'])} memory records added")
            logger.info(f"🔄 {len(self.memory_repo.operation_record['UPDATE'])} memory records updated")
            logger.info(f"💾 {len(self.memory_repo.operation_record['TOUCH'])} memory records touched")
            logger.info(f"🗑️ {len(self.memory_repo.operation_record['DELETE'])} memory records removed")
            logger.info(f"📚 {len(self.memory_repo.memory_history_created)} new memory history records")
            logger.info(f"💰 Total token used: {result['token_usage']['total']['total_tokens']}")
            logger.info(f"💰 LLM token used: {result['token_usage']['llm_client']}")
            logger.info(f"💰 Embedding token used: {result['token_usage']['embedding_client']}")
            # if result["database_errors"]:
            #     logger.warning(f"⚠️ Encountered {len(result['database_errors'])} database errors")

            Timer.tick("end")
            result["time_use"] = Timer.history
            return result

        except Exception as e:
            logger.exception(f"Error in conversation processing: {e!r}")

            Timer.tick("end")
            return result | {
                "success": False,
                "error": repr(e),
                "token_usage": self.summarize_token_usage(),
                "time_use": Timer.history,
            }

    def _convert_conversation_to_text(self, conversation: list[dict]) -> str:
        """Convert conversation list to text format for LLM processing"""
        if not conversation or not isinstance(conversation, list):
            return ""

        text_parts = []
        for message in conversation:
            if "role" in message:
                role = message["role"]
                if role == "participant":
                    role = message.get("name", "unknown")
            else:
                role = "unknown"
            content = message.get("content", "")
            text_parts.append(f"{role.upper()}: {content.strip()}")

        return "\n".join(text_parts)

    def summarize_token_usage(self) -> dict[str, dict[str, Any]]:
        """Summarize the token usage of the memory agent"""
        total_usage = {}
        for client in [self.memory_core.llm_client, self.memory_core.embedding_client]:
            if client:
                for key, value in client.usage.model_dump().items():
                    if key in total_usage:
                        total_usage[key] += value
                    else:
                        total_usage[key] = value
        return {
            "llm_client": self.memory_core.llm_client.usage,
            "embedding_client": self.memory_core.embedding_client.usage if self.memory_core.embedding_client else None,
            "total": total_usage,
        }

    # ================================
    # Function Calling Interface
    # ================================

    def get_functions_schema(self) -> list[dict[str, Any]]:
        """
        Get OpenAI-compatible function schemas for all memory functions

        Returns:
            List of function schemas that can be used with OpenAI function calling
        """
        schemas = []
        for action in self.actions.values():
            try:
                schema = action.get_schema()
                schemas.append(schema)
            except Exception as e:
                logger.exception(f"Failed to get schema for action {action.action_name}: {e}")
        return schemas

    async def call_function(self, function_name: str, arguments: dict[str, Any]) -> dict[str, Any]:
        """
        Call a memory function with the provided arguments

        Args:
            function_name: Name of the function to call
            arguments: Arguments to pass to the function
            dependencies: Action dependencies containing context and repository

        Returns:
            Dict containing the function result
        """
        try:
            if function_name not in self.actions:
                return {
                    "success": False,
                    "error": f"Unknown function: {function_name}",
                    "available_functions": list(self.actions.keys()),
                }

            # Get the action instance
            action = self.actions[function_name]

            # Execute the action with dependencies (handle both sync and async)
            if inspect.iscoroutinefunction(action.execute):
                result = await action.execute(**arguments)
            else:
                result = action.execute(**arguments)

            logger.debug(f"Function call successful: {function_name}")
            return result

        except Exception as e:
            error_result = {
                "success": False,
                "error": repr(e),
                "function_name": function_name,
                "timestamp": datetime.now().isoformat(),
            }
            logger.exception(f"Function call failed: {function_name} - {e!r}")
            return error_result

    def validate_function_call(self, function_name: str, arguments: dict[str, Any]) -> dict[str, Any]:
        """
        Validate a function call before execution

        Args:
            function_name: Name of the function
            arguments: Arguments for the function

        Returns:
            Dict with validation result
        """
        try:
            if function_name not in self.actions:
                return {
                    "valid": False,
                    "error": f"Unknown function: {function_name}",
                    "available_functions": list(self.actions.keys()),
                }

            # Use the action's validation method
            action = self.actions[function_name]
            return action.validate_arguments(arguments)

        except Exception as e:
            return {"valid": False, "error": f"Validation error: {e!s}"}

    # ================================
    # Utility Methods
    # ================================

    def get_function_list(self) -> list[str]:
        """Get list of available function names"""
        return list(self.actions.keys())

    def get_function_description(self, function_name: str) -> str:
        """Get description for a specific function"""
        if function_name in self.actions:
            try:
                schema = self.actions[function_name].get_schema()
                return schema.get("description", "No description available")
            except Exception:
                return "Description not available"
        return "Function not found"

    def get_action_instance(self, action_name: str):
        """Get a specific action instance (for advanced usage)"""
        return self.actions.get(action_name)

    def stop_action(self) -> dict[str, Any]:
        """
        Stop current operations

        Returns:
            Dict containing stop result
        """
        try:
            self.memory_core._stop_flag.set()
            logger.info("Memory Agent: Stop flag set")

            return {
                "success": True,
                "message": "Stop signal sent to Memory Agent operations",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.exception(f"Error stopping operations: {e}")
            return {"success": False, "error": str(e)}

    def reset_stop_flag(self):
        """Reset the stop flag to allow new operations"""
        self.memory_core._stop_flag.clear()
        logger.debug("Memory Agent: Stop flag reset")

    def get_status(self) -> dict[str, Any]:
        """Get status information about the memory agent"""
        return {
            "agent_name": "memory_agent",
            "architecture": "action_based",
            "memory_types": [],  # Now database-driven, no static config needed
            "processing_order": [],  # Now database-driven, no static config needed
            "storage_type": "database",
            "config_source": "database",
            "total_actions": len(self.actions),
            "available_actions": list(self.actions.keys()),
            "total_functions": len(self.function_registry),
            "available_functions": list(self.function_registry.keys()),
            "function_calling_enabled": True,
            "stop_flag_set": self.memory_core._stop_flag.is_set(),
            "embedding_capabilities": {
                "embeddings_enabled": self.memory_core.embeddings_enabled,
                "embedding_client": str(type(self.memory_core.embedding_client))
                if self.memory_core.embedding_client
                else None,
            },
            "config_details": {
                "total_file_types": 0,  # Now database-driven, no static file types
                "categories_from_config": False,
                "config_structure": "Database-driven",
            },
            "last_updated": datetime.now().isoformat(),
        }
