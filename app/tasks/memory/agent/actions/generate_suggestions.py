"""
Generate Memory Suggestions Action

Analyzes new memory items and suggests what should be added to different memory categories.
"""

from typing import Any

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class GenerateMemorySuggestionsAction(BaseAction):
    """
    Generate suggestions for what memory content should be added to different categories
    based on new memory items from conversations.
    """

    @property
    def action_name(self) -> str:
        return "generate_memory_suggestions"

    def get_schema(self) -> dict[str, Any]:
        """Return OpenAI-compatible function schema"""
        return {
            "name": self.action_name,
            "description": "Analyze new memory items and generate suggestions for what should be added to different memory categories",
            "parameters": {
                "type": "object",
                "properties": {
                    "new_memory_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "memory_id": {"type": "string"},
                                "content": {"type": "string"},
                                "mentioned_at": {"type": "string"},
                            },
                            "required": ["memory_id", "content", "mentioned_at"],
                        },
                        "description": "List of new memory items from the conversation",
                    },
                },
                "required": ["new_memory_items"],
            },
        }

    async def execute(
        self,
        *,
        new_memory_items: list[dict[str, str]] = [],
        available_categories: list[str] | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """
        Generate suggestions for memory category updates based on new conversation content

        Args:
            new_memory_items: List of new memory items from conversation
            available_categories: List of available memory categories

        Returns:
            Dict containing generated suggestions for each category
        """
        try:
            if not new_memory_items:
                return self._add_metadata({"success": False, "error": "No new memory items provided"})

            available_categories = await self.memory_repo.get_memory_categories("basic")
            if not available_categories:
                logger.warning("No available categories found")
                return self._add_metadata({"success": False, "error": "No available categories found"})

            available_categories = [category.name for category in available_categories]

            project_categories = await self.memory_repo.get_project_memory_categories()
            extra_prompts = {
                category.name: category.config.get("prompt", "")
                for category in project_categories
                if category.config and category.config.get("prompt", "")
            }

            # Call LLM to generate suggestions
            response = await self._generate_suggestions_with_llm(new_memory_items, available_categories, extra_prompts)

            if not response.strip():
                return self._add_metadata({"success": False, "error": "LLM returned empty suggestions"})

            # Parse text response
            suggestions = self._parse_suggestions_from_text(response.strip(), available_categories)

            return self._add_metadata({
                "success": True,
                "suggestions": suggestions,
                "categories_analyzed": available_categories,
                "message": f"Generated self-contained suggestions for {len(suggestions)} categories based on {len(new_memory_items)} memory items",
            })

        except Exception as e:
            return self._handle_error(e)

    async def _generate_suggestions_with_llm(
        self,
        new_memory_items: list[dict[str, str]],
        available_categories: list[str],
        extra_prompts: dict[str, str] = {},
    ) -> str:
        memory_items_text = "\n".join([f"- {item['content']}" for item in new_memory_items])

        extra_category_prompt = "".join([
            f"- **{category}**: {prompt}\n"
            for category, prompt in extra_prompts.items()
            if category in available_categories
        ])

        suggestions_prompt = f"""You are an expert in analyzing the provided memory items for the user and suggesting the memory items that should be added to each memory category.

New Memory Items:
{memory_items_text}

Available Categories: {", ".join(available_categories)}

**CRITICAL REQUIREMENT: Suggestions must be SELF-CONTAINED MEMORY ITEMS**

**SELF-CONTAINED MEMORY REQUIREMENTS:**
- EVERY activity item must be complete and standalone
- ALWAYS include the full subject (do not use "she/he/they/it")
- NEVER use pronouns that depend on context (no "she", "he", "they", "it")
- Include specific names, places, dates, and full context in each item
- Each activity should be understandable without reading other items
- Include all relevant details, emotions, and outcomes in the activity description

**CATEGORY-SPECIFIC REQUIREMENTS:**

For each category, analyze the new memory items and suggest what specific information should be extracted and added to that category:

- **activity**: Detailed description of the conversation, including the time, place, and people involved
- **profile**: ONLY basic personal information (age, location, occupation, education, family status, demographics) - EXCLUDE events, activities, things they did
- **event**: Specific events, dates, milestones, appointments, meetings, activities with time references
{extra_category_prompt}- **Other categories**: Relevant information for each specific category (if any)

**CRITICAL DISTINCTION - Profile vs Activity/Event:**
- Profile (GOOD): "Alice lives in San Francisco", "Alice is 28 years old", "Alice works at TechFlow Solutions"
- Profile (BAD): "Alice went hiking" (this is activity), "Alice attended workshop" (this is event)
- Activity/Event (GOOD): "Alice went hiking in Blue Ridge Mountains", "Alice attended photography workshop"

**SUGGESTION REQUIREMENTS:**
- Specify that memory items should include "User" as the subject
- Mention specific names, places, titles, and dates that should be included
- Ensure suggestions lead to complete, self-contained memory items
- Avoid suggesting content that would result in pronouns or incomplete sentences
- For profile: Focus ONLY on stable, factual, demographic information
- Be comprehensive: suggest all relevant information for each category
- If one input memory item involves information that belongs to multiple categories, you should reasonably separate the information and provide suggestions to all involved categories
- **IMPORTANT** If an input memory item use modal adverbs (perhaps, probably, likely, etc.) to indicate an uncertain inference, keep the modal adverbs as-is in your suggestions

**OUTPUT INSTRUCTIONS:**
- **IMPORTANT** NEVER suggest categories that are not in the Available Categories. You should only generate suggestions for categories that are in the Available Categories. Note that the Available Categories are: {", ".join(available_categories)}
- Only output categories where there are suggestions for new memory items

**OUTPUT FORMAT:**

**Category: [category_name]**
- Suggestion: [What specific self-contained content should be added to this category, ensuring full subjects and complete context]
- Suggestion: [What specific self-contained content should be added to this category, ensuring full subjects and complete context]
... other suggestions ...

**Category: [category_name]**
- Suggestion: [What specific self-contained content should be added to this category, ensuring full subjects and complete context]

... other categories ...
"""

        # Call LLM to generate suggestions
        response = self.llm_client.simple_chat(suggestions_prompt)

        # logger.info(f"Prompt: {suggestions_prompt}")
        # logger.info(f"Response: {response}")

        return response

    def _parse_suggestions_from_text(
        self, response_text: str, available_categories: list[str]
    ) -> dict[str, dict[str, str]]:
        """Parse suggestions from text format response"""
        suggestions = {}

        lines = response_text.split("\n")
        current_category = None

        for line in lines:
            line = line.strip()

            if line.startswith("**Category:") and line.endswith("**"):
                category_name = line.replace("**Category:", "").replace("**", "").strip()
                if category_name in available_categories:
                    current_category = category_name
                    suggestions[current_category] = ""

            elif current_category and line.startswith("- Suggestion:"):
                suggestion_text = line.replace("- Suggestion:", "").strip()
                suggestions[current_category] += f"{suggestion_text}\n"

        suggestions = {k: v for k, v in suggestions.items() if v.strip()}

        return suggestions
