"""
Update Memory with Suggestions Action

Updates memory categories based on new memory items and suggestions, supporting different operation types:
- ADD: Add new content to database
- UPDATE: Modify existing content in database
- DELETE: Delete specific content from database
- TOUCH: Keep content unchanged but update timestamp in database
"""

from datetime import datetime
from typing import Any

import numpy as np

from app.modules.memory.models import Memory

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class UpdateMemoryWithSuggestionsAction(BaseAction):
    """
    Update memory categories based on new memory items and suggestions,
    supporting different operation types (ADD, UPDATE, DELETE, TOUCH) with database operations.
    """

    def __init__(self, memory_core):
        super().__init__(memory_core)
        self.description = "Update memory categories with different operation types (ADD, UPDATE, DELETE, TOUCH) using database operations"

        self.max_history_items = 100

    @property
    def action_name(self) -> str:
        return "update_memory_with_suggestions"

    def get_schema(self) -> dict[str, Any]:
        """Return OpenAI-compatible function schema"""
        return {
            "name": self.action_name,
            "description": "Update memory categories with different operation types (ADD, UPDATE, DELETE, TOUCH) using database operations",
            "parameters": {
                "type": "object",
                "properties": {
                    "category": {
                        "type": "string",
                        "description": "Memory category to operate on",
                    },
                    "suggestion": {
                        "type": "string",
                        "description": "Suggestion for what content should be processed in this category. One piece of suggestion per line.",
                    },
                    "session_date": {
                        "type": "string",
                        "description": "Session date for the memory items (format: YYYY-MM-DD)",
                    },
                },
                "required": ["category", "suggestion"],
            },
        }

    async def execute(
        self,
        *,
        category: str,
        suggestion: str,
        session_date: str | None = None,
    ) -> dict[str, Any]:
        """
        Update memory category with different operation types based on suggestions using database

        Args:
            category: Memory category to operate on
            suggestion: Suggestion for what content should be processed
            session_date: Session date for the memory items (format: YYYY-MM-DD)

        Returns:
            Dict containing the operations performed in structured format
        """
        try:
            if not suggestion.strip():
                return self._add_metadata({"success": False, "error": "No suggestion provided"})

            available_categories = await self.memory_repo.get_memory_categories("basic")
            available_categories = [category.name for category in available_categories]

            if not available_categories:
                return self._add_metadata({"success": False, "error": "No available categories found"})

            if category not in available_categories:
                return self._add_metadata({
                    "success": False,
                    "error": f"Invalid category '{category}'. Available: {available_categories}",
                })

            if not session_date:
                session_date = self.action_context.session_date or datetime.now().strftime("%Y-%m-%d")

            existing_memory_in_category = await self.memory_repo.get_memory_items_in_category(category)

            if len(existing_memory_in_category) > self.max_history_items:
                existing_memory_in_category = await self.select_top_k_related(
                    existing_memory_in_category, suggestion, self.max_history_items
                )

            existing_memory_dict = {mem.memory_id: mem for mem in existing_memory_in_category}

            operation_response = await self._analyze_memory_operations_with_llm(
                category, suggestion, existing_memory_in_category
            )

            if not operation_response.strip():
                return self._add_metadata({"success": False, "error": "LLM returned empty memory operations"})

            operation_list = self._parse_operation_response(operation_response)
            operation_succeeded = []

            new_items = []
            updated_items = []

            for operation in operation_list:
                if operation["operation"] == "ADD":
                    if not operation["memory_content"]:
                        continue

                    memory_item = {
                        "memory_id": self._generate_memory_id(),
                        "mentioned_at": session_date,
                        "content": operation["memory_content"],
                    }

                    new_items.append(memory_item)
                    await self.memory_repo.add_memory_item(category, memory_item)
                    operation_succeeded.append(operation)

                elif operation["operation"] == "UPDATE":
                    if not operation["target_id"] or not operation["memory_content"]:
                        continue

                    memory_id = operation["target_id"]
                    if memory_id not in existing_memory_dict:
                        logger.warning(f"Memory item to update [{memory_id}] not exists in category [{category}]")
                        continue

                    memory_item = {
                        "memory_id": memory_id,
                        "mentioned_at": session_date,
                        "content": operation["memory_content"],
                    }

                    updated_items.append(memory_item)
                    await self.memory_repo.update_memory_item(category, memory_item, existing_memory_dict[memory_id])
                    operation_succeeded.append(operation)

                elif operation["operation"] == "DELETE":
                    if not operation["target_id"]:
                        continue

                    memory_id = operation["target_id"]
                    if memory_id not in existing_memory_dict:
                        logger.warning(f"Memory item to delete [{memory_id}] not exists in category [{category}]")
                        continue

                    await self.memory_repo.delete_memory_item(category, memory_id, existing_memory_dict[memory_id])
                    operation_succeeded.append(operation)

                elif operation["operation"] == "TOUCH":
                    if not operation["target_id"]:
                        continue

                    memory_id = operation["target_id"]
                    if memory_id not in existing_memory_dict:
                        logger.warning(f"Memory item to touch [{memory_id}] not exists in category [{category}]")
                        continue

                    await self.memory_repo.touch_memory_item(category, memory_id, existing_memory_dict[memory_id])
                    operation_succeeded.append(operation)

            return self._add_metadata({
                "success": True,
                "category": category,
                "operation_info": operation_list,
                "new_memory_items": new_items,
                # "updated_memory_items": updated_items,
                "message": f"Successfully updated memory for {category}, {(len(operation_list))} operations in total, {len(new_items)} new memory items, {len(updated_items)} memory updated",
            })
        except Exception as e:
            return self._add_metadata({"success": False, "error": f"Failed to update memory: {e!s}"})

    def _format_existing_content(self, existing_memories: list[Memory]) -> str:
        """Format existing memory content for LLM analysis"""

        # Wu: the current memory_id is 18 characters long, consider remap index if LLM cannot repeat such a long id correctly
        return "\n".join([f"[Memory ID: {mem.memory_id}] {mem.content}" for mem in existing_memories])

    async def _analyze_memory_operations_with_llm(
        self, category: str, suggestion: str, existing_memories: list[Memory]
    ) -> str:
        existing_content_text = (
            self._format_existing_content(existing_memories) if existing_memories else "No existing content"
        )

        analysis_prompt = f"""You are an expert in analyzing the following memory update scenario and determining the memory operations that should be performed.

Memory Category: {category}

Existing Memory Items in {category}:
{existing_content_text}

Memory Update Suggestion:
{suggestion}

**CRITICAL REQUIREMENT: The object of memory operations must be SELF-CONTAINED MEMORY ITEMS**

**SELF-CONTAINED MEMORY REQUIREMENTS:**
- EVERY activity item must be complete and standalone
- ALWAYS include the full subject (do not use "she/he/they/it")
- NEVER use pronouns that depend on context (no "she", "he", "they", "it")
- Include specific names, places, dates, and full context in each item
- Each activity should be understandable without reading other items
- Include all relevant details, emotions, and outcomes in the activity description

**OPERATION TYPES:**
1. **ADD**: Add completely new memory items that doesn't exist in Existing Memory Items
2. **UPDATE**: Modify or enhance existing memory items with new details
3. **DELETE**: Remove outdated, incorrect, or irrelevant memory items
4. **TOUCH**: Touch memory items that already exists in current content (only for updating last-mentioned timestamp)

**ANALYSIS GUIDELINES:**
- Read the Memory Update Suggestion carefully to determine what new memory items are offered
- Read the Existing Memory Items to view all memory items that are already present
- Determine the most appropriate operation type FOR EACH NEW MEMORY ITEM based on the new information and existing content
- **Use ADD for:** New memory items that are not covered in existing content
- **Use UPDATE for:** New memory items that provide updated details for existing memory items
- **Use DELETE for:** Existing memory items that are outdated/incorrect based on new memory items
- **Use TOUCH for:** Existing memory items that already covers the new memory items adequately

**OUTPUT INSTRUCTIONS:**
- **IMPORTANT** Output ALL necessary memory operations. It is common that you should perform different operations for different specific memory items
- For ADD and UPDATE operations, provide the content of the new memory items following the self-contained memory requirements
- For UPDATE, DELETE, and TOUCH operations, provide the target memory IDs associated with the memory items
- If there are multiple actions for an operation type (e.g, multiple ADDs), output them separately, do not put them in a single **OPERATION:** block
- **IMPORTANT** If a memory item in suggestion uses modal adverbs (perhaps, probably, likely, etc.) to indicate an uncertain inference, keep the modal adverbs as-is in your output

**OUTPUT FORMAT:**

**OPERATION:** [ADD/UPDATE/DELETE/TOUCH]
- Target Memory ID: [Only if operation is UPDATE, DELETE, or TOUCH][Memory ID of the memory item that is the target of the operation]
- Memory Item Content: [Only if operation is ADD or UPDATE][Content of the new memory item]

**OPERATION:** [ADD/UPDATE/DELETE/TOUCH]
- Target Memory ID: [Only if operation is UPDATE, DELETE, or TOUCH][Memory ID of the memory item that is the target of the operation]
- Memory Item Content: [Only if operation is ADD or UPDATE][Content of the new memory item]

... other operations ...
"""

        # Call LLM to determine operation type and content
        operation_response = self.llm_client.simple_chat(analysis_prompt)

        # logger.info(f"Prompt: {analysis_prompt}")
        # logger.info(f"Response: {operation_response}")

        return operation_response

    def _parse_operation_response(self, response: str) -> list[dict[str, Any]]:
        """Parse LLM response to extract operation info"""
        lines = response.strip().split("\n")

        operation_list = []
        current_operation = None

        for line in lines:
            line = line.strip()

            if line.startswith("**OPERATION:**"):
                operation = line.replace("**OPERATION:**", "").strip()
                if operation in ["ADD", "UPDATE", "DELETE", "TOUCH"]:
                    if current_operation:
                        operation_list.append(current_operation)
                    current_operation = {"operation": operation, "target_id": None, "memory_content": None}

            if line.startswith("- Target Memory ID:"):
                target_id = line.replace("- Target Memory ID:", "").strip()
                current_operation["target_id"] = target_id

            if line.startswith("- Memory Item Content:"):
                memory_content = line.replace("- Memory Item Content:", "").strip()
                current_operation["memory_content"] = memory_content

        if current_operation:
            operation_list.append(current_operation)

        return operation_list

    async def select_top_k_related(self, existing_memories: list[Memory], suggestion: str, k: int) -> list[Memory]:
        """
        Select top k memories most related to the suggestion based on cosine similarity.
        For each memory, calculate its maximum cosine similarity with any reference vector from suggestion.
        """
        # Generate embeddings for each line in suggestion as reference vectors
        reference_vectors = []
        for line in suggestion.split("\n"):
            if line.strip():
                embedding = await self.memory_repo.generate_embedding(line)
                if embedding:
                    reference_vectors.append(embedding)

        # If no valid reference vectors, fall back to time-based sorting
        if not reference_vectors:
            return sorted(existing_memories, key=lambda x: (x.happened_at or datetime(1980, 1, 1)), reverse=True)[:k]

        # Filter memories that have embeddings
        memories_with_embeddings = [
            mem for mem in existing_memories if mem.embedding is not None and len(mem.embedding) > 0
        ]

        if not memories_with_embeddings:
            return sorted(existing_memories, key=lambda x: (x.happened_at or datetime(1980, 1, 1)), reverse=True)[:k]

        # Convert to numpy arrays and normalize
        reference_vectors = np.array(reference_vectors)
        reference_vectors = reference_vectors / np.linalg.norm(reference_vectors, axis=1, keepdims=True)

        memory_embeddings = np.array([mem.embedding for mem in memories_with_embeddings])
        memory_embeddings = memory_embeddings / np.linalg.norm(memory_embeddings, axis=1, keepdims=True)

        # Calculate cosine similarities: (n_references, n_memories)
        similarities = np.dot(reference_vectors, memory_embeddings.T)

        # For each memory, get its maximum similarity with any reference vector
        max_similarities = np.max(similarities, axis=0)

        # Create list of (memory, max_similarity) pairs
        memory_similarity_pairs = list(zip(memories_with_embeddings, max_similarities, strict=False))

        # Sort by similarity (descending) and select top k
        memory_similarity_pairs.sort(key=lambda x: x[1], reverse=True)

        # Return top k memories
        selected_memories = [pair[0] for pair in memory_similarity_pairs[:k]]

        # If we don't have enough memories with embeddings, fill with remaining memories
        if len(selected_memories) < k:
            remaining_memories = [mem for mem in existing_memories if mem not in selected_memories]
            remaining_sorted = sorted(
                remaining_memories, key=lambda x: (x.happened_at or datetime(1980, 1, 1)), reverse=True
            )
            selected_memories.extend(remaining_sorted[: k - len(selected_memories)])

        return selected_memories
