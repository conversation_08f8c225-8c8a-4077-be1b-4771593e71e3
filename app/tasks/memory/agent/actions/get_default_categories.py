"""
Get Default Categories Action

Gets default memory categories from database by project, which can be used as templates.
"""

from typing import Any

from app.modules.organization.project.memory_category.crud import ProjectMemCategoryRepo

# Database imports
from app.services.db.session import SessionFactory

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class GetDefaultCategoriesAction(BaseAction):
    """Action to get default memory categories from database by project"""

    @property
    def action_name(self) -> str:
        return "get_default_categories"

    def get_schema(self) -> dict[str, Any]:
        """Return OpenAI-compatible function schema"""
        return {
            "name": "get_default_categories",
            "description": "Get default memory categories from database by project (template categories for reuse)",
            "parameters": {
                "type": "object",
                "properties": {
                    "project_id": {"type": "string", "description": "Project ID to get default categories for"},
                    "include_inactive": {
                        "type": "boolean",
                        "description": "Whether to include inactive categories",
                        "default": False,
                    },
                    "include_config": {
                        "type": "boolean",
                        "description": "Whether to include config-based categories for backwards compatibility",
                        "default": True,
                    },
                },
                "required": ["project_id"],
            },
        }

    async def execute(
        self, project_id: str, include_inactive: bool = False, include_config: bool = True
    ) -> dict[str, Any]:
        """
        Execute get default categories operation from database

        Args:
            project_id: Project ID to get default categories for
            include_inactive: Whether to include inactive categories
            include_config: Whether to include config-based categories

        Returns:
            Dict containing default category information from database
        """
        try:
            async with SessionFactory.make_local_session() as session:
                # Get default categories from database
                db_default = await ProjectMemCategoryRepo.get_active_categories(
                    project_id=project_id, include_inactive=include_inactive, session=session
                )

                default_categories = {}
                for cat in db_default:
                    # Skip activity category as it's handled separately by add_activity_memory
                    if cat.category_name == "activity":
                        continue

                    default_categories[cat.category_name] = {
                        "id": cat.id,
                        "category_name": cat.category_name,
                        "category_type": cat.category_type,
                        "description": cat.description or "No description available",
                        "is_active": cat.is_active,
                        "created_at": cat.created_at.isoformat() if cat.created_at else None,
                        "updated_at": cat.updated_at.isoformat() if cat.updated_at else None,
                    }

                # Include built-in categories from config for backwards compatibility
                config_categories = {}
                if include_config:
                    for category, filename in self.memory_types.items():
                        if category == "activity":
                            continue

                        # Only add if not already in database categories
                        if category not in default_categories:
                            description = self.config_manager.get_file_description(category)
                            config_categories[category] = {
                                "filename": filename,
                                "description": description,
                                "config_source": self.config_manager.get_folder_path(category),
                                "source": "config",
                            }

                return self._add_metadata({
                    "success": True,
                    "project_id": project_id,
                    "category_type": "default",
                    "default_categories": default_categories,
                    "config_categories": config_categories,
                    "total_default": len(default_categories),
                    "total_config": len(config_categories),
                    "include_inactive": include_inactive,
                    "include_config": include_config,
                    "embeddings_enabled": self.embeddings_enabled,
                    "excluded_categories": ["activity"],
                    "message": f"Found {len(default_categories)} default categories and {len(config_categories)} config categories for project {project_id}",
                })

        except Exception as e:
            return self._handle_error(e)
