"""
Link Related Memories Action

Automatically finds and links related memories using database embedding search.
"""

from typing import Any

from app.modules.memory.models import Memory

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class LinkRelatedMemoriesAction(BaseAction):
    """
    Action to find and link related memories using database embedding search

    This action takes a memory item and finds the most related existing memories,
    then creates links between them in the database.
    """

    def __init__(self, memory_core):
        super().__init__(memory_core)
        self.description = "Find and link related memories using database embedding search"

        self.filter_with_llm = False

    @property
    def action_name(self) -> str:
        """Return the name of this action"""
        return "link_related_memories"

    def get_schema(self) -> dict[str, Any]:
        """Get OpenAI function schema for linking related memories"""
        return {
            "name": "link_related_memories",
            "description": "Find related memories using database embedding search and create links between them",
            "parameters": {
                "type": "object",
                "properties": {
                    "memory_id": {
                        "type": "string",
                        "description": "ID of the memory item to find related memories for (optional if link_all_items is true)",
                    },
                    "category": {
                        "type": "string",
                        "description": "Category containing the target memory item (optional if link_all_items is true)",
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "Number of top related memories to find",
                        "default": 5,
                    },
                    "min_similarity": {
                        "type": "number",
                        "description": "Minimum similarity threshold (0.0-1.0)",
                        "default": 0.3,
                    },
                    "search_categories": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Categories to search in (default: all categories)",
                    },
                    "link_all_items": {
                        "type": "boolean",
                        "description": "Whether to link all items in category instead of just one item",
                        "default": False,
                    },
                },
                "required": ["memory_id", "category"],
            },
        }

    # Wu: Now we are not giving enough information (or instructions) to the LLM for whether use "link_all_items"
    #     Its behavior is uncertain and inconsistent.

    # Wu: Maybe consider move the entire link function to post-processing, reasons are as follows:
    #     1. There is nothing that really needs a LLM to decide, we know all new items or updated items already, just run link on them
    #     2. The link requires embedding, which is now done when updating database (i.e., not available when calling this action)
    #     3. The link is not really in use now

    async def execute(
        self,
        *,
        memory_id: str | None = None,
        category: str | None = None,
        top_k: int = 5,
        min_similarity: float = 0.3,
        search_categories: list[str] | None = None,
        link_all_items: bool = False,
    ) -> dict[str, Any]:
        """
        Execute link related memories operation using database

        Args:
            memory_id: ID of the memory item to find related memories for
            category: Category containing the target memory item
            top_k: Number of top related memories to find
            min_similarity: Minimum similarity threshold
            search_categories: Categories to search in
            link_all_items: Whether to link all items in category

        Returns:
            Dict containing related memories and generated links
        """
        try:
            available_categories = await self.memory_repo.get_memory_categories("basic")
            available_categories = [category.name for category in available_categories]

            if not available_categories:
                return self._add_metadata({"success": False, "error": "No available categories found"})

            if not search_categories:
                search_categories = available_categories

            memories_to_link = []

            if link_all_items:
                candidate_categories = [category] if category is not None else available_categories

                for _category in candidate_categories:
                    memories_to_link.extend(await self.memory_repo.get_memory_items_in_category(_category))

                if not memories_to_link:
                    return self._add_metadata({
                        "success": False,
                        "error": f"No memories found in category '{candidate_categories}'",
                    })
            else:
                target_memory = await self.memory_repo.get_memory_item_by_id(category, memory_id)

                if not target_memory:
                    return self._add_metadata({
                        "success": False,
                        "error": f"Memory ID '{memory_id}' not found",
                    })

                memories_to_link = [target_memory]

            return await self._solve_all_links(memories_to_link, top_k, min_similarity, search_categories)

        except Exception as e:
            return self._handle_error(e)

    async def _solve_all_links(
        self,
        memories_to_link: list[Memory],
        top_k: int,
        min_similarity: float,
        search_categories: list[str] | None,
    ) -> dict[str, Any]:
        """Solve all links for a list of memories"""
        try:
            total_linked = 0
            all_links_added = {}

            for memory_item in memories_to_link:
                if memory_item.embedding is None or len(memory_item.embedding) == 0:
                    logger.warning(f"Memory item {memory_item.memory_id} has no embedding")
                    continue

                related_memories = await self.memory_repo.similarity_search(
                    reference_memory=memory_item,
                    categories=search_categories,
                    limit=top_k * 2,
                    similarity_threshold=min_similarity,
                )

                if not related_memories:
                    continue

                if self.filter_with_llm:
                    related_memories = await self._filter_relevant_memories_with_llm(
                        related_memories, memory_item.content, top_k
                    )
                else:
                    related_memories = related_memories[:top_k]

                link_memory_ids = [memory_item.memory_id for memory_item, _ in related_memories]

                memory_item_to_update = {
                    "memory_id": memory_item.memory_id,
                    "links": link_memory_ids,
                }
                await self.memory_repo.update_memory_item(memory_item.category, memory_item_to_update, memory_item)

                total_linked += 1
                all_links_added[memory_item.memory_id] = link_memory_ids

            return self._add_metadata({
                "success": True,
                "total_linked": total_linked,
                "all_links_added": all_links_added,
                "message": f"Linked {total_linked} out of {len(memories_to_link)} memory items",
            })

        except Exception as e:
            return self._handle_error(e)

    async def _filter_relevant_memories_with_llm(
        self,
        candidate_memories: list[tuple[Memory, float]],
        target_content: str,
        max_links: int,
    ) -> list:
        """
        Use LLM to filter candidate memories and keep only truly relevant ones

        Args:
            candidate_memories: List of candidate memory objects from database
            target_content: The target memory content to compare against
            max_links: Maximum number of links to return

        Returns:
            List of filtered relevant memories
        """
        try:
            if not candidate_memories:
                return []

            # Prepare candidate memories for LLM evaluation
            candidates_text = ""
            for i, (mem, similarity) in enumerate(candidate_memories, 1):
                content_preview = mem.content[:200] + "..." if len(mem.content) > 200 else mem.content
                candidates_text += f"{i}. [ID: {mem.memory_id}] [{mem.category or 'Unknown'}] (similarity: {similarity:.3f}) {content_preview}\n"

            # Create LLM prompt for relevance filtering
            relevance_prompt = f"""You are evaluating whether candidate memories are truly related to a target memory.

TARGET MEMORY:
{target_content}

CANDIDATE MEMORIES:
{candidates_text}

**TASK**: Determine which candidate memories are genuinely related to the target memory.

**CRITERIA FOR RELEVANCE**:
- Memories should share meaningful connections (people, places, events, topics, themes)
- Avoid superficial similarities (just sharing common words like "the", "and", "is")
- Consider contextual relationships (cause-effect, temporal sequences, thematic connections)
- Focus on memories that would provide useful context or background for understanding the target memory

**EVALUATION GUIDELINES**:
- ✅ RELEVANT: Memories about the same people, events, locations, or directly related topics
- ✅ RELEVANT: Memories that provide context, background, or related information
- ❌ NOT RELEVANT: Memories that only share common words but different contexts
- ❌ NOT RELEVANT: Memories about completely different topics/people/events

**OUTPUT FORMAT**:
Return ONLY the numbers (1, 2, 3, etc.) of the truly relevant memories, separated by commas. If no memories are relevant, return "NONE".

Examples:
- If memories 1, 3, and 5 are relevant: "1, 3, 5"
- If no memories are relevant: "NONE"
- If only memory 2 is relevant: "2"

RELEVANT MEMORY NUMBERS:"""

            # Call LLM to evaluate relevance
            llm_response = self.llm_client.simple_chat(relevance_prompt)

            # Parse LLM response
            relevant_indices = self._parse_relevance_response(llm_response.strip())

            # Filter memories based on LLM evaluation
            relevant_memories = []
            for idx in relevant_indices:
                if 1 <= idx <= len(candidate_memories):
                    relevant_memories.append(candidate_memories[idx - 1])  # Convert to 0-based index

            # Limit to max_links
            return relevant_memories[:max_links]

        except Exception as e:
            logger.exception(f"Error filtering memories with LLM: {e}")
            # Fallback to original top candidates if LLM filtering fails
            return candidate_memories[:max_links]

    def _parse_relevance_response(self, response: str) -> list[int]:
        """
        Parse LLM response to extract relevant memory indices

        Args:
            response: LLM response containing memory numbers

        Returns:
            List of memory indices (1-based)
        """
        import re

        try:
            response = response.strip().upper()

            if response == "NONE" or not response:
                return []

            # Extract numbers from response
            numbers = re.findall(r"\b(\d+)\b", response)
            return [int(num) for num in numbers if num.isdigit()]

        except Exception as e:
            logger.warning(f"Failed to parse relevance response '{response}': {e}")
            return []
