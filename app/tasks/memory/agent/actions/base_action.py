"""
Base Action Class for Memory Operations

Defines the interface and common functionality for all memory actions.
"""

import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any

from ...utils import get_logger

logger = get_logger(__name__)


@dataclass
class ActionContext:
    """Context object containing database parameters for actions"""

    user_id: str
    agent_id: str | None = None
    conversation_id: str | None = None
    user_name: str = "user"
    agent_name: str = "assistant"
    conversation: list[dict[str, str]] | None = None
    conversation_text: str = ""
    session_date: str | None = None
    api_key_id: str | None = None
    project_id: str | None = None


class BaseAction(ABC):
    """
    Base class for all memory actions

    Defines the standard interface that all actions must implement:
    - get_schema(): Return OpenAI-compatible function schema
    - execute(**kwargs): Execute the action with given arguments
    - validate_arguments(): Validate input arguments
    """

    def __init__(self, memory_core):
        """
        Initialize action with memory core

        Args:
            memory_core: Core memory functionality (LLM client, embeddings, etc.)
        """
        self.memory_core = memory_core
        self.llm_client = memory_core.llm_client
        self.embedding_client = memory_core.embedding_client
        self.action_context = memory_core.action_context
        self.memory_repo = memory_core.memory_repo
        # Config is now database-driven, no static config needed

    @property
    @abstractmethod
    def action_name(self) -> str:
        """Return the name of this action"""
        pass

    @abstractmethod
    def get_schema(self) -> dict[str, Any]:
        """
        Return OpenAI-compatible function schema for this action

        Returns:
            Dict containing function schema with name, description, and parameters
        """
        pass

    @abstractmethod
    def execute(self, **kwargs) -> dict[str, Any]:
        """
        Execute the action with provided arguments

        Args:
            dependencies: ActionDependencies containing context and repository
            **kwargs: Action-specific arguments

        Returns:
            Dict containing execution result with success status and data
        """
        pass

    def validate_arguments(self, arguments: dict[str, Any]) -> dict[str, Any]:
        """
        Validate input arguments against schema

        Args:
            arguments: Arguments to validate

        Returns:
            Dict with validation result
        """
        try:
            schema = self.get_schema()
            required_params = schema["parameters"].get("required", [])

            # Check for missing required parameters
            missing_params = [param for param in required_params if param not in arguments]

            if missing_params:
                return {
                    "valid": False,
                    "error": f"Missing required parameters: {missing_params}",
                    "required_parameters": required_params,
                }

            return {"valid": True, "message": f"Validation passed for {self.action_name}"}

        except Exception as e:
            return {"valid": False, "error": f"Validation error: {e!s}"}

    def _add_metadata(self, result: dict[str, Any]) -> dict[str, Any]:
        """Add standard metadata to action result"""
        if isinstance(result, dict):
            result["action_name"] = self.action_name
            result["timestamp"] = datetime.now().isoformat()
        return result

    def _handle_error(self, error: Exception) -> dict[str, Any]:
        """Standard error handling for actions"""
        error_result = {
            "success": False,
            "error": str(error),
            "action_name": self.action_name,
            "timestamp": datetime.now().isoformat(),
        }
        logger.error(f"Action {self.action_name} failed: {error}")
        return error_result

    # ================================
    # Memory ID Utilities
    # ================================

    def _generate_memory_id(self) -> str:
        short_uuid = str(uuid.uuid4()).replace("-", "")[:8]
        return f"{short_uuid}"
