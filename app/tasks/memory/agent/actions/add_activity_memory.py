"""
Add Activity Memory Action

Adds new activity memory content with strict no-pronouns formatting, following the same
high-quality standards as update_memory_with_suggestions for self-contained memory items.
"""

from datetime import datetime
from typing import Any

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class AddActivityMemoryAction(BaseAction):
    """
    Action to add new activity memory content with strict formatting requirements

    Ensures all memory items are complete, self-contained sentences with no pronouns,
    following the same standards as update_memory_with_suggestions.
    """

    @property
    def action_name(self) -> str:
        return "add_activity_memory"

    def get_schema(self) -> dict[str, Any]:
        """Return OpenAI-compatible function schema"""
        return {
            "name": "add_activity_memory",
            "description": "Add new activity memory content with strict no-pronouns formatting for complete, self-contained memory items",
            "parameters": {
                "type": "object",
                "properties": {
                    # "content": {
                    #     "type": "string",
                    #     "description": "Complete original conversation text exactly as provided - do NOT modify, extract, or summarize",
                    # },
                    # "session_date": {
                    #     "type": "string",
                    #     "description": "Session date for the memory items (format: YYYY-MM-DD)",
                    # },
                },
                "required": [],
            },
        }

    async def execute(
        self,
        *,
        content: str | None = None,
        session_date: str | None = None,
    ) -> dict[str, Any]:
        """
        Execute add activity memory operation with strict formatting

        Args:
            content: Raw content to process and format
            session_date: Date of the session

        Returns:
            Dict containing operation result including formatted content and embedding info
        """
        try:
            if content is None:
                content = self.action_context.conversation_text
                # return self._add_metadata({"success": False, "error": "Content is required"})

            # Use current date if not provided
            if not session_date:
                session_date = self.action_context.session_date or datetime.now().strftime("%Y-%m-%d")

            # Process raw content through LLM to ensure strict formatting
            formatted_content = await self._format_content_with_llm(content, session_date)

            if not formatted_content.strip():
                return self._add_metadata({"success": False, "error": "LLM returned empty formatted content"})

            new_activity_items = [
                {
                    "memory_id": self._generate_memory_id(),
                    "content": line,
                    "mentioned_at": session_date,
                }
                for line in formatted_content.split("\n")
                if line.strip()
            ]
            await self.memory_repo.store_memory_items("activity", new_activity_items)

            return self._add_metadata({
                "success": True,
                "category": "activity",
                # "original_content": content,
                "formatted_content": formatted_content,
                "memory_items_added": len(new_activity_items),
                "new_memory_items": new_activity_items,
                # "session_date": session_date,
                # "embeddings_generated": self.embeddings_enabled,
                # "embeddings_info": embeddings_info,
                "message": f"Successfully added {len(new_activity_items)} activity memory items with strict formatting",
            })

        except Exception as e:
            return self._handle_error(e)

    async def _format_content_with_llm(self, content: str, session_date: str) -> str:
        """Use LLM to format content with meaningful activity grouping"""

        format_prompt = f"""You are formatting activity memory content for the user on {session_date}.

Raw content to format:
{content}

**CRITICAL REQUIREMENT: GROUP RELATED CONTENT INTO MEANINGFUL ACTIVITIES**

Transform this raw content into properly formatted activity memory items following these rules:

**MEANINGFUL ACTIVITY GROUPING REQUIREMENTS:**
- Group related sentences/statements into single, comprehensive activity descriptions
- Each activity should be a complete, self-contained description of what happened
- Combine related dialogue, actions, and context into cohesive activity blocks
- Only create separate items for genuinely different activities or topics
- Each activity item should tell a complete "story" or "event"

**SELF-CONTAINED MEMORY REQUIREMENTS:**
- EVERY activity item must be complete and standalone
- ALWAYS include the full subject (do not use "she/he/they/it")
- NEVER use pronouns that depend on context (no "she", "he", "they", "it")
- Include specific names, places, dates, and full context in each item
- Each activity should be understandable without reading other items
- Include all relevant details, emotions, and outcomes in the activity description

**FORMAT REQUIREMENTS:**
1. Each line = one complete, meaningful activity (may include multiple related sentences)
2. NO markdown headers, bullets, numbers, or structure
3. NO memory ID information (will be added automatically)
4. Write in plain text only
5. Focus on comprehensive, meaningful activity descriptions
6. Use specific names, titles, places, and dates
7. Each line ends with a period

**GOOD EXAMPLES (meaningful activities, one per line):**
User attended a LGBTQ support group where User heard inspiring transgender stories and felt happy, thankful, accepted, and gained courage to embrace User's true self.
User discussed future career plans with Melanie, expressing keen interest in counseling and mental health work to support people with similar issues, and Melanie encouraged User saying User would be a great counselor due to User's empathy and understanding.
User admired Melanie's lake sunrise painting from last year, complimented the color blending, and discussed how painting serves as a great outlet for expressing feelings and relaxing after long days.

**BAD EXAMPLES (too fragmented):**
User went to a LGBTQ support group.
User heard transgender stories.
User felt happy and thankful.
User gained courage to embrace User's true self.

**ACTIVITY GROUPING GUIDELINES:**
- Conversations about the same topic → Single activity
- Related actions and their outcomes → Single activity
- Emotional reactions to specific events → Include in the main activity
- Sequential related events → Single comprehensive activity
- Different topics or unrelated events → Separate activities

**QUALITY STANDARDS:**
- Never use "he", "she", "they", "it" - always use the person's actual name
- Never use "the book", "the place", "the friend" - always include full titles and names
- Each activity must be complete and tell the full story
- Include emotional context, outcomes, and significance
- Merge related content intelligently to create meaningful activity summaries

Transform the raw content into properly formatted activity memory items (ONE MEANINGFUL ACTIVITY PER LINE):

"""

        # Call LLM to format content
        cleaned_content = self.llm_client.simple_chat(format_prompt)

        return cleaned_content
