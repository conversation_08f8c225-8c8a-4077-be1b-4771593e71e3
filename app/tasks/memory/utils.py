"""
Utility functions for memory tasks
"""

import logging
import time


def get_logger(name: str | None = None) -> logging.Logger:
    """
    Get a logger instance for memory tasks

    Args:
        name: Logger name (optional)

    Returns:
        Logger instance
    """
    logger_name = name or "memory_tasks"
    logger = logging.getLogger(logger_name)

    # Check if we're running in a Celery worker to avoid duplicate logs
    def _is_celery_worker():
        try:
            from celery import current_task

            # If current_task exists and we can access it, we're in a worker
            return current_task is not None
        except (ImportError, RuntimeError):
            # Check environment variables as fallback
            import os

            return any(var in os.environ for var in ["CELERY_WORKER", "CELERY_TASK_ID"])

    is_celery_worker = _is_celery_worker()

    # Set default level if not already configured
    if not logger.handlers:
        handler = logging.StreamHandler()
        # formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        formatter = logging.Formatter("[%(asctime)s: %(levelname)s/%(name)s:%(lineno)d] %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        logger.setLevel(logging.INFO)

    # In Celery worker, prevent propagation to avoid duplicates
    if is_celery_worker:
        logger.propagate = False

    return logger


class Timer:
    _last = None
    history = []

    @classmethod
    def tick(cls, label: str):
        time_now = time.time()
        if cls._last is not None:
            cls.history.append((cls._last[0], time_now - cls._last[1]))
        cls._last = (label, time_now)

    @classmethod
    def clear(cls):
        cls._last = None
        cls.history = []
