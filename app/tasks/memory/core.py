"""
Memory Core

Core memory functionality shared across all actions
"""

import threading

from .agent.actions.base_action import ActionContext
from .llm import BaseEmbedding<PERSON>lient, BaseLLMClient
from .repository import MemoryRepository
from .utils import get_logger

logger = get_logger(__name__)


class MemoryCore:
    """
    Core memory functionality shared across all actions

    Provides the shared resources and utilities that actions need:
    - LLM client
    - Stop flag for operation control

    All configuration is now database-driven.
    """

    def __init__(
        self,
        llm_client: BaseLLMClient,
        embedding_client: BaseEmbeddingClient,
        action_context: ActionContext,
    ):
        self.llm_client = llm_client
        self._stop_flag = threading.Event()

        self.embedding_client = embedding_client

        self.action_context = action_context

        self.memory_repo = MemoryRepository(self.action_context, self.embedding_client)

        # logger.info(f"Memory Core initialized: embeddings: {self.embeddings_enabled}, database-driven configuration")
