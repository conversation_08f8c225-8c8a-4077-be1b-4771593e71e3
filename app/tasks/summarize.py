import asyncio

from app.services.task_queue import celery_app
from app.tasks.memory.agent.summary_agent import SummarizationDependency, SummaryAgent
from app.tasks.memory.llm.llm_factory import get_llm_client
from app.tasks.memory.utils import get_logger

logger = get_logger(__name__)


@celery_app.task(name="summarize", bind=True)
def summarize_memory(
    self,
    user_id: str,
    agent_id: str,
    project_id: str,
    memory_categories: list[str],
):
    """
    Summarize memory
    """

    logger.info(f"Starting summarization task for user {user_id} and agent {agent_id}")

    dependency = SummarizationDependency(
        user_id=user_id,
        agent_id=agent_id,
        project_id=project_id,
    )

    llm_client = get_llm_client()
    summary_agent = SummaryAgent(llm_client=llm_client, dependency=dependency)

    agent_result = asyncio.run(summary_agent.summarize_memory(memory_categories))

    return agent_result
