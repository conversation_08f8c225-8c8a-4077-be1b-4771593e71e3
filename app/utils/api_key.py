"""API key encryption and decryption utilities."""

import base64
import hashlib
import secrets

from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
from loguru import logger

from app.exceptions import UnauthorizedException


def _derive_chacha_key(secret: str) -> bytes:
    """Derive 32-byte ChaCha20 key from app secret."""
    return hashlib.sha256(secret.encode()).digest()


def encode_payload(project_id: str, expires_at: str | None = None) -> bytes:
    """Encode project_id and expires_at into a bytes payload."""
    expires_at_str = expires_at or "0000-00-00T00:00:00Z"
    payload_str = f"{project_id}|{expires_at_str}"
    return payload_str.encode()


def decode_payload(payload: bytes) -> dict:
    """Decode bytes payload into project_id and expires_at."""
    payload_str = payload.decode()
    parts = payload_str.split("|")
    if len(parts) != 2:
        logger.error(f"Invalid decrypted payload format: {payload_str}")
        raise UnauthorizedException()

    project_id = parts[0]
    expires_at_str = parts[1]
    expires_at = expires_at_str if expires_at_str != "0000-00-00T00:00:00Z" else None

    return {
        "project_id": project_id,
        "expires_at": expires_at,
    }


def encrypt_api_key_data(
    app_id: str,
    project_id: str,
    created_at: str,
    expires_at: str | None = None,
    secret: str = "",
) -> str:
    """Encrypt API key data into a compact token using ChaCha20-Poly1305.

    Args:
        app_id: Application ID (not stored in payload)
        project_id: Project ID
        created_at: Creation timestamp (not stored in payload)
        expires_at: Optional expiration timestamp
        secret: Secret key for encryption

    Returns:
        Compact encrypted token (~70 chars)
    """
    payload = encode_payload(project_id, expires_at)

    # Encrypt with ChaCha20-Poly1305
    key = _derive_chacha_key(secret)
    cipher = ChaCha20Poly1305(key)

    # Generate random nonce (12 bytes for ChaCha20-Poly1305)
    nonce = secrets.token_bytes(12)
    encrypted_data = cipher.encrypt(nonce, payload, None)

    # Combine nonce + encrypted data
    final_data = nonce + encrypted_data

    # Base64 encode
    encoded = base64.urlsafe_b64encode(final_data).decode().rstrip("=")

    return f"mu_{encoded}"


def decrypt_api_key_data(encrypted_token: str, secret: str) -> dict:
    """Decrypt compact API key token using ChaCha20-Poly1305.

    Args:
        encrypted_token: The encrypted token
        secret: Secret key for decryption

    Returns:
        Dictionary containing project_id, expires_at (no app_id or created_at)

    Raises:
        Exception: If decryption fails or token is invalid
    """
    try:
        # Remove prefix
        if encrypted_token.startswith("mu_"):
            encrypted_token = encrypted_token[3:]

        # Add padding for base64 decoding
        padding = 4 - len(encrypted_token) % 4
        if padding != 4:
            encrypted_token += "=" * padding

        final_data = base64.urlsafe_b64decode(encrypted_token)
        nonce = final_data[:12]
        encrypted_data = final_data[12:]

        # Decrypt with ChaCha20-Poly1305
        key = _derive_chacha_key(secret)
        cipher = ChaCha20Poly1305(key)
        decrypted_payload = cipher.decrypt(nonce, encrypted_data, None)
    except Exception as e:
        logger.error(f"Error while decrypting API key: {e.with_traceback()}")
        raise UnauthorizedException() from e
    else:
        return decode_payload(decrypted_payload)
