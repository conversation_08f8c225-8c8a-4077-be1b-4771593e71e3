import secrets
import string

import pendulum


def generate_uniq_slug(base_slug: str, existing_slugs: set[str]) -> str:
    """
    Generate a unique slug by progressively adding time-based suffixes and random strings.

    Args:
        base_slug: The base slug to make unique
        existing_slugs: A set of existing slugs to check against
    Returns:
        A unique slug string
    """
    # First try the base slug
    if base_slug not in existing_slugs:
        return base_slug

    now = pendulum.now()

    time_suffixes = [
        now.format("YYYY"),
        now.format("YYMM"),
        now.format("YYMMDD"),
        now.format("YYMMDDHH"),
        now.format("YYMMDDHHMM"),
        now.format("YYMMDDHHMMSS"),
    ]

    for suffix in time_suffixes:
        candidate_slug = f"{base_slug}-{suffix}"
        if candidate_slug not in existing_slugs:
            return candidate_slug

    base_with_time = f"{base_slug}-{time_suffixes[-1]}"

    while True:
        random_string = "".join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
        unique_slug = f"{base_with_time}-{random_string}"

        if unique_slug not in existing_slugs:
            return unique_slug


def sanitize_slug(text: str) -> str:
    """
    Sanitize text to create a valid slug.

    Args:
        text: The text to sanitize

    Returns:
        A sanitized slug string (lowercase, alphanumeric + hyphens only)
    """
    # Convert to lowercase
    slug = text.lower()

    # Replace any non-alphanumeric character with a hyphen
    slug = "".join(c if c.isalnum() else "-" for c in slug)

    # Remove multiple consecutive hyphens
    while "--" in slug:
        slug = slug.replace("--", "-")

    # Remove leading/trailing hyphens
    slug = slug.strip("-")

    return slug


def create_base_slug_from_email(email: str) -> str:
    """
    Create a base slug from an email address.

    Args:
        email: The email address

    Returns:
        A base slug derived from the email's username part
    """
    username = email.split("@")[0]
    return sanitize_slug(username)
