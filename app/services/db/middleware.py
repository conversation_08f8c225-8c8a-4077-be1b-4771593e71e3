from sqlalchemy.engine import URL
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.types import ASGIApp

from .session import DBSession, init_session_factory


class SQLAlchemyMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        db_url: str | URL,
        engine_args: dict | None = None,
        session_args: dict | None = None,
        commit_on_exit: bool = False,
    ):
        super().__init__(app)
        init_session_factory(db_url, engine_args=engine_args, session_args=session_args)
        self.commit_on_exit = commit_on_exit

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        async with DBSession(commit_on_exit=self.commit_on_exit):
            return await call_next(request)
