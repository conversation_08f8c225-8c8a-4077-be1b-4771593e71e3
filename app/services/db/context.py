# session_context.py
from contextlib import asynccontextmanager
from contextvars import <PERSON>text<PERSON><PERSON>

from loguru import logger

from .session import CommitBlockedSession, SessionFactory

_session_ctx: ContextVar[CommitBlockedSession] = ContextVar("session")


def set_current_session(session: CommitBlockedSession):
    _session_ctx.set(session)


def has_current_session() -> bool:
    try:
        _session_ctx.get()
        return True
    except LookupError:
        return False


def get_current_session() -> CommitBlockedSession:
    try:
        return _session_ctx.get()
    except LookupError:
        raise RuntimeError("No session found in context. Did you forget Depends(get_db)?")


@asynccontextmanager
async def get_or_create_session():
    """
    If a session is already set in the context, yield it.
    Otherwise, create a new session and yield it.
    After yielding, commit the session if no exceptions occurred,
    or rollback if an exception occurred.
    """
    if has_current_session():
        yield get_current_session()
    else:
        async with SessionFactory.make_req_ctx_session() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                logger.error(f"Error during session operation: {e}")
                await session.rollback()
                raise
            else:
                await session.force_commit()
