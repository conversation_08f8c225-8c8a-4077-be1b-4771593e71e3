import hashlib
import hmac
import time
from typing import Any

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from loguru import logger

from config.loader import SettingsFactory


class WebhookSignatureError(Exception):
    """Exception raised when webhook signature verification fails."""
    pass


class WebhookVerificationService:
    """Service for verifying Stripe webhook signatures."""
    
    def __init__(self):
        self.settings = SettingsFactory.settings()
        self.endpoint_secret = self.settings.STRIPE_WEBHOOK_ENDPOINT_SECRET
        
    def verify_signature(
        self, 
        payload: bytes, 
        signature_header: str,
        tolerance: int = 300  # 5 minutes tolerance for timestamp
    ) -> dict[str, Any]:
        """
        Verify the Stripe webhook signature.
        
        Args:
            payload: Raw request body as bytes
            signature_header: The Stripe-Signature header value
            tolerance: Maximum age of the webhook in seconds (default: 300)
            
        Returns:
            Parsed webhook event data
            
        Raises:
            WebhookSignatureError: If signature verification fails
            HTTPException: If webhook endpoint secret is not configured
        """
        if not self.endpoint_secret:
            logger.error("Stripe webhook endpoint secret is not configured")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Webhook endpoint secret not configured"
            )
            
        try:
            # Parse the signature header
            elements = signature_header.split(',')
            signature_data = {}
            
            for element in elements:
                key, value = element.split('=', 1)
                if key == 't':
                    signature_data['timestamp'] = int(value)
                elif key.startswith('v'):
                    signature_data[key] = value
                    
            if 'timestamp' not in signature_data:
                raise WebhookSignatureError("No timestamp found in signature header")
                
            if 'v1' not in signature_data:
                raise WebhookSignatureError("No v1 signature found in signature header")
                
            # Check timestamp tolerance
            current_time = int(time.time())
            if abs(current_time - signature_data['timestamp']) > tolerance:
                raise WebhookSignatureError(
                    f"Webhook timestamp too old. Current: {current_time}, "
                    f"Webhook: {signature_data['timestamp']}, "
                    f"Tolerance: {tolerance}s"
                )
                
            # Construct the signed payload
            signed_payload = f"{signature_data['timestamp']}.{payload.decode('utf-8')}"
            
            # Compute the expected signature
            expected_signature = hmac.new(
                self.endpoint_secret.encode('utf-8'),
                signed_payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures using constant-time comparison
            if not hmac.compare_digest(expected_signature, signature_data['v1']):
                raise WebhookSignatureError("Signature verification failed")
                
            logger.info(f"Webhook signature verified successfully for timestamp {signature_data['timestamp']}")
            
            # Parse and return the payload
            import json
            return json.loads(payload.decode('utf-8'))
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse webhook payload as JSON: {e}")
            raise WebhookSignatureError("Invalid JSON payload") from e
        except ValueError as e:
            logger.error(f"Error parsing signature header: {e}")
            raise WebhookSignatureError("Invalid signature header format") from e
        except Exception as e:
            logger.error(f"Unexpected error during signature verification: {e}")
            raise WebhookSignatureError(f"Signature verification failed: {str(e)}") from e


# Singleton instance
webhook_verification_service = WebhookVerificationService()
