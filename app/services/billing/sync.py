import asyncio
import logging
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.stripe_product.crud import price_crud, product_crud
from app.services.db.session import DBSession
from config.loader import SettingsFactory

from .client import Stripe<PERSON><PERSON>rror, stripe_service

logger = logging.getLogger(__name__)


class StripeSyncService:
    def __init__(self):
        self.settings = SettingsFactory.settings()
        self._last_sync: dict[str, datetime] = {}
        self._sync_interval = timedelta(minutes=self.settings.STRIPE_SYNC_INTERVAL_MINUTES)
        self._sync_lock = asyncio.Lock()

    async def should_sync(self, sync_type: str) -> bool:
        """Check if enough time has passed since last sync."""
        last_sync = self._last_sync.get(sync_type)
        if not last_sync:
            return True
        return datetime.utcnow() - last_sync > self._sync_interval

    async def sync_products_and_prices(self, force: bool = False) -> dict[str, int]:
        """Sync products and prices from Stripe to database."""
        async with self._sync_lock:
            if not force and not await self.should_sync("products_and_prices"):
                logger.info("Skipping sync - too soon since last sync")
                return {"products": 0, "prices": 0}

            logger.info("Starting Stripe products and prices sync")
            start_time = datetime.utcnow()

            try:
                async with DBSession(commit_on_exit=True) as db_context:
                    session = db_context.session

                    # Sync products
                    products_synced = await self._sync_products(session)

                    # Sync prices
                    prices_synced = await self._sync_prices(session)

                    self._last_sync["products_and_prices"] = datetime.utcnow()

                    duration = datetime.utcnow() - start_time
                    logger.info(
                        f"Stripe sync completed in {duration.total_seconds():.2f}s. "
                        f"Products: {products_synced}, Prices: {prices_synced}"
                    )

                    return {"products": products_synced, "prices": prices_synced}

            except StripeAPIError:
                logger.exception("Stripe API error during sync")
                raise
            except Exception:
                logger.exception("Unexpected error during Stripe sync")
                raise

    async def _sync_products(self, session: AsyncSession) -> int:
        """Sync products from Stripe."""
        try:
            stripe_products = await stripe_service.get_all_products()
            products_synced = 0

            for stripe_product in stripe_products:
                try:
                    await product_crud.upsert_from_stripe(stripe_product=stripe_product.model_dump(), session=session)
                    products_synced += 1
                except Exception:
                    logger.exception(f"Error syncing product {stripe_product.id}")
                    continue

            logger.info(f"Synced {products_synced} products")

        except Exception:
            logger.exception("Error fetching products from Stripe")
            raise
        else:
            return products_synced

    async def _sync_prices(self, session: AsyncSession) -> int:
        """Sync prices from Stripe."""
        try:
            stripe_prices = await stripe_service.get_all_prices()
            prices_synced = 0

            for stripe_price in stripe_prices:
                try:
                    # Find the corresponding product in our database
                    product = await product_crud.get_by_stripe_id(
                        stripe_product_id=stripe_price.product, session=session
                    )

                    if not product:
                        logger.warning(f"Product {stripe_price.product} not found for price {stripe_price.id}")
                        continue

                    await price_crud.upsert_from_stripe(
                        stripe_price=stripe_price.model_dump(), product_id=product.id, session=session
                    )
                    prices_synced += 1

                except Exception:
                    logger.exception(f"Error syncing price {stripe_price.id}")
                    continue

            logger.info(f"Synced {prices_synced} prices")

        except Exception:
            logger.exception("Error fetching prices from Stripe")
            raise
        else:
            return prices_synced

    async def sync_on_startup(self) -> None:
        """Perform initial sync on application startup."""
        logger.info("Performing initial Stripe sync on startup")
        try:
            await self.sync_products_and_prices(force=True)
        except Exception:
            logger.exception("Failed to sync Stripe data on startup")
            # Don't raise - we don't want to prevent app startup


# Singleton instance
stripe_sync_service = StripeSyncService()
