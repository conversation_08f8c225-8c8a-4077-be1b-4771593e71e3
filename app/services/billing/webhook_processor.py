from datetime import datetime
from typing import Any

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules import cruds
from app.modules.stripe_subscription.models import StripeSubscription
from app.modules.stripe_webhook.crud import stripe_webhook_event_crud
from app.modules.stripe_webhook.models import WebhookEventStatus
from app.modules.stripe_webhook.schema import StripeInvoicePaidEvent
from app.services.billing import stripe_service
from app.usecases.checkout_session import change_org_plan


class WebhookProcessingError(Exception):
    """Exception raised when webhook processing fails."""
    pass


class StripeWebhookProcessor:
    """Service for processing Stripe webhook events."""

    async def process_event(
        self, 
        event_data: dict[str, Any], 
        session: AsyncSession
    ) -> dict[str, Any]:
        """
        Process a Stripe webhook event.
        
        Args:
            event_data: The verified webhook event data from Stripe
            session: Database session
            
        Returns:
            Processing result dictionary
            
        Raises:
            WebhookProcessingError: If processing fails
        """
        event_id = event_data.get("id")
        event_type = event_data.get("type")
        
        logger.info(f"Processing webhook event {event_id} of type {event_type}")
        
        # Check if we've already processed this event (idempotency)
        existing_event = await stripe_webhook_event_crud.get_by_stripe_event_id(
            stripe_event_id=event_id, session=session
        )
        
        if existing_event:
            if existing_event.status == WebhookEventStatus.COMPLETED:
                logger.info(f"Event {event_id} already processed successfully")
                await stripe_webhook_event_crud.update_processing_status(
                    webhook_event=existing_event,
                    status=WebhookEventStatus.DUPLICATE,
                    session=session
                )
                return {"status": "duplicate", "message": "Event already processed"}
            elif existing_event.status == WebhookEventStatus.PROCESSING:
                logger.warning(f"Event {event_id} is currently being processed")
                return {"status": "processing", "message": "Event currently being processed"}
        else:
            # Create new webhook event record
            existing_event = await stripe_webhook_event_crud.create_from_stripe_event(
                stripe_event_id=event_id,
                event_type=event_type,
                stripe_created=datetime.fromtimestamp(event_data.get("created", 0)),
                livemode=event_data.get("livemode", False),
                event_data=event_data,
                session=session
            )
        
        # Mark as processing
        await stripe_webhook_event_crud.update_processing_status(
            webhook_event=existing_event,
            status=WebhookEventStatus.PROCESSING,
            session=session
        )
        
        try:
            # Process based on event type
            if event_type == "invoice.paid":
                result = await self._process_invoice_paid(event_data, existing_event, session)
            else:
                logger.info(f"Event type {event_type} not handled, marking as completed")
                result = {"status": "ignored", "message": f"Event type {event_type} not handled"}
            
            # Mark as completed
            await stripe_webhook_event_crud.update_processing_status(
                webhook_event=existing_event,
                status=WebhookEventStatus.COMPLETED,
                processing_result=result,
                session=session
            )
            
            logger.info(f"Successfully processed webhook event {event_id}")
            return result
            
        except Exception as e:
            error_message = f"Failed to process event {event_id}: {str(e)}"
            logger.error(error_message)
            
            # Mark as failed
            await stripe_webhook_event_crud.update_processing_status(
                webhook_event=existing_event,
                status=WebhookEventStatus.FAILED,
                error_message=error_message,
                session=session
            )
            
            raise WebhookProcessingError(error_message) from e

    async def _process_invoice_paid(
        self, 
        event_data: dict[str, Any], 
        webhook_event, 
        session: AsyncSession
    ) -> dict[str, Any]:
        """
        Process an invoice.paid webhook event.
        
        Args:
            event_data: The webhook event data
            webhook_event: The webhook event database record
            session: Database session
            
        Returns:
            Processing result dictionary
        """
        try:
            # Parse the invoice data
            invoice_data = event_data["data"]["object"]
            invoice_id = invoice_data["id"]
            customer_id = invoice_data["customer"]
            subscription_id = invoice_data.get("subscription")
            amount_paid = invoice_data["amount_paid"]
            currency = invoice_data["currency"]
            
            logger.info(
                f"Processing invoice.paid event: invoice={invoice_id}, "
                f"customer={customer_id}, subscription={subscription_id}, "
                f"amount={amount_paid} {currency}"
            )
            
            # Update webhook event with extracted IDs
            webhook_event.customer_id = customer_id
            webhook_event.subscription_id = subscription_id
            webhook_event.invoice_id = invoice_id
            
            result = {
                "invoice_id": invoice_id,
                "customer_id": customer_id,
                "subscription_id": subscription_id,
                "amount_paid": amount_paid,
                "currency": currency,
                "actions_taken": []
            }
            
            # If there's a subscription, update its status
            if subscription_id:
                logger.info(f"Processing subscription {subscription_id} from checkout session")

                # Retrieve full subscription data from Stripe
                stripe_subscription = await stripe_service.get_subscription(
                    subscription_id=subscription_id)
                product = await cruds.product_crud.get_by_stripe_id(
                    stripe_product_id=stripe_subscription.product_id, session=session
                )
                internal_subscription = await cruds.stripe_subscription_crud.get_by_stripe_id(
                    stripe_id=subscription_id, session=session
                )
                if internal_subscription is None:
                    logger.info(f"Internal subscription {internal_subscription} not found")
                    return result
                code = product.stripe_metadata["code"]
                organization_subscription_id = await change_org_plan(
                    organization_id=internal_subscription.organization_id,
                    code=code,
                    cancel_at=stripe_subscription.cancel_at,
                    session=session
                )
                logger.info(f"{organization_subscription_id} org subscription id")
                subscription_dict = {
                    "id": stripe_subscription.id,
                    "organization_subscription_id": organization_subscription_id,
                    "status": stripe_subscription.status,
                    "cancel_at": stripe_subscription.cancel_at,
                    "latest_invoice": stripe_subscription.latest_invoice,
                    "identity_id": internal_subscription.identity_id,
                    "organization_id": internal_subscription.organization_id,
                }
                # Upsert the subscription
                await cruds.stripe_subscription_crud.upsert_from_stripe(
                    stripe_subscription=subscription_dict, session=session
                )

            # Log the successful payment for audit purposes
            logger.info(
                f"Payment processed successfully: "
                f"Invoice {invoice_id} paid {amount_paid} {currency} "
                f"for customer {customer_id}"
            )
            
            result["actions_taken"].append("payment_logged")
            result["status"] = "success"
            result["message"] = "Invoice payment processed successfully"
            
            return result
            
        except KeyError as e:
            raise WebhookProcessingError(f"Missing required field in invoice data: {e}") from e
        except Exception as e:
            raise WebhookProcessingError(f"Error processing invoice.paid event: {e}") from e


# Singleton instance
stripe_webhook_processor = StripeWebhookProcessor()
