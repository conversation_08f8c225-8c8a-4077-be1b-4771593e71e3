import logging
import os

from celery import Celery
from celery.signals import worker_process_init

from config.loader import SettingsFactory

settings = SettingsFactory.settings()
celery_app = Celery(
    __name__,
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
)

# Configure Celery based on environment
config_updates = {
    # Use JSON for task serialization (default)
    "task_serializer": "json",
    # Accept both JSON and pickle content for backward compatibility
    "accept_content": ["json", "pickle"],
    # Use JSON for result serialization to avoid pickle security issues
    "result_serializer": "json",
    # Ensure task results are encoded as JSON
    "result_accept_content": ["json"],
    # Enable UTC timezone
    "timezone": "UTC",
    "enable_utc": True,
    # Task result expiration (1 hour)
    "result_expires": 3600,
    # Worker prefetch multiplier (how many tasks to prefetch)
    "worker_prefetch_multiplier": 1,
    # Task acks late (acknowledge after task completion)
    "task_acks_late": True,
    # Task reject on worker lost
    "task_reject_on_worker_lost": True,
}

# Development environment configuration to avoid task persistence issues
if settings.MODE.value == "dev":
    config_updates.update({
        # Don't store task results in development to avoid accumulation
        "task_ignore_result": True,
        # Shorter task expiration in development
        "result_expires": 300,  # 5 minutes
        # Don't persist tasks to disk in development
        "task_always_eager": os.getenv("CELERY_ALWAYS_EAGER", "false").lower() == "true",
    })

celery_app.conf.update(config_updates)


# Set environment variable to identify Celery worker context
@worker_process_init.connect
def set_worker_env(**kwargs):
    """Set environment variables when worker process starts"""
    os.environ["CELERY_WORKER"] = "1"

    # http_loggers = ["httpx", "openai", "requests", "urllib3"]
    if os.getenv("ENABLE_HTTP_LOG", "false").lower() == "false":
        logging.getLogger("httpx").setLevel(logging.WARNING)
    if os.getenv("ENABLE_AZURE_LOGS", "false").lower() == "false":
        logging.getLogger("azure").setLevel(logging.WARNING)
        logging.getLogger("azure.core").setLevel(logging.WARNING)
        logging.getLogger("azure.ai.inference").setLevel(logging.WARNING)


# 自动发现 tasks 模块下的所有任务
