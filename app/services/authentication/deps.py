from typing import Annotated

from fastapi import Depends
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTP<PERSON>ear<PERSON>
from loguru import logger

from app.exceptions import UnauthenticatedException

from .firebase import FirebaseUser, verify_token

bearer_scheme = HTTPBearer(auto_error=False)
BearerDep = Annotated[HTTPAuthorizationCredentials | None, Depends(bearer_scheme)]


def get_current_user(bearer: BearerDep) -> FirebaseUser:
    if bearer and (token := bearer.credentials):
        try:
            decoded_token = verify_token(token)
            return FirebaseUser(**decoded_token)
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise UnauthenticatedException(message="Invalid or expired token")
    else:
        raise UnauthenticatedException(message="Authentication required")


FirebaseUserDep = Annotated[FirebaseUser, Depends(get_current_user)]
