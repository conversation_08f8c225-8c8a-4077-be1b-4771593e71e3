import json

import firebase_admin
from firebase_admin import auth, credentials
from pydantic import BaseModel

from config.loader import SettingsFactory

settings = SettingsFactory.settings()
firebase_config = json.loads(settings.FIREBASE_ADMIN_SDK)
cred = credentials.Certificate(firebase_config)
firebase_app = firebase_admin.initialize_app(cred)


class FirebaseIdentities(BaseModel):
    email: list[str]


class FirebaseInfo(BaseModel):
    identities: FirebaseIdentities
    sign_in_provider: str


class FirebaseUser(BaseModel):
    iss: str
    aud: str
    auth_time: int
    user_id: str
    sub: str
    iat: int
    exp: int
    email: str
    email_verified: bool
    firebase: FirebaseInfo
    uid: str


def verify_token(id_token: str) -> dict:
    return auth.verify_id_token(id_token)
