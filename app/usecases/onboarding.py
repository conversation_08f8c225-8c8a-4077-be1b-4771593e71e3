import pendulum
from sqlalchemy.exc import SQLAlchemyError
from sqlmodel import select

from app.exceptions import NotFoundException
from app.modules import cruds
from app.modules.invitation.models import Invitation
from app.modules.models import (
    Identity,
)
from app.modules.organization.member.schema import IOrganizationMemberCreate
from app.modules.organization.project.member.schema import IProjectMemberCreate
from app.modules.role.models import Role
from app.services.authentication.firebase import FirebaseUser
from app.services.db.session import AsyncSession

from .identity_manager import create_identity_with_defaults, get_identity_with_defaults


async def register_without_invitation(firebase_user: FirebaseUser, db_session: AsyncSession) -> Identity:
    """Complete user onboarding process including identity, organization, and project creation."""
    identity, default_org, default_proj = await create_identity_with_defaults(firebase_user, db_session)
    return identity


async def register_with_invitation(
    firebase_user: FirebaseUser, invitation: Invitation, db_session: AsyncSession
) -> Identity:
    """Complete user onboarding process for invited users - join existing org/project."""
    try:
        # Validate invitation has target context
        if not invitation.target_organization_id and not invitation.target_project_id:
            raise ValueError("Invitation must specify either organization_id or project_id")

        # Create core entities
        identity, default_org, default_proj = await create_identity_with_defaults(firebase_user, db_session)

        # Get role for the invitation
        if not invitation.role:
            raise ValueError(f"Role with id {invitation.invited_role_id} not found")

        # Handle project-specific invitation first (more specific context)
        if invitation.project_id:
            # Add as project member
            proj_member_create = IProjectMemberCreate(
                project_id=invitation.project_id,
                identity_id=identity.id,
                role=invitation.role,
                is_active=True,
                invitation_id=invitation.id,
                joined_at=pendulum.now("UTC"),
            )
            await cruds.ProjectMemberRepo.create(new=proj_member_create, db_session=db_session)

            # Set as default project
            identity.default_project_id = invitation.project_id

            # If project invitation doesn't include organization membership,
            # we still need to check if user should be added to the organization
            if invitation.organization_id:
                # Also add as organization member
                org_member_create = IOrganizationMemberCreate(
                    organization_id=invitation.organization_id,
                    identity_id=identity.id,
                    role_id=invitation.invited_role_id,
                    is_active=True,
                    invitation_id=invitation.id,
                    joined_at=pendulum.now("UTC"),
                )
                await cruds.OrganizationMemberRepo.create(new=org_member_create, db_session=db_session)

        elif invitation.organization_id:
            # Organization-only invitation - add as organization member
            org_member_create = IOrganizationMemberCreate(
                organization_id=invitation.organization_id,
                identity_id=identity.id,
                role_id=invitation.invited_role_id,
                is_active=True,
                invitation_id=invitation.id,
                joined_at=pendulum.now("UTC"),
            )
            await cruds.OrganizationMemberRepo.create(new=org_member_create, db_session=db_session)

            # Find a suitable default project from organization's projects
            org_projects = await cruds.ProjectRepo.get_by_organization_id(
                organization_id=invitation.organization_id, db_session=db_session
            )
            if org_projects:
                # Use the first project as default (could be enhanced with logic for "default" projects)
                identity.default_project_id = org_projects[0].id

                # Also add user to the default project with the same role
                proj_member_create = IProjectMemberCreate(
                    project_id=org_projects[0].id,
                    identity_id=identity.id,
                    role_id=invitation.invited_role_id,
                    is_active=True,
                    invitation_id=invitation.id,
                    joined_at=pendulum.now("UTC"),
                )
                await cruds.ProjectMemberRepo.create(new=proj_member_create, db_session=db_session)
    except SQLAlchemyError:
        await db_session.rollback()
        raise
    else:
        # Return the fully populated identity with relationships
        identity = await get_identity_with_defaults(firebase_user.uid, db_session=db_session)
        return identity


async def login_with_invitation(identity: Identity, invitation: Invitation, db_session: AsyncSession) -> Identity:
    """Handle invitation for existing user - add to org/project."""
    try:
        # Validate invitation has target context
        if not invitation.target_organization_id and not invitation.target_project_id:
            raise ValueError("Target not found")

        # Get role for the invitation
        role_stmt = select(Role).where(Role.id == invitation.target_role_id)
        role_result = await db_session.execute(role_stmt)
        role = role_result.scalar_one_or_none()

        if not role:
            raise NotFoundException(Role)

        # Track if any memberships were added
        memberships_added = False
        new_default_project_id = None

        # Add user to organization if specified
        if invitation.target_organization_id:
            # Check if already a member
            existing_org_member = await cruds.OrganizationMemberRepo.get_by_organization_and_identity(
                organization_id=invitation.target_organization_id, identity_id=identity.id, db_session=db_session
            )

            if not existing_org_member:
                org_member_create = IOrganizationMemberCreate(
                    organization_id=invitation.target_organization_id,
                    identity_id=identity.id,
                    role_id=invitation.target_role_id,
                    is_active=True,
                    invitation_id=invitation.id,
                    joined_at=pendulum.now("UTC"),
                )
                await cruds.OrganizationMemberRepo.create(new=org_member_create, db_session=db_session)
                memberships_added = True

        # Add user to specific project if specified
        if invitation.target_project_id:
            # Check if already a member
            existing_proj_member = await cruds.ProjectMemberRepo.get_by_project_and_identity(
                project_id=invitation.target_project_id, identity_id=identity.id, db_session=db_session
            )

            if not existing_proj_member:
                proj_member_create = IProjectMemberCreate(
                    project_id=invitation.target_project_id,
                    identity_id=identity.id,
                    role_id=invitation.target_role_id,
                    is_active=True,
                    invitation_id=invitation.id,
                    joined_at=pendulum.now("UTC"),
                )
                await cruds.ProjectMemberRepo.create(new=proj_member_create, db_session=db_session)
                memberships_added = True
                new_default_project_id = invitation.target_project_id

        # Update default project if user was added to a specific project
        # or if they don't have a default project and were added to an organization
        if new_default_project_id:
            identity.default_project_id = new_default_project_id
        elif not identity.default_project_id and invitation.target_organization_id and memberships_added:
            # Find a suitable default project from the organization
            org_projects = await cruds.ProjectRepo.get_by_organization_id(
                organization_id=invitation.target_organization_id, db_session=db_session
            )
            if org_projects:
                identity.default_project_id = org_projects[0].id
    except SQLAlchemyError:
        await db_session.rollback()
        raise
    else:
        refreshed_identity = await get_identity_with_defaults(
            identity.auth_provider.provider_uid, db_session=db_session
        )
        return refreshed_identity
