from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.modules.models import OrganizationSubscription, SubscriptionPlan
from app.modules.organization.subscription.schema import ICurrentSubscriptionRead
from app.services.db.session import AsyncSession


def get_token_quota(subscription: OrganizationSubscription, plan: SubscriptionPlan) -> int:
    if subscription.token_quota is None:
        return plan.token_quota
    return subscription.token_quota


async def get_current_subscription(org_id: str, session: AsyncSession) -> ICurrentSubscriptionRead | None:
    stmt = (
        select(OrganizationSubscription)
        .where(OrganizationSubscription.organization_id == org_id)
        .options(selectinload(OrganizationSubscription.plan))
    )

    result = await session.execute(stmt)
    org_sub = result.scalar_one_or_none()

    if not org_sub:
        return None

    return ICurrentSubscriptionRead(
        plan_name=org_sub.plan.name,
        status=org_sub.status,
        created_at=org_sub.created_at,
        updated_at=org_sub.updated_at,
        expires_at=org_sub.expires_at,
        started_at=org_sub.started_at,
        token_quota=get_token_quota(org_sub, org_sub.plan),
    )
