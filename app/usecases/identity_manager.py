from sqlalchemy.orm import selectinload
from sqlmodel import select

from app.modules.identity import Identity
from app.modules.identity.auth_provider import IdentityAuthProvider
from app.modules.identity.usecases import create_identity_resources
from app.modules.organization import Organization, OrganizationMember, Project, ProjectMember
from app.modules.organization.usecases import create_org_with_defaults
from app.modules.role import Role
from app.services.authentication.firebase import FirebaseUser
from app.services.db.session import AsyncSession


async def create_identity_with_defaults(firebase_user: FirebaseUser, db_session: AsyncSession):
    new_identity = await create_identity_resources(firebase_user, db_session)

    default_org, default_proj = await create_org_with_defaults(
        new_identity.id, new_identity.slug, db_session=db_session
    )

    await db_session.flush()
    new_identity.default_project_id = default_proj.id

    return new_identity, default_org, default_proj


async def get_identity_with_defaults(provider_uid: str, db_session: AsyncSession) -> Identity | None:
    stmt = (
        select(Identity)
        .join(IdentityAuthProvider)
        .options(
            selectinload(Identity.auth_provider),
            selectinload(Identity.default_project).selectinload(Project.organization),
        )
        .where(IdentityAuthProvider.provider_uid == provider_uid)
        .where(Identity.deleted_at.is_(None))
    )

    result = await db_session.execute(stmt)
    identity = result.scalar_one_or_none()

    return identity


async def get_organizations_and_projects(
    identity_id: str, db_session: AsyncSession
) -> tuple[list[tuple[Organization, str]], list[tuple[Project, str]]]:
    # Get all organizations where the identity is a member with their roles
    org_stmt = (
        select(Organization, Role.name)
        .select_from(Organization)
        .join(OrganizationMember, Organization.id == OrganizationMember.organization_id)
        .join(Role, OrganizationMember.role_id == Role.id)
        .where(OrganizationMember.identity_id == identity_id)
        .where(OrganizationMember.is_active.is_(True))
        .where(Organization.deleted_at.is_(None))
    )

    # Get all projects where the identity is a member with their roles
    project_stmt = (
        select(Project, Role.name)
        .select_from(Project)
        .join(ProjectMember, Project.id == ProjectMember.project_id)
        .join(Role, ProjectMember.role_id == Role.id)
        .options(selectinload(Project.organization))
        .where(ProjectMember.identity_id == identity_id)
        .where(ProjectMember.is_active.is_(True))
        .where(Project.deleted_at.is_(None))
    )

    org_result = await db_session.execute(org_stmt)
    project_result = await db_session.execute(project_stmt)

    # Get organizations with their role names
    user_organizations = list(org_result.all())
    user_projects = list(project_result.all())

    return user_organizations, user_projects
