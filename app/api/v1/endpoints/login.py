from dataclasses import dataclass

from fastapi import APIRouter
from pydantic import BaseModel

from app.exceptions.models import error_responses
from app.modules import cruds
from app.modules.identity import Identity
from app.modules.identity.schema import IIdentityReadWithProject
from app.modules.identity.usecases import get_identity_by_provider_uid
from app.modules.invitation import Invitation
from app.modules.invitation.deps import ResolvedInvitationDep
from app.modules.invitation.schema import IInvitationCheck
from app.modules.permission.deps import need_any_firebase_user
from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.services.authentication.firebase import FirebaseUser
from app.services.db.deps import AsyncSession, DatabaseSessionDep
from app.usecases.onboarding import (
    get_identity_with_defaults,
    login_with_invitation,
    register_with_invitation,
    register_without_invitation,
)


class ILoginResponse(BaseModel):
    identity: IIdentityReadWithProject
    invitation: IInvitationCheck | None = None


CAN_LOGIN_OR_REGISTER = need_any_firebase_user()


@dataclass
class LoginCase:
    is_existing_user: bool
    has_valid_invitation: bool


async def handle_login_case(
    firebase_user: FirebaseUser,
    identity: Identity | None,
    invitation: Invitation | None,
    invitation_check: IInvitationCheck | None,
    db_session: AsyncSession,
) -> tuple[IIdentityReadWithProject, IInvitationCheck, str]:
    """Handle login cases using match statement."""
    is_existing_user = identity is not None
    has_valid_invitation = invitation is not None
    login_case = LoginCase(is_existing_user=is_existing_user, has_valid_invitation=has_valid_invitation)

    # invitation_check_log = (
    #     f" (invalid invitation: {invitation_check.model_dump()})"
    #     if invitation_check and not invitation_check.is_valid
    #     else ""
    # )

    match login_case:
        case LoginCase(is_existing_user=True, has_valid_invitation=False):
            # Handle existing user simple login
            await cruds.IdentityRepo.update_last_login_at(identity=identity, db_session=db_session)
            await db_session.flush()
            updated_identity = await get_identity_with_defaults(firebase_user.uid, db_session=db_session)
            identity_info = IIdentityReadWithProject.from_identity(updated_identity)
            invitation_info = IInvitationCheck.model_validate(invitation_check) if invitation_check else None
            return identity_info, invitation_info, "Login successful"
        case LoginCase(is_existing_user=False, has_valid_invitation=False):
            # Handle new user registration
            await register_without_invitation(firebase_user, db_session=db_session)
            await db_session.flush()
            new_identity = await get_identity_with_defaults(firebase_user.uid, db_session=db_session)
            identity_info = IIdentityReadWithProject.from_identity(new_identity)
            invitation_info = IInvitationCheck.model_validate(invitation_check) if invitation_check else None
            return identity_info, invitation_info, "Account created successfully with default organization and project"
        case LoginCase(is_existing_user=True, has_valid_invitation=True):
            # Handle existing user with valid invitation
            updated_identity = await login_with_invitation(identity, invitation)
            updated_invitation = await cruds.invitation.use_invitation(
                invitation=invitation, invited_identity_id=updated_identity.id, db_session=db_session
            )
            invitation_info = IInvitationCheck(
                is_valid=invitation_check.is_valid,
                used_at=updated_invitation.used_at,
                invited_by_name=invitation.invited_by.name if invitation.invited_by else None,
            )
            return updated_identity, invitation_info, "Successfully joined organization/project via invitation"
        case LoginCase(is_existing_user=False, has_valid_invitation=True):
            # Handle new user with valid invitation
            identity = await register_with_invitation(firebase_user, invitation, db_session=db_session)
            updated_invitation = await cruds.invitation.use_invitation(
                invitation=invitation, invited_identity_id=identity.id, db_session=db_session
            )
            invitation_info = IInvitationCheck(
                is_valid=invitation_check.is_valid,
                used_at=updated_invitation.used_at,
                invited_by_name=invitation.invited_by.name if invitation.invited_by else None,
            )
            return identity, invitation_info, "Account created and successfully joined"


router = APIRouter()


@router.get("/", responses=error_responses)
async def login_or_register(
    *, firebase_user=CAN_LOGIN_OR_REGISTER, resolved_invitation: ResolvedInvitationDep, db_session: DatabaseSessionDep
) -> IGetResponseBase[ILoginResponse]:
    # Validate Firebase user data
    # TODO: Consider adding rate limiting based on firebase_user.uid
    invitation, invitation_check = resolved_invitation

    identity = await get_identity_by_provider_uid(provider_uid=firebase_user.uid, db_session=db_session)

    identity_info, invitation_info, message = await handle_login_case(
        firebase_user,
        identity=identity,
        invitation=invitation,
        invitation_check=invitation_check,
        db_session=db_session,
    )

    result = ILoginResponse(identity=identity_info, invitation=invitation_info)
    return create_response(data=result, message=message)
