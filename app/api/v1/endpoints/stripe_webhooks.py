from fastapi import APIRouter, HTTPException, Request, status
from loguru import logger

from app.modules.stripe_webhook.schema import WebhookErrorResponse, WebhookProcessingResponse
from app.services.billing.webhook_processor import WebhookProcessingError, stripe_webhook_processor
from app.services.billing.webhook_verification import WebhookSignatureError, webhook_verification_service
from app.services.db.deps import DatabaseSessionDep

router = APIRouter()


@router.post("/stripe", response_model=WebhookProcessingResponse)
async def handle_stripe_webhook(
    request: Request,
    session: DatabaseSessionDep,
) -> WebhookProcessingResponse:
    """
    Handle Stripe webhook events.
    
    This endpoint:
    1. Verifies the webhook signature for security
    2. Processes the event based on its type
    3. Implements idempotency to prevent duplicate processing
    4. Logs all events for audit purposes
    5. Returns appropriate HTTP status codes
    
    Supported events:
    - invoice.paid: Updates subscription status and logs payment
    
    Returns:
        WebhookProcessingResponse with processing details
        
    Raises:
        HTTPException: For various error conditions with appropriate status codes
    """
    try:
        # Get the raw request body and signature header
        body = await request.body()
        signature_header = request.headers.get("stripe-signature")
        
        if not signature_header:
            logger.error("Missing Stripe-Signature header")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing Stripe-Signature header"
            )
        
        # Verify the webhook signature
        try:
            event_data = webhook_verification_service.verify_signature(
                payload=body,
                signature_header=signature_header
            )
        except WebhookSignatureError as e:
            logger.error(f"Webhook signature verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Webhook signature verification failed: {str(e)}"
            ) from e
        
        event_id = event_data.get("id", "unknown")
        event_type = event_data.get("type", "unknown")
        
        logger.info(f"Received verified webhook event {event_id} of type {event_type}")
        
        # Process the event
        try:
            processing_result = await stripe_webhook_processor.process_event(
                event_data=event_data,
                session=session
            )
            
            # Determine if this was a duplicate
            is_duplicate = processing_result.get("status") == "duplicate"
            
            response = WebhookProcessingResponse(
                event_id=event_id,
                event_type=event_type,
                processed=not is_duplicate,
                message=processing_result.get("message", "Event processed successfully")
            )
            
            if is_duplicate:
                logger.info(f"Webhook event {event_id} was a duplicate")
            else:
                logger.info(f"Successfully processed webhook event {event_id}")
            
            return response
            
        except WebhookProcessingError as e:
            logger.error(f"Failed to process webhook event {event_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to process webhook event: {str(e)}"
            ) from e
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.exception(f"Unexpected error processing webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error processing webhook"
        ) from e