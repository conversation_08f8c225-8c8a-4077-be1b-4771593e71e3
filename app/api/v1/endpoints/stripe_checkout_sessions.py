from fastapi import APIRouter, HTTPException, status
from loguru import logger

from app.exceptions import NotFoundException, ValidationException
from app.modules import cruds
from app.modules.checkout_session.schema import (
    CheckoutSessionProcessResponse,
    StripeCheckoutRequest,
    StripeCheckoutResponse,
)
from app.modules.permission.deps import need_any_identity, need_org_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.services.billing.client import StripeAPIError, stripe_service
from app.services.db.deps import DatabaseSessionDep
from app.usecases.checkout_session import process_checkout_session_completion
from config.loader import SettingsFactory

resource = "checkout_session"
CAN_CREATE = need_org_permission(Action.CREATE, resource, need_email_verified=True)
CAN_PROCESS = need_any_identity()


router = APIRouter()


def _validate_organization_membership(org_member) -> None:
    """Validate that the user is an active member of the organization."""
    if not org_member or not org_member.is_active:
        raise ValidationException(message="User is not an active member of the specified organization")


def _validate_user_email(identity) -> None:
    """Validate that the user has an email address."""
    if not identity.email:
        raise ValidationException(message="User email is required for checkout")


def _validate_price_exists(price) -> None:
    """Validate that the price exists in our database."""
    if not price:
        raise NotFoundException(message="Price not found")


@router.post("/{organization_id}/billing", response_model=IGetResponseBase[StripeCheckoutResponse])
async def create_stripe_checkout(
    *, identity=CAN_CREATE, organization_id: str, checkout_request: StripeCheckoutRequest, session: DatabaseSessionDep
) -> IGetResponseBase[StripeCheckoutResponse]:
    """
    Create a Stripe checkout session and return the checkout URL.

    This endpoint:
    1. Gets the identity_id from the current session
    2. Gets the organization_id from the API request
    3. Gets the email from the identity
    4. Uses the email to trigger Stripe checkout with fixed success/cancel URLs
    5. Stores the organization_id in the checkout table
    6. Returns the Stripe checkout URL and session details
    """
    try:
        # Validate that the organisation is not within pro subscription
        org_subscription = await cruds.organization_subscription.get_active_by_org(
            org_id=organization_id, db_session=session
        )
        await session.refresh(org_subscription, ["plan"])
        if org_subscription.plan.code == "MEMU-PRO-v20250731":
            return create_response(
                data=StripeCheckoutResponse(stripe_session_id="", checkout_url=""),
                message="Checkout session is not allowed",
            )
        # Validate that the price exists in our database
        price = await cruds.price_crud.get_by_stripe_price_id(
            stripe_price_id=checkout_request.price_id, session=session
        )
        _validate_price_exists(price)

        # Create Stripe checkout session
        logger.info(
            f"Creating Stripe checkout for identity {identity.id}, organization {organization_id}, email {identity.email}"
        )

        setting = SettingsFactory.settings()
        stripe_checkout = await stripe_service.create_checkout_session(
            price_id=checkout_request.price_id,
            customer_email=identity.email,
            success_url=f"{setting.HOST_URL}/purchase-result?status=success",
            cancel_url=f"{setting.HOST_URL}/purchase-result?status=error",
        )

        # Store checkout session in database
        checkout_session = await cruds.checkout_session_crud.upsert_from_stripe(
            stripe_checkout=stripe_checkout.model_dump(),
            identity_id=identity.id,
            organization_id=organization_id,
            session=session,
        )

        logger.info(f"Created checkout session {checkout_session.id} with Stripe session {stripe_checkout.id}")

        # Get the checkout URL
        checkout_url = stripe_checkout.url

        logger.info(f"Created Stripe checkout URL: {checkout_url}")

        # Create response data
        response_data = StripeCheckoutResponse(
            stripe_session_id=stripe_checkout.id,
            checkout_url=checkout_url,
        )
        await session.commit()
        # Return checkout URL and session details
        return create_response(data=response_data, message="Checkout session created successfully")
    except StripeAPIError as e:
        logger.error(f"Stripe API error: {e}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY, detail="Failed to create Stripe checkout session"
        ) from e
    except (NotFoundException, ValidationException) as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Unexpected error creating checkout: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error") from e


@router.get("/sessions", response_model=IGetResponseBase[dict])
async def get_checkout_sessions(*, identity=CAN_CREATE, session: DatabaseSessionDep) -> IGetResponseBase[dict]:
    """Get all checkout sessions for the current user."""
    try:
        checkout_sessions = await cruds.checkout_session_crud.get_by_identity_id(
            identity_id=identity.id, session=session
        )

        # Convert to dict format for response
        sessions_data = []
        for checkout_session in checkout_sessions:
            sessions_data.append({
                "id": checkout_session.id,
                "stripe_id": checkout_session.stripe_id,
                "payment_status": checkout_session.payment_status,
                "status": checkout_session.status,
                "organization_id": checkout_session.organization_id,
                "created_at": checkout_session.created_at,
                "updated_at": checkout_session.updated_at,
            })
        sessions_result = {"items": sessions_data}

        return create_response(data=sessions_result, message="Checkout sessions retrieved successfully")

    except Exception as e:
        logger.error(f"Error retrieving checkout sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve checkout sessions"
        ) from e


@router.post("/sessions/process", response_model=IGetResponseBase[CheckoutSessionProcessResponse])
async def process_checkout_session_completion_endpoint(
    *, identity=CAN_PROCESS, session: DatabaseSessionDep
) -> IGetResponseBase[CheckoutSessionProcessResponse]:
    """
    Process Stripe checkout session completion and update internal records.

    This endpoint:
    1. Retrieves the complete checkout session data from Stripe API
    2. Updates the internal StripeCheckoutSession record with latest status
    3. If a subscription was created, upserts the StripeSubscription table
    """
    try:
        # Process checkout session completion using usecase
        subscription_result = await process_checkout_session_completion(identity_id=identity.id, session=session)

        # Prepare response data
        response_data = CheckoutSessionProcessResponse(
            subscriptions=subscription_result,
        )

        return create_response(data=response_data, message="Checkout session processed successfully")

    except StripeAPIError as e:
        logger.error(f"Stripe API error processing checkout session: {e}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY, detail="Failed to retrieve data from Stripe"
        ) from e
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing checkout session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while processing checkout session",
        ) from e
