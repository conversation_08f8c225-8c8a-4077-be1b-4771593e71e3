from fastapi import APIRouter

from app.modules.identity.schema import IIdentityOrganizationsResponse
from app.modules.permission.deps import need_any_identity
from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.services.db.deps import DatabaseSessionDep
from app.usecases.identity_manager import get_organizations_and_projects

router = APIRouter(prefix="/identity", tags=["Identity Resource"])
SELF = need_any_identity()


@router.get("/organizations")
async def get_organizations(
    *, identity=SELF, session: DatabaseSessionDep
) -> IGetResponseBase[IIdentityOrganizationsResponse]:
    """Get all organizations and projects for the current user"""
    organizations_with_roles, projects_with_roles = await get_organizations_and_projects(
        identity_id=identity.id, db_session=session
    )

    result = IIdentityOrganizationsResponse.format_from_data(organizations_with_roles, projects_with_roles)
    return create_response(data=result, message="Organizations retrieved successfully")
