# from fastapi import APIRouter, Depends

# from app.exceptions import NotFoundException, UnauthorizedException
# from app.exceptions.models import error_responses
# from app.modules import cruds
# from app.modules.invitation.schema import (
#     IInvitationCreate,
#     IInvitationListResponse,
#     IInvitationResponse,
# )
# from app.modules.shared.base_schema import IGetResponseBase, create_response


# resource = "invitation"
# CAN_CREATE = need_

# router = APIRouter()


# @router.post("/", responses=error_responses)
# async def create_invitation(
#     invitation_data: IInvitationCreate,
#     current_identity=Depends(get_current_identity),
# ) -> IGetResponseBase[IInvitationResponse]:
#     """Create a new invitation for a user"""

#     # Check if there's already an active invitation for this email
#     await cruds.invitation.get_active_by_email(email=invitation_data.email)

#     # Create the invitation
#     invitation = await cruds.invitation.create_invitation(
#         email=invitation_data.email,
#         invited_by_id=current_identity.id,
#         expires_hours=invitation_data.expires_hours,
#     )

#     # Prepare response
#     response_data = IInvitationResponse(
#         id=invitation.id,
#         email=invitation.email,
#         token=invitation.token,
#         expires_at=invitation.expires_at,
#         used_at=invitation.used_at,
#         is_active=invitation.is_active,
#         invited_by_id=invitation.invited_by_id,
#         invited_identity_id=invitation.invited_identity_id,
#         is_expired=invitation.is_expired(),
#         is_usable=invitation.is_usable(),
#     )

#     return create_response(data=response_data, message="Invitation created successfully")


# @router.get("/", responses=error_responses)
# async def list_invitations(
#     current_user=Depends(get_current_identity),
# ) -> IGetResponseBase[IInvitationListResponse]:
#     """List all invitations created by the current user"""

#     invitations = await cruds.invitation.get_invitations_by_inviter(invited_by_id=current_user.id)

#     invitation_responses = []
#     for invitation in invitations:
#         invitation_responses.append(
#             IInvitationResponse(
#                 id=invitation.id,
#                 email=invitation.email,
#                 token=invitation.token,
#                 expires_at=invitation.expires_at,
#                 used_at=invitation.used_at,
#                 is_active=invitation.is_active,
#                 invited_by_id=invitation.invited_by_id,
#                 invited_identity_id=invitation.invited_identity_id,
#                 is_expired=invitation.is_expired(),
#                 is_usable=invitation.is_usable(),
#             )
#         )

#     response_data = IInvitationListResponse(invitations=invitation_responses)
#     return create_response(data=response_data, message="Invitations retrieved successfully")


# @router.get("/{invitation_id}", responses=error_responses)
# async def get_invitation(
#     invitation_id: str,
#     current_user=Depends(get_current_identity),
# ) -> IGetResponseBase[IInvitationResponse]:
#     """Get a specific invitation by ID"""

#     invitation = await cruds.invitation.get(id=invitation_id)
#     if not invitation:
#         raise NotFoundException(message="Invitation not found")

#     # Check if the current user created this invitation
#     if invitation.invited_by_id != current_user.id:
#         raise UnauthorizedException(message="Access denied")

#     response_data = IInvitationResponse(
#         id=invitation.id,
#         email=invitation.email,
#         token=invitation.token,
#         expires_at=invitation.expires_at,
#         used_at=invitation.used_at,
#         is_active=invitation.is_active,
#         invited_by_id=invitation.invited_by_id,
#         invited_identity_id=invitation.invited_identity_id,
#         is_expired=invitation.is_expired(),
#         is_usable=invitation.is_usable(),
#     )

#     return create_response(data=response_data, message="Invitation retrieved successfully")


# @router.delete("/{invitation_id}", responses=error_responses)
# async def deactivate_invitation(
#     invitation_id: str,
#     current_user=Depends(get_current_identity),
# ) -> IGetResponseBase[IInvitationResponse]:
#     """Deactivate an invitation"""

#     invitation = await cruds.invitation.get(id=invitation_id)
#     if not invitation:
#         raise NotFoundException(message="Invitation not found")

#     # Check if the current user created this invitation
#     if invitation.invited_by_id != current_user.id:
#         raise UnauthorizedException(status_code=403, detail="Access denied")

#     # Deactivate the invitation
#     invitation = await cruds.invitation.deactivate_invitation(invitation=invitation)

#     response_data = IInvitationResponse(
#         id=invitation.id,
#         email=invitation.email,
#         token=invitation.token,
#         expires_at=invitation.expires_at,
#         used_at=invitation.used_at,
#         is_active=invitation.is_active,
#         invited_by_id=invitation.invited_by_id,
#         invited_identity_id=invitation.invited_identity_id,
#         is_expired=invitation.is_expired(),
#         is_usable=invitation.is_usable(),
#     )

#     return create_response(data=response_data, message="Invitation deactivated successfully")
