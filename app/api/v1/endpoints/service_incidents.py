from fastapi import APIRouter, HTTPException

from app.modules.service_incident.crud import service_incident as crud
from app.modules.service_incident.schema import ICreateServiceIncident, IServiceIncidentResponse, IUpdateServiceIncident
from app.modules.shared.base_schema import (
    IDeleteResponseBase,
    IGetResponsePaginated,
    IPostResponseBase,
    create_response,
)
from app.services.db.deps import DatabaseSessionDep

router = APIRouter()


@router.get("/recent")
async def get_recent_incidents(session: DatabaseSessionDep) -> IGetResponsePaginated[IServiceIncidentResponse]:
    """Get incidents by service name."""
    incidents_data = await crud.list_multi_paginated_ordered(order_by="created_at", order="desc", session=session)
    return create_response(data=incidents_data, message="Recent incidents retrieved successfully")


@router.post("/", response_model=IPostResponseBase[IServiceIncidentResponse])
async def create_incident(incident: ICreateServiceIncident, session: DatabaseSessionDep):
    # TODO: add created_by from identity id
    new_incident = await crud.create(new=incident, session=session)
    return create_response(data=new_incident, message="Incident created successfully")


@router.post("/update", response_model=IPostResponseBase[bool])
async def update_incident(
    incident: IUpdateServiceIncident,
    session: DatabaseSessionDep,
):
    try:
        success = await crud.update(update_id=incident.id, update_data=incident, session=session)
        await session.commit()
        if success:
            return create_response(data=True, message="更新成功")
        else:
            return create_response(data=False, message="更新失败")
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=400, detail=f"请求失败: {e!s}") from e


@router.delete("/{incident_id}", response_model=IDeleteResponseBase[IServiceIncidentResponse])
async def delete_incident(incident_id: str, session: DatabaseSessionDep):
    delete_incident = await crud.remove(id=incident_id, session=session)
    return create_response(data=delete_incident)
