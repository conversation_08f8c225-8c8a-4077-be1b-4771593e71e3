import logging

from fastapi import APIRouter, HTTPException, status

from app.services.billing.sync import stripe_sync_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/sync", response_model=dict[str, int])
async def manual_stripe_sync():
    """Manually trigger Stripe products and prices sync."""
    try:
        result = await stripe_sync_service.sync_products_and_prices(force=True)
        logger.info(f"Manual sync completed: {result}")
    except Exception as e:
        logger.exception("Manual sync failed")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Sync failed: {e!s}") from e
    else:
        return result


@router.get("/status")
async def sync_status():
    """Get sync status information."""
    return {
        "last_sync": stripe_sync_service._last_sync,
        "sync_interval_minutes": stripe_sync_service._sync_interval.total_seconds() / 60,
    }
