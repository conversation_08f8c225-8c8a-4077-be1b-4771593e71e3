import numpy as np
from celery.result import AsyncResult
from fastapi import APIRouter, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.memory.crud import MemoryCategoryRepo, MemoryRepo
from app.modules.memory.models import MemoryCategory, MemoryHistory
from app.modules.memory.schema import (
    IMemorizeRequest,
    IMemorizeResponse,
    IMemoryDeleteRequest,
    IMemoryDeleteResponse,
    IRetrieveDefaultCategoriesRequest,
    IRetrieveDefaultCategoriesResponse,
    IRetrieveRelatedClusteredCategoriesRequest,
    IRetrieveRelatedClusteredCategoriesResponse,
    IRetrieveRelatedMemoryItemsRequest,
    IRetrieveRelatedMemoryItemsResponse,
)
from app.modules.memory.task.crud import MemoryTaskRepo
from app.modules.memory.task.model import MemoryTask
from app.modules.memory.task.schema import IMemoryDiffEnhanced, ITaskStatusResponseFull, ITaskStatusResponseSDK
from app.modules.organization.project.crud import ProjectRepo
from app.modules.organization.project.memory_category.crud import ProjectMemCategoryRepo
from app.modules.organization.subscription.crud import organization_subscription
from app.modules.organization.subscription.usage_events.crud import usage_event
from app.modules.organization.subscription.usage_events.model import UsageEventType
from app.modules.organization.subscription.usage_events.schema import IUsageEventCreate
from app.modules.permission.deps import need_api_key, need_memorize_calls
from app.services.db.deps import DatabaseSessionDep
from app.services.db.session import SessionFactory
from app.services.task_queue import celery_app
from app.tasks.memory.llm.llm_factory import get_llm_client
from config.loader import SettingsFactory

CAN_ACCESS = need_api_key()
CAN_MEMORIZE = need_memorize_calls()

router = APIRouter()

# Global variable to store OpenAI client
_openai_client = None


def get_openai_client():
    """Get or create OpenAI client instance"""
    global _openai_client
    if _openai_client is None:
        try:
            settings = SettingsFactory.settings()
            if not settings.AZURE_API_KEY:
                raise HTTPException(status_code=500, detail="AZURE_API_KEY not configured in environment variables")
            # _openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            _openai_client = get_llm_client(task="embedding")
        except ImportError:
            raise HTTPException(
                status_code=500, detail="OpenAI library is not installed. Please install it with: pip install openai"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to initialize OpenAI client: {e!s}")
    return _openai_client


async def generate_embedding(text: str) -> tuple[list[float], int]:
    """
    Generate embedding for text using OpenAI text-embedding-3-small model.
    """
    try:
        client = get_openai_client()

        # Create embedding using OpenAI
        response = client.generate_embedding(text)

        # Extract embedding vector
        embedding = response.embedding
        token_used = response.usage.get("total_tokens", 0)

        return embedding, token_used

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate embedding: {e!s}")


@router.post("/memorize")
async def memorize_conversation(
    *,
    request: IMemorizeRequest,
    project_api_key=CAN_MEMORIZE,
    db_session: DatabaseSessionDep,
) -> IMemorizeResponse:
    """
    Start a Celery task to memorize conversation text with agent processing.

    Args:
        request: Memorize request containing conversation text and participant info
        current_identity: Current authenticated user identity

    Returns:
        IMemorizeResponse: Task ID and status for tracking the memorization process
    """
    try:
        (project_id, api_key_id) = project_api_key

        # Get project to find organization_id
        project = await ProjectRepo.get(model_id=project_id, db_session=db_session)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get active subscription for the organization
        active_subscription = await organization_subscription.get_active_by_org(
            org_id=project.organization_id, db_session=db_session
        )

        # Track usage event if subscription exists
        if active_subscription:
            usage_event_data = IUsageEventCreate(
                organization_id=project.organization_id,
                subscription_id=active_subscription.id,
                project_id=project_id,
                api_key_id=api_key_id,
                event_type=UsageEventType.MEMORIZE_CALL,
                feature_name="memorize",
                quantity=1,
                data={"user_id": request.user_id, "agent_id": request.agent_id},
            )
            await usage_event.create(obj_in=usage_event_data, session=db_session)

        if request.conversation:
            if request.conversation_text:
                raise HTTPException(
                    status_code=400, detail="conversation_text and conversation cannot be provided together"
                )
            else:
                conversation = {
                    "formatted": True,
                    "messages": [message.model_dump() for message in request.conversation],
                }
        else:
            if not request.conversation_text:
                raise HTTPException(status_code=400, detail="conversation_text or conversation must be provided")
            else:
                conversation = {"formatted": False, "text": request.conversation_text}

        # Import the task here to avoid circular imports
        from app.tasks.memorize import memorize_conversation as memorize_task

        # Create task record in database first
        new_task = await MemoryTaskRepo.new_task_detached(
            status="PENDING",
            db_session=db_session,
        )
        await db_session.commit()

        # Start the Celery task with the specific task ID
        task = memorize_task.apply_async(
            args=[
                conversation,
                request.user_id,
                request.user_name,
                request.agent_id,
                request.agent_name,
                request.session_date,
            ],
            kwargs={
                "api_key_id": api_key_id,
                "project_id": project_id,
            },
            task_id=new_task.id,
        )

        return IMemorizeResponse(
            task_id=new_task.id,
            status="PENDING",
            message=f"Memorization task registered for user {request.user_name} and agent {request.agent_name}",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start memorization task: {e!s}")


@router.get("/memorize/status/{task_id}")
async def get_memorize_status_db_lite(
    *,
    task_id: str,
    _=CAN_ACCESS,
    db_session: DatabaseSessionDep,
):
    """
    Get the status of a memorization task.
    """
    try:
        task = await MemoryTaskRepo.get_by_task_id(task_id=task_id, db_session=db_session)

        if not task:
            raise HTTPException(status_code=500, detail=f"Task id {task_id} not found")

        return ITaskStatusResponseSDK(
            task_id=task.id,
            status=task.status,
            detail_info=task.detail_info or "",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {e!s}")


@router.get("/memorize/status/{task_id}/verbose")
async def get_memorize_status_db_full(
    *,
    task_id: str,
    _=CAN_ACCESS,
    db_session: DatabaseSessionDep,
):
    """
    Get the status of a memorization task.
    """
    try:
        # Use query with selectinload to preload conversation relationship
        query = select(MemoryTask).where(MemoryTask.id == task_id).options(selectinload(MemoryTask.conversation))
        result = await db_session.execute(query)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=500, detail=f"Task id {task_id} not found")

        _finished = task.status == "SUCCESS"
        if _finished:
            query = (
                select(MemoryHistory)
                .where(MemoryHistory.conversation_id == task.conversation_id)
                .order_by(MemoryHistory.created_at)
            )

            result = await db_session.execute(query)
            memory_history = result.scalars().all()

            memory_diffs = [
                IMemoryDiffEnhanced(
                    memory_id=memory_history_item.memory_id,
                    action=memory_history_item.action,
                    category=memory_history_item.category,
                    content_before=memory_history_item.content_before,
                    content_after=memory_history_item.content_after,
                    links_before=memory_history_item.links_before,
                    links_after=memory_history_item.links_after,
                )
                for memory_history_item in memory_history
            ]
        else:
            memory_diffs = None

        task_response = ITaskStatusResponseFull(
            task_id=task.id,
            status=task.status,
            detail_info=task.detail_info,
            token_used=task.token_used,
            created_at=task.created_at,
            updated_at=task.updated_at,
            finished_at=task.finished_at,
            conversation_id=task.conversation_id,
            user_id=task.conversation.user_id if task.conversation else None,
            user_name=task.conversation.user_name if task.conversation else None,
            agent_id=task.conversation.agent_id if task.conversation else None,
            agent_name=task.conversation.agent_name if task.conversation else None,
            diff=memory_diffs,
        )

        return task_response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {e!s}")


# Wu: [task id / cleary task_id] mismatch problem not fully solved yet
@router.get("/memorize/status/{task_id}/celery")
async def get_memorize_status_celery(
    *,
    task_id: str,
    _=CAN_ACCESS,
):
    """
    Get the status of a memorization task.

    Args:
        task_id: The Celery task ID
        current_identity: Current authenticated user identity

    Returns:
        dict: Task status, progress, and results
    """
    try:
        task = AsyncResult(task_id, app=celery_app)

        if task.status == "PENDING":
            return {
                "task_id": task_id,
                "status": "PENDING",
                "ready": False,
                "result": None,
                "message": "Task is queued and waiting to be processed",
            }
        elif task.status == "PROCESSING":
            return {
                "task_id": task_id,
                "status": "PROCESSING",
                "ready": False,
                "result": task.result,  # Contains progress info
                "message": "Task is currently being processed",
            }
        elif task.status == "SUCCESS":
            return {
                "task_id": task_id,
                "status": "SUCCESS",
                "ready": True,
                "result": task.result,
                "message": "Memorization completed successfully",
            }
        elif task.status == "FAILURE":
            return {
                "task_id": task_id,
                "status": "FAILURE",
                "ready": True,
                "result": None,
                "error": str(task.result),
                "traceback": task.traceback,
                "message": "Memorization task failed",
            }
        else:
            return {
                "task_id": task_id,
                "status": task.status,
                "ready": task.ready(),
                "result": task.result if task.ready() else None,
                "message": f"Task status: {task.status}",
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {e!s}")


@router.post("/delete")
async def delete_memories(
    *,
    request: IMemoryDeleteRequest,
    project_api_key=CAN_ACCESS,
    db_session: DatabaseSessionDep,
):
    """
    Delete memories for a given user. If agent_id is provided, delete only that agent's memories; otherwise delete all memories for the user within the project.
    """
    try:
        (project_id, api_key_id) = project_api_key

        deleted = await MemoryRepo.delete_by_user_and_agent(
            user_id=request.user_id,
            agent_id=request.agent_id,
            project_id=project_id,
            db_session=db_session,
        )

        category_deleted = await MemoryCategoryRepo.delete_by_user_and_agent(
            user_id=request.user_id,
            agent_id=request.agent_id,
            project_id=project_id,
            db_session=db_session,
        )

        return IMemoryDeleteResponse(
            success=True,
            deleted_count=deleted,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete memories: {e!s}")


# ========================================
# Retrieve APIs
# ========================================


@router.post("/retrieve/default-categories")
async def retrieve_default_categories(
    *,
    request: IRetrieveDefaultCategoriesRequest,
    project_api_key=CAN_ACCESS,
) -> IRetrieveDefaultCategoriesResponse:
    """
    Retrieve default categories and their content for a project.

    Args:
        request: Request containing project_id and options
        current_identity: Current authenticated user identity

    Returns:
        IRetrieveDefaultCategoriesResponse: Default categories with their content
    """
    try:
        (project_id, api_key_id) = project_api_key
        want_summary = request.want_summary

        async with SessionFactory.make_local_session() as session:
            # Get default categories for the project
            default_categories = await ProjectMemCategoryRepo.get_active_categories(
                project_id=project_id, include_inactive=request.include_inactive, db_session=session
            )

            # For each category, get all memories in that category
            categories_with_content = []
            for proj_category in default_categories:
                # Get memories for this category (we'll search across all users in the project)
                # category_memories = await MemoryRepo.get_by_category(
                if not request.agent_id:
                    category_all_agents = await MemoryCategoryRepo.get_by_user_name_all_agent(
                        user_id=request.user_id,
                        name=proj_category.name,
                        project_id=project_id,
                        db_session=session,
                    )
                    agent_ids = [cat.agent_id for cat in category_all_agents]
                    agent_category_map = {cat.agent_id: cat for cat in category_all_agents}
                else:
                    agent_ids = [request.agent_id]
                    agent_category_map = {}
                config_summary_length = proj_category.config.get("summary_length", -1) if proj_category.config else -1

                for agent_id in agent_ids:
                    memory_result = await get_memory_or_summary(
                        want_summary=want_summary,
                        want_summary_length=config_summary_length,
                        user_id=request.user_id,
                        agent_id=agent_id,
                        category_data=agent_category_map.get(agent_id, None),
                        category_name=proj_category.name,
                        project_id=project_id,
                        db_session=session,
                    )

                    categories_with_content.append({
                        "name": proj_category.name,
                        "type": proj_category.type,
                        "user_id": request.user_id,
                        "agent_id": agent_id,
                        "description": proj_category.description or "",
                        "is_active": proj_category.is_active,
                        **memory_result,
                    })

            return IRetrieveDefaultCategoriesResponse(
                categories=categories_with_content,
                total_categories=len(categories_with_content),
                # project_id=project_id,
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve default categories: {e!s}")


@router.post("/retrieve/related-memory-items")
async def retrieve_related_memory_items(
    *,
    request: IRetrieveRelatedMemoryItemsRequest,
    project_api_key=CAN_ACCESS,
) -> IRetrieveRelatedMemoryItemsResponse:
    """
    Retrieve related memory items from activities using embedding search.

    Args:
        request: Request containing user_id, query, and search parameters
        current_identity: Current authenticated user identity

    Returns:
        IRetrieveRelatedMemoryItemsResponse: Related memory items with similarity scores
    """
    try:
        (project_id, api_key_id) = project_api_key
        total_token_used = 0

        async with SessionFactory.make_local_session() as session:
            # Generate embedding for the query
            query_embedding, token_used = await generate_embedding(request.query)
            total_token_used += token_used

            # Search for related memories in activities category
            # search_categories = request.include_categories or ["activity"]
            if request.include_categories:
                search_categories = request.include_categories
            else:
                default_categories = await ProjectMemCategoryRepo.get_active_categories(
                    project_id=project_id, db_session=session
                )
                search_categories = [cat.name for cat in default_categories]

            related_memories = await MemoryRepo.similarity_search(
                query_embedding=query_embedding,
                user_id=request.user_id,
                agent_id=request.agent_id,
                project_id=project_id,
                categories=search_categories,
                similarity_threshold=request.min_similarity,
                limit=request.top_k,
                db_session=session,
            )

            # Convert to response format
            memory_results = []
            for mem, similarity in related_memories:
                memory_results.append({
                    "memory": {
                        "memory_id": mem.memory_id,
                        "category": mem.category,
                        "content": mem.content,
                        "happened_at": mem.happened_at,
                        "created_at": mem.created_at,
                        "updated_at": mem.updated_at,
                    },
                    "user_id": mem.user_id,
                    "agent_id": mem.agent_id,
                    "similarity_score": similarity,
                })

            return IRetrieveRelatedMemoryItemsResponse(
                related_memories=memory_results,
                query=request.query,
                total_found=len(memory_results),
                search_params={
                    "top_k": request.top_k,
                    "min_similarity": request.min_similarity,
                    "categories": search_categories,
                },
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve related memory items: {e!s}")


@router.post("/retrieve/related-clustered-categories")
async def retrieve_related_clustered_categories(
    *,
    request: IRetrieveRelatedClusteredCategoriesRequest,
    project_api_key=CAN_ACCESS,
) -> IRetrieveRelatedClusteredCategoriesResponse:
    """
    Retrieve related clustered categories using semantic search on category names.

    Args:
        request: Request containing user_id, category query, and search parameters
        current_identity: Current authenticated user identity

    Returns:
        IRetrieveRelatedClusteredCategoriesResponse: Related categories with all their content
    """
    try:
        (project_id, api_key_id) = project_api_key
        total_token_used = 0
        want_summary = True

        async with SessionFactory.make_local_session() as session:
            # Generate embedding for the category query
            category_query_embedding, token_used = await generate_embedding(request.category_query)
            total_token_used += token_used

            categories = await MemoryCategoryRepo.get_by_user_agent_group(
                user_id=request.user_id,
                agent_id=request.agent_id,
                project_id=project_id,
                group="cluster",
                db_session=session,
            )

            # Calculate similarity between query and each category name
            category_similarities = []
            for category in categories:
                if category.name:
                    try:
                        category_embedding, token_used = await generate_embedding(category.name)
                        total_token_used += token_used

                        # Calculate cosine similarity
                        similarity = np.dot(category_query_embedding, category_embedding) / (
                            np.linalg.norm(category_query_embedding) * np.linalg.norm(category_embedding)
                        )

                        if similarity >= request.min_similarity:
                            category_similarities.append({
                                "name": category.name,
                                "category_item": category,
                                "similarity": float(similarity),
                            })
                    except Exception:
                        # Skip categories that fail embedding generation
                        continue

            # Sort by similarity and take top_k
            category_similarities.sort(key=lambda x: x["similarity"], reverse=True)
            top_categories = category_similarities[: request.top_k]

            # Get all memories for each top category
            clustered_categories = []
            for cat_info in top_categories:
                category_item = cat_info["category_item"]

                memory_result = await get_memory_or_summary(
                    want_summary=True,
                    # need get project config if we also include non-cluster (custom) categories
                    want_summary_length=-1,
                    user_id=category_item.user_id,
                    agent_id=category_item.agent_id,
                    category_data=category_item,
                    category_name=category_item.name,
                    project_id=project_id,
                    db_session=session,
                )

                clustered_categories.append({
                    "name": category_item.name,
                    "user_id": category_item.user_id,
                    "agent_id": category_item.agent_id,
                    "similarity_score": cat_info["similarity"],
                    **memory_result,
                })

            return IRetrieveRelatedClusteredCategoriesResponse(
                clustered_categories=clustered_categories,
                category_query=request.category_query,
                total_categories_found=len(clustered_categories),
                search_params={"top_k": request.top_k, "min_similarity": request.min_similarity},
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve related clustered categories: {e!s}")


async def get_memory_or_summary(
    *,
    want_summary: bool,
    want_summary_length: int = 1000,
    user_id: str,
    agent_id: str | None = None,
    category_data: MemoryCategory | None = None,
    category_name: str,
    project_id: str,
    db_session: AsyncSession,
) -> str | None:
    if want_summary:
        if category_data is None:
            category_data = await MemoryCategoryRepo.get_by_user_agent_name(
                user_id=user_id,
                agent_id=agent_id,
                name=category_name,
                project_id=project_id,
                db_session=db_session,
            )
        if category_data is not None:
            summaries = category_data.summary
            if summaries is not None:
                summary = get_best_summary(summaries, want_summary_length)
                if summary is not None:
                    return {
                        "summary": summary,
                    }

    # fallback to raw memory items
    category_memories = await MemoryRepo.get_by_category_top(
        user_id=user_id,
        agent_id=agent_id,
        project_id=project_id,
        category=category_name,
        db_session=db_session,
        top_n=100,
        order="desc",
        order_by="updated_at",
    )
    category_memories = category_memories[::-1]

    return {
        "memories": [
            {
                "memory_id": mem.memory_id,
                "category": mem.category,
                "content": mem.content,
                "happened_at": mem.happened_at,
                "created_at": mem.created_at,
                "updated_at": mem.updated_at,
            }
            for mem in category_memories
        ],
        "memory_count": len(category_memories),
    }


def get_best_summary(summaries: dict, want_summary_length: int) -> str:
    if len(summaries) == 0:
        return None

    if want_summary_length > 0:
        best_summary = max(
            summaries.values(),
            key=lambda item: (
                -abs(item["metadata"]["target_length"] - want_summary_length),
                item["timestamp"],
            ),
        )
    else:
        best_summary = max(summaries.values(), key=lambda item: item["timestamp"])

    return best_summary["summary"]
