import asyncio
from datetime import datetime

from app.modules.service_incident.crud import service_incident
from app.modules.service_incident.model import IncidentStatus, ServiceIncident, Services, ServiceStatus
from app.modules.service_incident.schema import IUpdateServiceIncident
from app.services.db.session import AsyncSession

RETRY_COUNT = 3


async def do_health_check(service_name: str) -> ServiceStatus:
    # 模拟健康检查
    pass
    # if random.random() < 0.5:
    #     return ServiceStatus.online
    # else:
    #     return ServiceStatus.offline


async def health_check(service_name: str) -> ServiceStatus:
    for _ in range(RETRY_COUNT):
        if await do_health_check(service_name):
            return ServiceStatus.online
        await asyncio.sleep(1)

    return ServiceStatus.offline


async def health_check_task(session: AsyncSession):
    """单次健康检查任务, 每分钟执行一次"""
    for service in Services:
        service_name = service.value
        active_incident = service_incident.get_by_service_name_and_status(
            service_name=service_name, status=IncidentStatus.ACTIVE.int_value
        )
        previous_status = active_incident is None
        current_status = await health_check(service_name)
        # 两次状态相同
        if previous_status == current_status.value:
            continue
        await update_service_status(
            service_name=service_name,
            previous_status=previous_status,
            current_status=current_status.value,
            current_incident_id=active_incident.id if active_incident else None,
            session=session,
        )


async def update_service_status(
    service_name: str, previous_status: int, current_status: int, current_incident_id: str, session: AsyncSession
):
    if previous_status == current_status:
        return
    """更新服务状态"""
    try:
        # 开启事务 状态更新 结束上个incidents 并更新service状态为online
        if previous_status == ServiceStatus.offline.value:
            """offline -> online"""
            incidents_update = IUpdateServiceIncident(
                id=current_incident_id, status=IncidentStatus.RESOLVED.int_value, end_time=str(datetime.now())
            )
            # 结束上个incidents
            await service_incident.update(update_id=current_incident_id, update_data=incidents_update, session=session)
        else:
            """online -> offline"""
            incidents_create = ServiceIncident(
                service_name=service_name,
                status=IncidentStatus.ACTIVE.int_value,
                incidents_start_time=str(datetime.now()),
                date=datetime.now().strftime("%Y-%m-%d"),
            )
            # 创建新的incidents
            await service_incident.insert(incidents=incidents_create, session=session)
        await session.commit()
    except Exception:
        await session.rollback()
        raise
