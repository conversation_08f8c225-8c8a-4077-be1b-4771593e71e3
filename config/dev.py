from pathlib import Path

from pydantic_settings import SettingsConfigDict

from .base import BaseAppSettings, Environment


class DevSettings(BaseAppSettings):
    MODE: Environment = Environment.DEV
    FIREBASE_ADMIN_SDK: str = Path("config/secrets/firebase-admin.json").read_text(encoding="utf-8")
    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=".env.dev",
    )


settings = DevSettings()
