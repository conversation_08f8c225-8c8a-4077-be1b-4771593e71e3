import os

from .base import BaseAppSettings, Environment


class SettingsFactory:
    _settings_instance = None

    @classmethod
    def settings(cls) -> BaseAppSettings:
        if cls._settings_instance is None:
            APP_ENV = Environment(os.getenv("ENV", Environment.DEV.value).lower())

            if Environment.DEV == APP_ENV:
                from dotenv import load_dotenv

                load_dotenv("config/.env.dev")
                from .dev import settings as _settings_instance
            elif Environment.TEST == APP_ENV:
                from .test import settings as _settings_instance
            elif Environment.PROD == APP_ENV:
                from .prod import settings as _settings_instance
            # return _settings_instance

            cls._settings_instance = _settings_instance
        return cls._settings_instance

    @classmethod
    def reset(cls):
        cls._settings_instance = None
