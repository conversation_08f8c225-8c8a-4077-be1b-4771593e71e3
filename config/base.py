import os
from enum import Enum
from typing import Any

from pydantic import AnyHttpUrl, EmailStr, PostgresDsn, field_validator
from pydantic_core.core_schema import ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    DEV = "dev"
    PROD = "prod"
    TEST = "test"


class BaseAppSettings(BaseSettings):
    MODE: Environment
    API_VERSION: str = "v1"
    API_V1_STR: str = f"/api/{API_VERSION}"
    PROJECT_NAME: str = "Hippocampus Cloud"
    HOST_URL: str = "https://app-preview.memu.so"

    DATABASE_USER: str = os.getenv("DATABASE_USER", "postgres")
    DATABASE_PASSWORD: str = os.getenv("DATABASE_PASSWORD", "postgres")
    DATABASE_HOST: str = os.getenv("DATABASE_HOST", "localhost")
    DATABASE_PORT: int = os.getenv("DATABASE_PORT", 5432)
    DATABASE_NAME: str = os.getenv("DATABASE_NAME", "cloud_dev")
    DATABASE_URI: PostgresDsn | str = ""

    DB_POOL_SIZE: int = 80
    WEB_CONCURRENCY: int = 10
    POOL_SIZE: int = max(DB_POOL_SIZE // WEB_CONCURRENCY, 5)

    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/1")

    SUPERUSER_EMAIL: EmailStr = "<EMAIL>"
    SUPERUSER_PASSWORD: str = os.getenv("SUPERUSER_PASSWORD", "Test12345")

    # Stripe configuration
    STRIPE_SECRET_KEY: str = os.getenv("STRIPE_SECRET_KEY", "stripe_secret_key")
    STRIPE_PUBLISHABLE_KEY: str = os.getenv("STRIPE_PUBLISHABLE_KEY", "stripe_publishable_key")
    STRIPE_WEBHOOK_ENDPOINT_SECRET: str = os.getenv("STRIPE_WEBHOOK_ENDPOINT_SECRET", "")
    STRIPE_SYNC_ON_STARTUP: bool = False
    STRIPE_SYNC_ON_API_CALLS: bool = False
    STRIPE_SYNC_INTERVAL_MINUTES: int = 5

    # OpenAI Configuration
    OPENAI_API_KEY: str = ""
    OPENAI_PRODUCT: str = os.getenv("OPENAI_PRODUCT", "gpt-4.1-mini-2")

    # Database logging configuration
    ENABLE_SQL_LOGS: bool = os.getenv("ENABLE_SQL_LOGS", True)

    AZURE_ENDPOINT: str = os.getenv("AZURE_ENDPOINT", "https://api.azure.com")

    AZURE_API_KEY: str = os.getenv("AZURE_API_KEY", "azure_api_key")

    FIREBASE_ADMIN_SDK: str = os.getenv("FIREBASE_ADMIN_SDK", "{}")
    DEEPSEEK_ENDPOINT: str = os.getenv("DEEPSEEK_ENDPOINT", "https://api.deepseek.com")

    MEMORIZE_TOKEN_PRICE: int = 8000

    MAX_MESSAGE_WORDS: int = 1000
    MEMORIZE_BATCH_WORDS: int = 6000

    DEFAULT_SUMMARY_LENGTH: int = 1000

    # "all" / "error": when client request error / "none"
    LLM_DEBUG_INFO_LEVEL: str = "all"

    @field_validator("DATABASE_URI", mode="after")
    def assemble_db_connection(cls, v: str | None, info: ValidationInfo) -> Any:
        if isinstance(v, str) and v == "":
            print("Assembling database connection string from environment variables")
            print(f"HOST={info.data['DATABASE_HOST']}")
            return PostgresDsn.build(
                scheme="postgresql+psycopg",
                username=info.data["DATABASE_USER"],
                password=info.data["DATABASE_PASSWORD"],
                host=info.data["DATABASE_HOST"],
                port=info.data["DATABASE_PORT"],
                path=info.data["DATABASE_NAME"],
            )
        return v

    BACKEND_CORS_ORIGINS: list[str] | list[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str] | str:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list | str):
            return v
        raise ValueError(v)

    model_config = SettingsConfigDict(case_sensitive=True)
