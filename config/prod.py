import json
import os

import boto3
from pydantic_settings import SettingsConfigDict

from .base import BaseAppSettings, Environment


class ProdSettings(BaseAppSettings):
    MODE: Environment = Environment.PROD

    model_config = SettingsConfigDict(case_sensitive=True)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_aws_config()
        self._rebuild_database_uri()

    def _load_aws_config(self):
        """Load configuration from AWS Secrets Manager and SSM Parameter Store"""
        try:
            # Region mapping from AWS region to abbreviated format
            REGION_MAP = {"us-east-1": "use1", "ap-northeast-1": "apn1"}

            # Initialize AWS clients for the current region
            region = os.getenv("AWS_REGION", "us-east-1")
            project_name = os.getenv("PROJECT_NAME", "memu-api")

            # Get the abbreviated region code
            region_abbr = REGION_MAP.get(region, region)

            print(f"Loading AWS config for region: {region} ({region_abbr}), project: {project_name}")

            # Use local region clients - parameters are replicated in each region
            ssm_client = boto3.client("ssm", region_name=region)
            secrets_client = boto3.client("secretsmanager", region_name=region)

            # Load region-specific app config from SSM Parameter Store
            try:
                config_param = ssm_client.get_parameter(Name=f"/{project_name}/prod/{region_abbr}/app_config")
                config = json.loads(config_param["Parameter"]["Value"])
                print(f"SSM CONFIG: {config}")

                # Database configuration
                self.DATABASE_HOST = config.get("database_host")
                self.DATABASE_NAME = config.get("database_name")
                self.DATABASE_USER = config.get("database_user")

                # API Endpoints
                self.AZURE_ENDPOINT = config.get("azure_endpoint")
                self.DEEPSEEK_ENDPOINT = config.get("deepseek_endpoint")

                # Celery configuration
                self.CELERY_BROKER_URL = config.get("celery_broker_url")
                self.CELERY_RESULT_BACKEND = config.get("celery_result_backend")
            except Exception as e:
                print(f"Warning: Could not load app config from SSM: {e}")

            # Load app secrets from Secrets Manager (region-specific path)
            try:
                app_secrets_response = secrets_client.get_secret_value(
                    SecretId=f"{project_name}/prod/{region_abbr}/app_secrets"
                )
                app_secrets = json.loads(app_secrets_response["SecretString"])

                self.OPENAI_API_KEY = app_secrets.get("openai_api_key")
                self.AZURE_API_KEY = app_secrets.get("azure_api_key")
                self.DATABASE_PASSWORD = app_secrets.get("database_password")
                self.STRIPE_SECRET_KEY = app_secrets.get("stripe_secret_key")
                self.STRIPE_PUBLISHABLE_KEY = app_secrets.get("stripe_publishable_key")
            except Exception as e:
                print(f"Warning: Could not load app secrets from Secrets Manager: {e}")

            # Load Firebase Admin SDK credentials from Secrets Manager (region-specific path)
            try:
                firebase_response = secrets_client.get_secret_value(
                    SecretId=f"{project_name}/prod/{region_abbr}/firebase_admin_sdk"
                )
                print(f"Loaded Firebase Admin SDK from Secrets Manager, {firebase_response['SecretString']}")
                self.FIREBASE_ADMIN_SDK = firebase_response["SecretString"]
            except Exception as e:
                print(f"Warning: Could not load Firebase Admin SDK from Secrets Manager: {e}")

            # Override with environment variables if set (useful for ECS task definitions)
            # This allows region-specific overrides from ECS environment variables
            if os.getenv("CELERY_BROKER_URL"):
                self.CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")

        except Exception as e:
            print(f"Error initializing AWS clients or loading config: {e}")
            # In case of AWS client initialization failure, settings will use defaults or env vars

    def _rebuild_database_uri(self):
        """Rebuild DATABASE_URI after AWS config is loaded"""
        from pydantic import PostgresDsn

        if self.DATABASE_HOST and self.DATABASE_USER and self.DATABASE_PASSWORD and self.DATABASE_NAME:
            self.DATABASE_URI = PostgresDsn.build(
                scheme="postgresql+psycopg",
                username=self.DATABASE_USER,
                password=self.DATABASE_PASSWORD,
                host=self.DATABASE_HOST,
                port=self.DATABASE_PORT,
                path=self.DATABASE_NAME,
            )


settings = ProdSettings()
