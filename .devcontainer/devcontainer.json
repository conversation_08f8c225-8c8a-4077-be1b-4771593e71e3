// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
    "name": "hippocampus-cloud",
    // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
    "image": "mcr.microsoft.com/devcontainers/python:1-3.11-bullseye",
    "runArgs": [
        // avoid UID/GID remapping under rootless Podman
        "--userns=keep-id"
      ],
    "features": {},

    // Use 'postCreateCommand' to run commands after the container is created.
    "postCreateCommand": "./.devcontainer/postCreateCommand.sh",

    // Configure tool-specific properties.
    "customizations": {
        "vscode": {
            "extensions": ["ms-python.python", "editorconfig.editorconfig"],
            "settings": {
                "python.testing.pytestArgs": ["tests"],
                "python.testing.unittestEnabled": false,
                "python.testing.pytestEnabled": true,
                "python.defaultInterpreterPath": "/workspaces/hippocampus-cloud/.venv/bin/python",
                "python.testing.pytestPath": "/workspaces/hippocampus-cloud/.venv/bin/pytest"
            }
        }
    }
}
