# Celery Task Queue Setup Guide

This guide explains how to set up and run the Celery task queue system for asynchronous task processing.

## Prerequisites

- Python environment with project dependencies installed
- Docker (for Redis)

## 1. Start Redis Docker Container

Redis serves as both the message broker and result backend for Celery.

```bash
# Start Redis container
docker run -d --name redis-server -p 6379:6379 redis:latest

# Or using docker-compose if you have one
docker-compose up -d redis
```

The default configuration uses:
- Broker URL: `redis://localhost:6379/0`
- Result Backend: `redis://localhost:6379/1`

## 2. Run Celery Worker

Start the Celery worker to process tasks in the background:

```bash
# From the project root directory
celery -A app.services.task_queue.celery_app worker --loglevel=info
```

You should see output indicating the worker is ready:
```
[2024-01-01 12:00:00,000: INFO/MainProcess] Connected to redis://localhost:6379/0
[2024-01-01 12:00:00,000: INFO/MainProcess] mingle: searching for neighbor nodes
[2024-01-01 12:00:00,000: INFO/MainProcess] celery@hostname ready.
```

## 3. Create and Monitor Tasks

### Start a Task

Use the API endpoint to create a new task:

```bash
# Example: Add two numbers (3 + 4)
curl -X GET "http://localhost:8000/api/v1/memory/task/create/3/4"
```

Response:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "submitted"
}
```

### Get Task Result

Use the task ID to check the result:

```bash
# Replace {task_id} with the actual task ID from step 3
curl -X GET "http://localhost:8000/api/v1/memory/task/{task_id}"
```

Possible responses:

**Task Pending:**
```json
{
  "detail": "任务未找到或尚未入队"
}
```

**Task Completed:**
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "SUCCESS",
  "ready": true,
  "result": 7,
  "traceback": null
}
```

**Task Failed:**
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "FAILURE",
  "ready": true,
  "result": null,
  "traceback": "Traceback details..."
}
```

## Task States

- `PENDING`: Task is waiting to be processed
- `SUCCESS`: Task completed successfully
- `FAILURE`: Task failed with an error
- `RETRY`: Task is being retried
- `REVOKED`: Task was cancelled

## Configuration

The Celery configuration is defined in `config/base.py`:

```python
CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/1")
```

You can override these using environment variables:

```bash
export CELERY_BROKER_URL="redis://your-redis-host:6379/0"
export CELERY_RESULT_BACKEND="redis://your-redis-host:6379/1"
```

## Available Tasks

Currently available tasks are defined in `app/tasks/example.py`:

- `tasks.add`: Adds two integers

To add new tasks, create them in the `app/tasks/` directory and decorate with `@celery_app.task`.

## Troubleshooting

1. **Worker not connecting to Redis**: Check Redis is running and accessible
2. **Tasks stuck in PENDING**: Ensure worker is running and consuming from the correct queue
3. **Import errors**: Make sure all task modules are properly imported
4. **Connection refused**: Verify Redis host and port configuration
